@charset "UTF-8";
/**
 * uni-app内置的常用样式变量
 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-list {
  display: flex;
  background-color: #ffffff;
  position: relative;
  flex-direction: column;
}
.uni-list--border {
  position: relative;
  z-index: -1;
}
.uni-list--border-top {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: #e5e5e5;
  z-index: 1;
}
.uni-list--border-bottom {
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  height: 1px;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: #e5e5e5;
}
