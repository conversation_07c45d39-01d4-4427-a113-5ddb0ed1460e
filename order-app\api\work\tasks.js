import request from '@/utils/request'

// 查询生产任务库列表
export function listTasks(query) {
  return request({
    url: '/work/tasks/list',
    method: 'get',
    params: query
  })
}

// 查询生产任务库详细
export function getTasks(taskId) {
  return request({
    url: '/work/tasks/' + taskId,
    method: 'get'
  })
}

// 新增生产任务库
export function addTasks(data) {
  return request({
    url: '/work/tasks',
    method: 'post',
    data: data
  })
}

// 修改生产任务库
export function updateTasks(data) {
  return request({
    url: '/work/tasks',
    method: 'put',
    data: data
  })
}

// 删除生产任务库
export function delTasks(taskId) {
  return request({
    url: '/work/tasks/' + taskId,
    method: 'delete'
  })
}
