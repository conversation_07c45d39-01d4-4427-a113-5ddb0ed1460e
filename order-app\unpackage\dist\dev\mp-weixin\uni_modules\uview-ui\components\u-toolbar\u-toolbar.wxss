@charset "UTF-8";
/**
 * uni-app内置的常用样式变量
 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
view.data-v-55c89db1, scroll-view.data-v-55c89db1, swiper-item.data-v-55c89db1 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-toolbar.data-v-55c89db1 {
  height: 42px;

  display: flex;

  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.u-toolbar__wrapper__cancel.data-v-55c89db1 {
  color: #909193;
  font-size: 15px;
  padding: 0 15px;
}
.u-toolbar__title.data-v-55c89db1 {
  color: #303133;
  padding: 0 60rpx;
  font-size: 16px;
  flex: 1;
  text-align: center;
}
.u-toolbar__wrapper__confirm.data-v-55c89db1 {
  color: #3c9cff;
  font-size: 15px;
  padding: 0 15px;
}
