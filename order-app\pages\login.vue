<template>
  <view class="business-login-container">
    <view class="header-banner">
      <view class="banner-content">
        <view class="system-name">工单管理系统</view>
        <view class="welcome-text">高效处理，智能管理</view>
      </view>
      <view class="logo-container">
        <image class="logo-image" :src="globalConfig.appInfo.logo" mode="widthFix"></image>
      </view>
    </view>
    
    <view class="login-form-content">
      <view class="form-title">账号登录</view>
      
      <view class="input-item flex align-center">
        <view class="iconfont icon-user icon"></view>
        <input v-model="loginForm.username" class="input" type="text" placeholder="请输入账号" maxlength="30" />
      </view>
      
      <view class="input-item flex align-center">
        <view class="iconfont icon-password icon"></view>
        <input v-model="loginForm.password" password class="input" placeholder="请输入密码" maxlength="20" />
      </view>
      
      <view class="captcha-container flex" v-if="captchaEnabled">
        <view class="input-item captcha-input flex align-center">
          <view class="iconfont icon-code icon"></view>
          <input v-model="loginForm.code" type="number" class="input" placeholder="请输入验证码" maxlength="4" />
        </view>
        <view class="login-code" @click="getCode"> 
          <image :src="codeUrl" class="login-code-img"></image>
        </view>
      </view>
      
      <view class="action-btn">
        <button @click="handleLogin" class="login-btn">登录系统</button>
      </view>
      
      <!-- <view class="divider">
        <text class="divider-text">或</text>
      </view>
      
      <view class="wechat-login-btn">
        <button type="default" class="wechat-btn" open-type="getPhoneNumber" @getphonenumber="WeChatLogin">
          <text class="iconfont icon-wechat"></text>
          <text>手机号快捷登录</text>
        </button>
      </view> -->
      
      <view class="reg text-center" v-if="register">
        <text class="text-grey1">没有账号？</text>
        <text @click="handleUserRegister" class="text-blue">立即注册</text>
      </view>
    </view>
    
    <view class="footer">
      <text class="footer-text">© 2023 工单管理系统 版权所有</text>
    </view>
  </view>
</template>

<script>
  import { getCodeImg } from '@/api/login'

  export default {
    data() {
      return {
        codeUrl: "",
        captchaEnabled: true,
        // 用户注册开关
        register: true,
        globalConfig: getApp().globalData.config,
        loginForm: {
          username: "",
          password: "",
          code: "",
          uuid: ''
        }
      }
    },
    created() {
      this.getCode()
    },
    methods: {
      // 用户注册
      handleUserRegister() {
        this.$tab.redirectTo(`/pages/register`)
      },
      // 隐私协议
      handlePrivacy() {
        let site = this.globalConfig.appInfo.agreements[0]
        this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
      },
      // 用户协议
      handleUserAgrement() {
        let site = this.globalConfig.appInfo.agreements[1]
        this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
      },
      // 获取图形验证码
      getCode() {
        getCodeImg().then(res => {
          this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
          if (this.captchaEnabled) {
            this.codeUrl = 'data:image/gif;base64,' + res.img
            this.loginForm.uuid = res.uuid
          }
        })
      },
	   WeChatLogin(e){
	  	this.$modal.loading("登录中，请耐心等待...")
	  	let params = {}
	  	params.code = e.detail.code
	  	uni.getUserInfo({
	  		success: (res) => {
	  			params.avatarUrl = res.userInfo.avatarUrl
	  			params.nickName = res.userInfo.nickName
	  		},
	  		complete: async () => {
	  			this.$store.dispatch('WxLogin', params).then(res=>{
	  				this.loginSuccess()
	  			})
	  		}
	  	})
	  },
      // 登录方法
      async handleLogin() {
        if (this.loginForm.username === "") {
          this.$modal.msgError("请输入您的账号")
        } else if (this.loginForm.password === "") {
          this.$modal.msgError("请输入您的密码")
        } else if (this.loginForm.code === "" && this.captchaEnabled) {
          this.$modal.msgError("请输入验证码")
        } else {
          this.$modal.loading("登录中，请耐心等待...")
          this.pwdLogin()
        }
      },
      // 密码登录
      async pwdLogin() {
        this.$store.dispatch('Login', this.loginForm).then(() => {
          this.$modal.closeLoading()
          this.loginSuccess()
        }).catch(() => {
          if (this.captchaEnabled) {
            this.getCode()
          }
        })
      },
      // 登录成功后，处理函数
      loginSuccess(result) {
        // 设置用户信息
        this.$store.dispatch('GetInfo').then(res => {
          this.$tab.reLaunch('/pages/index')
        })
      }
    }
  }
</script>

<style lang="scss">
  page {
    background-color: #f8f9fa;
  }

  .business-login-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    
    .header-banner {
      width: 100%;
      height: 280rpx;
      background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
      position: relative;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      
      &:before {
        content: '';
        position: absolute;
        top: -10%;
        right: -10%;
        width: 300rpx;
        height: 300rpx;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
      }
      
      &:after {
        content: '';
        position: absolute;
        bottom: -15%;
        left: -5%;
        width: 250rpx;
        height: 250rpx;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.08);
      }
      
      .banner-content {
        z-index: 2;
        text-align: center;
        padding: 0 40rpx;
        
        .system-name {
          font-size: 48rpx;
          font-weight: 600;
          color: #ffffff;
          letter-spacing: 2rpx;
          margin-bottom: 20rpx;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .welcome-text {
          font-size: 28rpx;
          color: rgba(255, 255, 255, 0.9);
          letter-spacing: 4rpx;
        }
      }
      
      .logo-container {
        position: absolute;
        top: 30rpx;
        left: 30rpx;
        z-index: 3;
        
        .logo-image {
          width: 80rpx;
          height: 80rpx;
          border-radius: 8rpx;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
      }
    }

    .login-form-content {
      width: 85%;
      margin: 40rpx auto;
      padding: 40rpx;
      background-color: #ffffff;
      border-radius: 8rpx;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      
      .form-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #2c3e50;
        text-align: left;
        margin-bottom: 40rpx;
        padding-bottom: 20rpx;
        border-bottom: 1px solid #ebeef5;
      }

      .input-item {
        margin: 30rpx auto;
        height: 90rpx;
        border: 1px solid #dcdfe6;
        border-radius: 4rpx;
        background-color: #ffffff;
        
        .icon {
          font-size: 40rpx;
          margin-left: 20rpx;
          color: #606266;
        }

        .input {
          width: 100%;
          font-size: 28rpx;
          line-height: 90rpx;
          text-align: left;
          padding-left: 20rpx;
          color: #2c3e50;
        }
      }
      
      .captcha-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 30rpx auto;
        
        .captcha-input {
          width: 60%;
          margin: 0;
        }
        
        .login-code {
          width: 35%;
          height: 90rpx;
          border: 1px solid #dcdfe6;
          border-radius: 4rpx;
          overflow: hidden;
          
          .login-code-img {
            width: 100%;
            height: 100%;
          }
        }
      }

      .action-btn {
        margin-top: 50rpx;
        
        .login-btn {
          height: 90rpx;
          line-height: 90rpx;
          background-color: #1e3a8a;
          color: #ffffff;
          font-size: 32rpx;
          border-radius: 4rpx;
          font-weight: 500;
          letter-spacing: 2rpx;
        }
      }
      
      .divider {
        position: relative;
        text-align: center;
        margin: 40rpx 0;
        height: 20rpx;
        
        &:before {
          content: '';
          position: absolute;
          top: 50%;
          left: 0;
          right: 0;
          height: 1px;
          background-color: #ebeef5;
          z-index: 1;
        }
        
        .divider-text {
          position: relative;
          display: inline-block;
          padding: 0 20rpx;
          background-color: #ffffff;
          color: #909399;
          font-size: 24rpx;
          z-index: 2;
        }
      }
      
      .wechat-login-btn {
        margin: 30rpx 0;
        
        .wechat-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 90rpx;
          line-height: 90rpx;
          background-color: #ffffff;
          color: #07c160;
          font-size: 32rpx;
          border: 1px solid #07c160;
          border-radius: 4rpx;
          
          .icon-wechat {
            margin-right: 10rpx;
            font-size: 36rpx;
          }
        }
      }
      
      .reg {
        margin-top: 30rpx;
        font-size: 26rpx;
        
        .text-grey1 {
          color: #606266;
        }
        
        .text-blue {
          color: #1e3a8a;
          margin-left: 10rpx;
        }
      }
    }
    
    .footer {
      margin-top: auto;
      padding: 40rpx 0;
      text-align: center;
      
      .footer-text {
        font-size: 24rpx;
        color: #909399;
      }
    }
  }
</style>