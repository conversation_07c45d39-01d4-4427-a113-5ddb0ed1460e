{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/index.vue?630c", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/index.vue?f99e", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/index.vue?ba57", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/index.vue?92f5", "uni-app:///pages/mine/index.vue", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/index.vue?73e8", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/index.vue?ee54"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "name", "version", "computed", "avatar", "windowHeight", "methods", "handleToInfo", "handleToEditInfo", "handleToSetting", "handleToLogin", "handleToAvatar", "handleLogout", "handleHelp", "handleAbout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgoB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC+EppB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9HA;AAAA;AAAA;AAAA;AAAmsC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAvtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4bd6864f&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4bd6864f&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"mine-container\" :style=\"{height: `${windowHeight}px`}\">\r\n    <!--顶部个人信息栏-->\r\n    <view class=\"header-section\">\r\n      <view class=\"flex padding justify-between\">\r\n        <view class=\"flex align-center\">\r\n          <view v-if=\"!avatar\" class=\"cu-avatar xl round bg-white\">\r\n            <view class=\"iconfont icon-people text-gray icon\"></view>\r\n          </view>\r\n          <image v-if=\"avatar\" @click=\"handleToAvatar\" :src=\"avatar\" class=\"cu-avatar xl round\" mode=\"widthFix\">\r\n          </image>\r\n          <view v-if=\"!name\" @click=\"handleToLogin\" class=\"login-tip\">\r\n            点击登录\r\n          </view>\r\n          <view v-if=\"name\" @click=\"handleToInfo\" class=\"user-info\">\r\n            <view class=\"u_title\">\r\n              用户名：{{ name }}\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view @click=\"handleToInfo\" class=\"flex align-center\">\r\n          <text>个人信息</text>\r\n          <view class=\"iconfont icon-right\"></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"content-section\">\r\n      <!-- <view class=\"mine-actions grid col-4 text-center\">\r\n        <view class=\"action-item\" @click=\"handleJiaoLiuQun\">\r\n          <view class=\"iconfont icon-friendfill text-pink icon\"></view>\r\n          <text class=\"text\">交流群</text>\r\n        </view>\r\n        <view class=\"action-item\" @click=\"handleBuilding\">\r\n          <view class=\"iconfont icon-service text-blue icon\"></view>\r\n          <text class=\"text\">在线客服</text>\r\n        </view>\r\n        <view class=\"action-item\" @click=\"handleBuilding\">\r\n          <view class=\"iconfont icon-community text-mauve icon\"></view>\r\n          <text class=\"text\">反馈社区</text>\r\n        </view>\r\n        <view class=\"action-item\" @click=\"handleBuilding\">\r\n          <view class=\"iconfont icon-dianzan text-green icon\"></view>\r\n          <text class=\"text\">点赞我们</text>\r\n        </view>\r\n      </view> -->\r\n\r\n      <view class=\"menu-list\">\r\n        <view class=\"list-cell list-cell-arrow\" @click=\"handleToEditInfo\">\r\n          <view class=\"menu-item-box\">\r\n            <view class=\"iconfont icon-user menu-icon\"></view>\r\n            <view>编辑资料</view>\r\n          </view>\r\n        </view>\r\n        <!-- <view class=\"list-cell list-cell-arrow\" @click=\"handleHelp\">\r\n          <view class=\"menu-item-box\">\r\n            <view class=\"iconfont icon-help menu-icon\"></view>\r\n            <view>常见问题</view>\r\n          </view>\r\n        </view>\r\n        <view class=\"list-cell list-cell-arrow\" @click=\"handleAbout\">\r\n          <view class=\"menu-item-box\">\r\n            <view class=\"iconfont icon-aixin menu-icon\"></view>\r\n            <view>关于我们</view>\r\n          </view>\r\n        </view> -->\r\n        <view class=\"list-cell list-cell-arrow\" @click=\"handleLogout\">\r\n          <view class=\"menu-item-box\">\r\n            <view class=\"iconfont icon-setting menu-icon\"></view>\r\n            <view>退出登录</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import storage from '@/utils/storage'\r\n  \r\n  export default {\r\n    data() {\r\n      return {\r\n        name: this.$store.state.user.name,\r\n        version: getApp().globalData.config.appInfo.version\r\n      }\r\n    },\r\n    computed: {\r\n      avatar() {\r\n        return this.$store.state.user.avatar\r\n      },\r\n      windowHeight() {\r\n        return uni.getSystemInfoSync().windowHeight - 50\r\n      }\r\n    },\r\n    methods: {\r\n      handleToInfo() {\r\n        this.$tab.navigateTo('/pages/mine/info/index')\r\n      },\r\n      handleToEditInfo() {\r\n        this.$tab.navigateTo('/pages/mine/info/edit')\r\n      },\r\n      handleToSetting() {\r\n        this.$tab.navigateTo('/pages/mine/setting/index')\r\n      },\r\n      handleToLogin() {\r\n        this.$tab.reLaunch('/pages/login')\r\n      },\r\n      handleToAvatar() {\r\n        this.$tab.navigateTo('/pages/mine/avatar/index')\r\n      },\r\n      handleLogout() {\r\n        this.$modal.confirm('确定注销并退出系统吗？').then(() => {\r\n          this.$store.dispatch('LogOut').then(() => {\r\n            this.$tab.reLaunch('/pages/index')\r\n          })\r\n        })\r\n      },\r\n      handleHelp() {\r\n        this.$tab.navigateTo('/pages/mine/help/index')\r\n      },\r\n      handleAbout() {\r\n        this.$tab.navigateTo('/pages/mine/about/index')\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n  page {\r\n    background-color: #f5f6f7;\r\n  }\r\n\r\n  .mine-container {\r\n    width: 100%;\r\n    height: 100%;\r\n\r\n\r\n    .header-section {\r\n      padding: 15px 15px 45px 15px;\r\n      background-color: #3c96f3;\r\n      color: white;\r\n\r\n      .login-tip {\r\n        font-size: 18px;\r\n        margin-left: 10px;\r\n      }\r\n\r\n      .cu-avatar {\r\n        border: 2px solid #eaeaea;\r\n\r\n        .icon {\r\n          font-size: 40px;\r\n        }\r\n      }\r\n\r\n      .user-info {\r\n        margin-left: 15px;\r\n\r\n        .u_title {\r\n          font-size: 18px;\r\n          line-height: 30px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .content-section {\r\n      position: relative;\r\n      top: -50px;\r\n\r\n      .mine-actions {\r\n        margin: 15px 15px;\r\n        padding: 20px 0px;\r\n        border-radius: 8px;\r\n        background-color: white;\r\n\r\n        .action-item {\r\n          .icon {\r\n            font-size: 28px;\r\n          }\r\n\r\n          .text {\r\n            display: block;\r\n            font-size: 13px;\r\n            margin: 8px 0px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752425864208\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}