@charset "UTF-8";
/**
 * uni-app内置的常用样式变量
 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
view.data-v-113bc24f, scroll-view.data-v-113bc24f, swiper-item.data-v-113bc24f {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-input.data-v-113bc24f {

  display: flex;

  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}
.u-input--radius.data-v-113bc24f, .u-input--square.data-v-113bc24f {
  border-radius: 4px;
}
.u-input--no-radius.data-v-113bc24f {
  border-radius: 0;
}
.u-input--circle.data-v-113bc24f {
  border-radius: 100px;
}
.u-input__content.data-v-113bc24f {
  flex: 1;

  display: flex;

  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.u-input__content__field-wrapper.data-v-113bc24f {
  position: relative;

  display: flex;

  flex-direction: row;
  margin: 0;
  flex: 1;
}
.u-input__content__field-wrapper__field.data-v-113bc24f {
  line-height: 26px;
  text-align: left;
  color: #303133;
  height: 24px;
  font-size: 15px;
  flex: 1;
}
.u-input__content__clear.data-v-113bc24f {
  width: 20px;
  height: 20px;
  border-radius: 100px;
  background-color: #c6c7cb;

  display: flex;

  flex-direction: row;
  align-items: center;
  justify-content: center;
  -webkit-transform: scale(0.82);
          transform: scale(0.82);
  margin-left: 4px;
}
.u-input__content__subfix-icon.data-v-113bc24f {
  margin-left: 4px;
}
.u-input__content__prefix-icon.data-v-113bc24f {
  margin-right: 4px;
}
