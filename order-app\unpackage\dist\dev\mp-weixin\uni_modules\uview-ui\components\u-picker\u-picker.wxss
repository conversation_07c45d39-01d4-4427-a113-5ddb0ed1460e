@charset "UTF-8";
/**
 * uni-app内置的常用样式变量
 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
view.data-v-f45a262e, scroll-view.data-v-f45a262e, swiper-item.data-v-f45a262e {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-picker.data-v-f45a262e {
  position: relative;
}
.u-picker__view__column.data-v-f45a262e {

  display: flex;

  flex-direction: row;
  flex: 1;
  justify-content: center;
}
.u-picker__view__column__item.data-v-f45a262e {

  display: flex;

  flex-direction: row;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  text-align: center;
  display: block;
  color: #303133;
}
.u-picker__view__column__item--disabled.data-v-f45a262e {
  cursor: not-allowed;
  opacity: 0.35;
}
.u-picker--loading.data-v-f45a262e {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;

  display: flex;

  flex-direction: row;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.87);
  z-index: 1000;
}
