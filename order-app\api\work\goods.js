import request from '@/utils/request'

// 查询产品信息列表
export function listGoods(query) {
  return request({
    url: '/work/goods/list',
    method: 'get',
    params: query
  })
}

// 查询产品信息详细
export function getGoods(id) {
  return request({
    url: '/work/goods/' + id,
    method: 'get'
  })
}

// 新增产品信息
export function addGoods(data) {
  return request({
    url: '/work/goods',
    method: 'post',
    data: data
  })
}

// 修改产品信息
export function updateGoods(data) {
  return request({
    url: '/work/goods',
    method: 'put',
    data: data
  })
}

// 删除产品信息
export function delGoods(id) {
  return request({
    url: '/work/goods/' + id,
    method: 'delete'
  })
}
