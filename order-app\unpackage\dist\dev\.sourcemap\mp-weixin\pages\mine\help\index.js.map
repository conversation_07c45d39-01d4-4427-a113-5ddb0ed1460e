{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/help/index.vue?872d", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/help/index.vue?e849", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/help/index.vue?7f39", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/help/index.vue?f0bc", "uni-app:///pages/mine/help/index.vue", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/help/index.vue?f822", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/help/index.vue?04c3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "list", "icon", "title", "childList", "content", "methods", "handleText"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC4K;AAC5K,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAA+oB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCkBnqB;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;UACAD;UACAE;QACA;UACAF;UACAE;QACA;UACAF;UACAE;QACA;UACAF;UACAE;QACA;MACA,GACA;QACAH;QACAC;QACAC;UACAD;UACAE;QACA;UACAF;UACAE;QACA;UACAF;UACAE;QACA;MACA;IAEA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAAsvC,CAAgB,snCAAG,EAAC,C;;;;;;;;;;;ACA1wC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/help/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/help/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=00369c57&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=00369c57&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"00369c57\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/help/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=00369c57&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.list, function (item, findex) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.childList.length\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"help-container\">\r\n    <view v-for=\"(item, findex) in list\" :key=\"findex\" :title=\"item.title\" class=\"list-title\">\r\n      <view class=\"text-title\">\r\n        <view :class=\"item.icon\"></view>{{ item.title }}\r\n      </view>\r\n      <view class=\"childList\">\r\n        <view v-for=\"(child, zindex) in item.childList\" :key=\"zindex\" class=\"question\" hover-class=\"hover\"\r\n          @click=\"handleText(child)\">\r\n          <view class=\"text-item\">{{ child.title }}</view>\r\n          <view class=\"line\" v-if=\"zindex !== item.childList.length - 1\"></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    data() {\r\n      return {\r\n        list: [{\r\n            icon: 'iconfont icon-github',\r\n            title: '若依问题',\r\n            childList: [{\r\n              title: '若依开源吗？',\r\n              content: '开源'\r\n            }, {\r\n              title: '若依可以商用吗？',\r\n              content: '可以'\r\n            }, {\r\n              title: '若依官网地址多少？',\r\n              content: 'http://ruoyi.vip'\r\n            }, {\r\n              title: '若依文档地址多少？',\r\n              content: 'http://doc.ruoyi.vip'\r\n            }]\r\n          },\r\n          {\r\n            icon: 'iconfont icon-help',\r\n            title: '其他问题',\r\n            childList: [{\r\n              title: '如何退出登录？',\r\n              content: '请点击[我的] - [应用设置] - [退出登录]即可退出登录',\r\n            }, {\r\n              title: '如何修改用户头像？',\r\n              content: '请点击[我的] - [选择头像] - [点击提交]即可更换用户头像',\r\n            }, {\r\n              title: '如何修改登录密码？',\r\n              content: '请点击[我的] - [应用设置] - [修改密码]即可修改登录密码',\r\n            }]\r\n          }\r\n        ]\r\n      }\r\n    },\r\n    methods: {\r\n      handleText(item) {\r\n        this.$tab.navigateTo(`/pages/common/textview/index?title=${item.title}&content=${item.content}`)\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  page {\r\n    background-color: #f8f8f8;\r\n  }\r\n\r\n  .help-container {\r\n    margin-bottom: 100rpx;\r\n    padding: 30rpx;\r\n  }\r\n\r\n  .list-title {\r\n    margin-bottom: 30rpx;\r\n  }\r\n\r\n  .childList {\r\n    background: #ffffff;\r\n    box-shadow: 0px 0px 10rpx rgba(193, 193, 193, 0.2);\r\n    border-radius: 16rpx;\r\n    margin-top: 10rpx;\r\n  }\r\n\r\n  .line {\r\n    width: 100%;\r\n    height: 1rpx;\r\n    background-color: #F5F5F5;\r\n  }\r\n\r\n  .text-title {\r\n    color: #303133;\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    margin-left: 10rpx;\r\n\r\n    .iconfont {\r\n      font-size: 16px;\r\n      margin-right: 10rpx;\r\n    }\r\n  }\r\n\r\n  .text-item {\r\n    font-size: 28rpx;\r\n    padding: 24rpx;\r\n  }\r\n\r\n  .question {\r\n    color: #606266;\r\n    font-size: 28rpx;\r\n  }\r\n</style>\n", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=00369c57&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=00369c57&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752425864199\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}