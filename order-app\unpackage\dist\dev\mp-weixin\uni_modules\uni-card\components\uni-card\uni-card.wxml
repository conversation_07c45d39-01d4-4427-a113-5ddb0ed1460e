<view class="{{['uni-card',(isFull)?'uni-card--full':'',(isShadow)?'uni-card--shadow':'',(border)?'uni-card--border':'']}}" style="{{'margin:'+(isFull?0:margin)+';'+('padding:'+(spacing)+';')+('box-shadow:'+(isShadow?shadow:'')+';')}}"><block wx:if="{{$slots.cover}}"><slot name="cover"></slot></block><block wx:else><block wx:if="{{cover}}"><view class="uni-card__cover"><image class="uni-card__cover-image" mode="widthFix" src="{{cover}}" data-event-opts="{{[['tap',[['onClick',['cover']]]]]}}" bindtap="__e"></image></view></block></block><block wx:if="{{$slots.title}}"><slot name="title"></slot></block><block wx:else><block wx:if="{{title||extra}}"><view class="uni-card__header"><view data-event-opts="{{[['tap',[['onClick',['title']]]]]}}" class="uni-card__header-box" bindtap="__e"><block wx:if="{{thumbnail}}"><view class="uni-card__header-avatar"><image class="uni-card__header-avatar-image" src="{{thumbnail}}" mode="aspectFit"></image></view></block><view class="uni-card__header-content"><text class="uni-card__header-content-title uni-ellipsis">{{title}}</text><block wx:if="{{title&&subTitle}}"><text class="uni-card__header-content-subtitle uni-ellipsis">{{subTitle}}</text></block></view></view><view data-event-opts="{{[['tap',[['onClick',['extra']]]]]}}" class="uni-card__header-extra" bindtap="__e"><block wx:if="{{$slots.extra}}"><slot name="extra"></slot></block><block wx:else><text class="uni-card__header-extra-text">{{extra}}</text></block></view></view></block></block><view data-event-opts="{{[['tap',[['onClick',['content']]]]]}}" class="uni-card__content" style="{{'padding:'+(padding)+';'}}" bindtap="__e"><slot></slot></view><view data-event-opts="{{[['tap',[['onClick',['actions']]]]]}}" class="uni-card__actions" bindtap="__e"><slot name="actions"></slot></view></view>