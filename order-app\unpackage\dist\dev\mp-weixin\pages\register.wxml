<view class="business-register-container"><view class="header-banner"><view class="banner-content"><view class="system-name">工单管理系统</view><view class="welcome-text">创建您的账号</view></view><view class="logo-container"><image class="logo-image" src="{{globalConfig.appInfo.logo}}" mode="widthFix"></image></view></view><view class="register-form-content"><view class="form-title">账号注册</view><view class="input-item flex align-center"><view class="iconfont icon-user icon"></view><input class="input" type="tel" placeholder="请输入手机号作为账号" maxlength="11" data-event-opts="{{[['input',[['__set_model',['$0','username','$event',[]],['registerForm']]]]]}}" value="{{registerForm.username}}" bindinput="__e"/></view><view class="input-item flex align-center"><view class="iconfont icon-password icon"></view><input class="input" type="password" placeholder="请输入密码" maxlength="20" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['registerForm']]]]]}}" value="{{registerForm.password}}" bindinput="__e"/></view><view class="input-item flex align-center"><view class="iconfont icon-password icon"></view><input class="input" type="password" placeholder="请输入确认密码" maxlength="20" data-event-opts="{{[['input',[['__set_model',['$0','confirmPassword','$event',[]],['registerForm']]]]]}}" value="{{registerForm.confirmPassword}}" bindinput="__e"/></view><block wx:if="{{captchaEnabled}}"><view class="captcha-container flex"><view class="input-item captcha-input flex align-center"><view class="iconfont icon-code icon"></view><input class="input" type="number" placeholder="请输入验证码" maxlength="4" data-event-opts="{{[['input',[['__set_model',['$0','code','$event',[]],['registerForm']]]]]}}" value="{{registerForm.code}}" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['getCode',['$event']]]]]}}" class="register-code" bindtap="__e"><image class="register-code-img" src="{{codeUrl}}"></image></view></view></block><view class="action-btn"><button data-event-opts="{{[['tap',[['handleRegister']]]]}}" class="register-btn" bindtap="__e">注册账号</button></view><view class="login-link text-center"><text class="text-grey1">已有账号？</text><text data-event-opts="{{[['tap',[['handleUserLogin',['$event']]]]]}}" class="text-blue" bindtap="__e">立即登录</text></view></view><view class="footer"><text class="footer-text">© 2023 工单管理系统 版权所有</text></view></view>