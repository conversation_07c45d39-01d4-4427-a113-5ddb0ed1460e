{"version": 3, "sources": ["webpack:///D:/项目/工单小程序/order-app/uni_modules/uview-ui/components/u-calendar/u-calendar.vue?a743", "webpack:///D:/项目/工单小程序/order-app/uni_modules/uview-ui/components/u-calendar/u-calendar.vue?4e49", "webpack:///D:/项目/工单小程序/order-app/uni_modules/uview-ui/components/u-calendar/u-calendar.vue?98b5", "webpack:///D:/项目/工单小程序/order-app/uni_modules/uview-ui/components/u-calendar/u-calendar.vue?16b1", "uni-app:///uni_modules/uview-ui/components/u-calendar/u-calendar.vue", "webpack:///D:/项目/工单小程序/order-app/uni_modules/uview-ui/components/u-calendar/u-calendar.vue?5090", "webpack:///D:/项目/工单小程序/order-app/uni_modules/uview-ui/components/u-calendar/u-calendar.vue?a6d1"], "names": ["name", "mixins", "components", "<PERSON><PERSON><PERSON><PERSON>", "uMonth", "data", "months", "monthIndex", "listHeight", "selected", "scrollIntoView", "scrollTop", "innerFormatter", "watch", "<PERSON><PERSON><PERSON><PERSON>", "immediate", "handler", "show", "computed", "innerMaxDate", "Number", "innerMinDate", "subtitle", "buttonDisabled", "mounted", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monthSelected", "init", "close", "confirm", "getMonths", "setMonth", "add", "valueOf", "i", "date", "fill", "map", "day", "format", "bottomInfo", "week", "disabled", "dot", "month", "year", "scrollIntoDefaultMonth", "onScroll", "updateMonthTop", "topArr"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AAC4K;AAC5K,gBAAgB,6KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AAAopB,CAAgB,onBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACoExqB;AACA;AACA;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AArCA,eAsCA;EACAA;EACAC;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACAC;MACAC;MACA;MACAC;QAAA;MAAA;IACA;EACA;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;EACA;EACAE;IACA;IACAC;MACA,0CACAC,uBACA;IACA;IACAC;MACA,0CACAD,uBACA;IACA;IACA;IACAN;MACA;IACA;IACAQ;MACA;MACA;QACA,qEACA;MAEA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACA,IACA,4BACA,0BACA,oDACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA,IACA,qBACA,qBACA,+EACA;QACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA,cACA,qBACA,6BACAC,gCACAC;MACA;MACA,0BACA,GACA,eACA,iCACA;MACA;MACA;MAAA,2BACAC;QACA;UACAC,gBACA,2DACA,CACAC,QACAC;YACA;YACA;YACA;YACA,wCACAL,gBACAG,UACAG;YACA,wCACAN,gBACAG,UACAI;YACA;YACA;cACA;cACA,0CACA,kCACA,uCACA,iCACA;cACAC;YACA;YACA;cACAF;cACAG;cACA;cACAC,UACA,mCACA,kDACA,IACA,kCACA,kDACA;cACA;cACAP;cACAK;cACAG;cACAC,OACA;YACA;YACA,gBACA;YACA;UACA;UACA;UACAA;UACA;UACAC;QACA;MAAA;MAtDA;QAAA;MAuDA;IAEA;IACA;IACAC;MACA;MACA,mDAGA;QAAA,IAFAD;UACAD;QAEAA;QACA;MACA;MACA;QAOA;MAEA;IACA;IACA;IACAG;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;MACA;MACAC;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;QACA;MACA;MACA;MACA;MACA;QACAzC;MACA;QACAA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpXA;AAAA;AAAA;AAAA;AAA2vC,CAAgB,2nCAAG,EAAC,C;;;;;;;;;;;ACA/wC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-calendar/u-calendar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-calendar.vue?vue&type=template&id=b73440ae&scoped=true&\"\nvar renderjs\nimport script from \"./u-calendar.vue?vue&type=script&lang=js&\"\nexport * from \"./u-calendar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-calendar.vue?vue&type=style&index=0&id=b73440ae&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b73440ae\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-calendar/u-calendar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-calendar.vue?vue&type=template&id=b73440ae&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.$u.addUnit(_vm.listHeight)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-calendar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-calendar.vue?vue&type=script&lang=js&\"", "<template>\n\t<u-popup\n\t\t:show=\"show\"\n\t\tmode=\"bottom\"\n\t\tcloseable\n\t\t@close=\"close\"\n\t\t:round=\"round\"\n\t\t:closeOnClickOverlay=\"closeOnClickOverlay\"\n\t>\n\t\t<view class=\"u-calendar\">\n\t\t\t<uHeader\n\t\t\t\t:title=\"title\"\n\t\t\t\t:subtitle=\"subtitle\"\n\t\t\t\t:showSubtitle=\"showSubtitle\"\n\t\t\t\t:showTitle=\"showTitle\"\n\t\t\t></uHeader>\n\t\t\t<scroll-view\n\t\t\t\t:style=\"{\n                    height: $u.addUnit(listHeight)\n                }\"\n\t\t\t\tscroll-y\n\t\t\t\t@scroll=\"onScroll\"\n\t\t\t\t:scroll-top=\"scrollTop\"\n\t\t\t\t:scrollIntoView=\"scrollIntoView\"\n\t\t\t>\n\t\t\t\t<uMonth\n\t\t\t\t\t:color=\"color\"\n\t\t\t\t\t:rowHeight=\"rowHeight\"\n\t\t\t\t\t:showMark=\"showMark\"\n\t\t\t\t\t:months=\"months\"\n\t\t\t\t\t:mode=\"mode\"\n\t\t\t\t\t:maxCount=\"maxCount\"\n\t\t\t\t\t:startText=\"startText\"\n\t\t\t\t\t:endText=\"endText\"\n\t\t\t\t\t:defaultDate=\"defaultDate\"\n\t\t\t\t\t:minDate=\"innerMinDate\"\n\t\t\t\t\t:maxDate=\"innerMaxDate\"\n\t\t\t\t\t:maxMonth=\"monthNum\"\n\t\t\t\t\t:readonly=\"readonly\"\n\t\t\t\t\t:maxRange=\"maxRange\"\n\t\t\t\t\t:rangePrompt=\"rangePrompt\"\n\t\t\t\t\t:showRangePrompt=\"showRangePrompt\"\n\t\t\t\t\t:allowSameDay=\"allowSameDay\"\n\t\t\t\t\tref=\"month\"\n\t\t\t\t\t@monthSelected=\"monthSelected\"\n\t\t\t\t\t@updateMonthTop=\"updateMonthTop\"\n\t\t\t\t></uMonth>\n\t\t\t</scroll-view>\n\t\t\t<slot name=\"footer\" v-if=\"showConfirm\">\n\t\t\t\t<view class=\"u-calendar__confirm\">\n\t\t\t\t\t<u-button\n\t\t\t\t\t\tshape=\"circle\"\n\t\t\t\t\t\t:text=\"\n                            buttonDisabled ? confirmDisabledText : confirmText\n                        \"\n\t\t\t\t\t\t:color=\"color\"\n\t\t\t\t\t\t@click=\"confirm\"\n\t\t\t\t\t\t:disabled=\"buttonDisabled\"\n\t\t\t\t\t></u-button>\n\t\t\t\t</view>\n\t\t\t</slot>\n\t\t</view>\n\t</u-popup>\n</template>\n\n<script>\nimport uHeader from './header.vue'\nimport uMonth from './month.vue'\nimport props from './props.js'\nimport util from './util.js'\nimport dayjs from '../../libs/util/dayjs.js'\nimport Calendar from '../../libs/util/calendar.js'\n/**\n * Calendar 日历\n * @description  此组件用于单个选择日期，范围选择日期等，日历被包裹在底部弹起的容器中.\n * @tutorial https://www.uviewui.com/components/calendar.html\n *\n * @property {String}\t\t\t\ttitle\t\t\t\t标题内容 (默认 日期选择 )\n * @property {Boolean}\t\t\t\tshowTitle\t\t\t是否显示标题  (默认 true )\n * @property {Boolean}\t\t\t\tshowSubtitle\t\t是否显示副标题\t(默认 true )\n * @property {String}\t\t\t\tmode\t\t\t\t日期类型选择  single-选择单个日期，multiple-可以选择多个日期，range-选择日期范围 （ 默认 'single' )\n * @property {String}\t\t\t\tstartText\t\t\tmode=range时，第一个日期底部的提示文字  (默认 '开始' )\n * @property {String}\t\t\t\tendText\t\t\t\tmode=range时，最后一个日期底部的提示文字 (默认 '结束' )\n * @property {Array}\t\t\t\tcustomList\t\t\t自定义列表\n * @property {String}\t\t\t\tcolor\t\t\t\t主题色，对底部按钮和选中日期有效  (默认 ‘#3c9cff' )\n * @property {String | Number}\t\tminDate\t\t\t\t最小的可选日期\t (默认 0 )\n * @property {String | Number}\t\tmaxDate\t\t\t\t最大可选日期  (默认 0 )\n * @property {Array | String| Date}\tdefaultDate\t\t\t默认选中的日期，mode为multiple或range是必须为数组格式\n * @property {String | Number}\t\tmaxCount\t\t\tmode=multiple时，最多可选多少个日期  (默认 \tNumber.MAX_SAFE_INTEGER  )\n * @property {String | Number}\t\trowHeight\t\t\t日期行高 (默认 56 )\n * @property {Function}\t\t\t\tformatter\t\t\t日期格式化函数\n * @property {Boolean}\t\t\t\tshowLunar\t\t\t是否显示农历  (默认 false )\n * @property {Boolean}\t\t\t\tshowMark\t\t\t是否显示月份背景色 (默认 true )\n * @property {String}\t\t\t\tconfirmText\t\t\t确定按钮的文字 (默认 '确定' )\n * @property {String}\t\t\t\tconfirmDisabledText\t确认按钮处于禁用状态时的文字 (默认 '确定' )\n * @property {Boolean}\t\t\t\tshow\t\t\t\t是否显示日历弹窗 (默认 false )\n * @property {Boolean}\t\t\t\tcloseOnClickOverlay\t是否允许点击遮罩关闭日历 (默认 false )\n * @property {Boolean}\t\t\t\treadonly\t        是否为只读状态，只读状态下禁止选择日期 (默认 false )\n * @property {String | Number}\t\tmaxRange\t        日期区间最多可选天数，默认无限制，mode = range时有效\n * @property {String}\t\t\t\trangePrompt\t        范围选择超过最多可选天数时的提示文案，mode = range时有效\n * @property {Boolean}\t\t\t\tshowRangePrompt\t    范围选择超过最多可选天数时，是否展示提示文案，mode = range时有效 (默认 true )\n * @property {Boolean}\t\t\t\tallowSameDay\t    是否允许日期范围的起止时间为同一天，mode = range时有效 (默认 false )\n * @property {Number|String}\t    round\t\t\t\t圆角值，默认无圆角  (默认 0 )\n * @property {Number|String}\t    monthNum\t\t\t最多展示的月份数量  (默认 3 )\n *\n * @event {Function()} confirm \t\t点击确定按钮时触发\t\t选择日期相关的返回参数\n * @event {Function()} close \t\t日历关闭时触发\t\t\t可定义页面关闭时的回调事件\n * @example <u-calendar  :defaultDate=\"defaultDateMultiple\" :show=\"show\" mode=\"multiple\" @confirm=\"confirm\">\n\t</u-calendar>\n * */\nexport default {\n\tname: 'u-calendar',\n\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n\tcomponents: {\n\t\tuHeader,\n\t\tuMonth\n\t},\n\tdata() {\n\t\treturn {\n\t\t\t// 需要显示的月份的数组\n\t\t\tmonths: [],\n\t\t\t// 在月份滚动区域中，当前视图中月份的index索引\n\t\t\tmonthIndex: 0,\n\t\t\t// 月份滚动区域的高度\n\t\t\tlistHeight: 0,\n\t\t\t// month组件中选择的日期数组\n\t\t\tselected: [],\n\t\t\tscrollIntoView: '',\n\t\t\tscrollTop:0,\n\t\t\t// 过滤处理方法\n\t\t\tinnerFormatter: (value) => value\n\t\t}\n\t},\n\twatch: {\n\t\tselectedChange: {\n\t\t\timmediate: true,\n\t\t\thandler(n) {\n\t\t\t\tthis.setMonth()\n\t\t\t}\n\t\t},\n\t\t// 打开弹窗时，设置月份数据\n\t\tshow: {\n\t\t\timmediate: true,\n\t\t\thandler(n) {\n\t\t\t\tthis.setMonth()\n\t\t\t}\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 由于maxDate和minDate可以为字符串(2021-10-10)，或者数值(时间戳)，但是dayjs如果接受字符串形式的时间戳会有问题，这里进行处理\n\t\tinnerMaxDate() {\n\t\t\treturn uni.$u.test.number(this.maxDate)\n\t\t\t\t? Number(this.maxDate)\n\t\t\t\t: this.maxDate\n\t\t},\n\t\tinnerMinDate() {\n\t\t\treturn uni.$u.test.number(this.minDate)\n\t\t\t\t? Number(this.minDate)\n\t\t\t\t: this.minDate\n\t\t},\n\t\t// 多个条件的变化，会引起选中日期的变化，这里统一管理监听\n\t\tselectedChange() {\n\t\t\treturn [this.innerMinDate, this.innerMaxDate, this.defaultDate]\n\t\t},\n\t\tsubtitle() {\n\t\t\t// 初始化时，this.months为空数组，所以需要特别判断处理\n\t\t\tif (this.months.length) {\n\t\t\t\treturn `${this.months[this.monthIndex].year}年${\n\t\t\t\t\tthis.months[this.monthIndex].month\n\t\t\t\t}月`\n\t\t\t} else {\n\t\t\t\treturn ''\n\t\t\t}\n\t\t},\n\t\tbuttonDisabled() {\n\t\t\t// 如果为range类型，且选择的日期个数不足1个时，让底部的按钮出于disabled状态\n\t\t\tif (this.mode === 'range') {\n\t\t\t\tif (this.selected.length <= 1) {\n\t\t\t\t\treturn true\n\t\t\t\t} else {\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\treturn false\n\t\t\t}\n\t\t}\n\t},\n\tmounted() {\n\t\tthis.start = Date.now()\n\t\tthis.init()\n\t},\n\tmethods: {\n\t\t// 在微信小程序中，不支持将函数当做props参数，故只能通过ref形式调用\n\t\tsetFormatter(e) {\n\t\t\tthis.innerFormatter = e\n\t\t},\n\t\t// month组件内部选择日期后，通过事件通知给父组件\n\t\tmonthSelected(e) {\n\t\t\tthis.selected = e\n\t\t\tif (!this.showConfirm) {\n\t\t\t\t// 在不需要确认按钮的情况下，如果为单选，或者范围多选且已选长度大于2，则直接进行返还\n\t\t\t\tif (\n\t\t\t\t\tthis.mode === 'multiple' ||\n\t\t\t\t\tthis.mode === 'single' ||\n\t\t\t\t\t(this.mode === 'range' && this.selected.length >= 2)\n\t\t\t\t) {\n\t\t\t\t\tthis.$emit('confirm', this.selected)\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tinit() {\n\t\t\t// 校验maxDate，不能小于minDate\n\t\t\tif (\n\t\t\t\tthis.innerMaxDate &&\n\t\t\t\tthis.innerMinDate &&\n\t\t\t\tnew Date(this.innerMaxDate).getTime() < new Date(this.innerMinDate).getTime()\n\t\t\t) {\n\t\t\t\treturn uni.$u.error('maxDate不能小于minDate')\n\t\t\t}\n\t\t\t// 滚动区域的高度\n\t\t\tthis.listHeight = this.rowHeight * 5 + 30\n\t\t\tthis.setMonth()\n\t\t},\n\t\tclose() {\n\t\t\tthis.$emit('close')\n\t\t},\n\t\t// 点击确定按钮\n\t\tconfirm() {\n\t\t\tif (!this.buttonDisabled) {\n\t\t\t\tthis.$emit('confirm', this.selected)\n\t\t\t}\n\t\t},\n\t\t// 获得两个日期之间的月份数\n\t\tgetMonths(minDate, maxDate) {\n\t\t\tconst minYear = dayjs(minDate).year()\n\t\t\tconst minMonth = dayjs(minDate).month() + 1\n\t\t\tconst maxYear = dayjs(maxDate).year()\n\t\t\tconst maxMonth = dayjs(maxDate).month() + 1\n\t\t\treturn (maxYear - minYear) * 12 + (maxMonth - minMonth) + 1\n\t\t},\n\t\t// 设置月份数据\n\t\tsetMonth() {\n\t\t\t// 最小日期的毫秒数\n\t\t\tconst minDate = this.innerMinDate || dayjs().valueOf()\n\t\t\t// 如果没有指定最大日期，则往后推3个月\n\t\t\tconst maxDate =\n\t\t\t\tthis.innerMaxDate ||\n\t\t\t\tdayjs(minDate)\n\t\t\t\t\t.add(this.monthNum - 1, 'month')\n\t\t\t\t\t.valueOf()\n\t\t\t// 最大最小月份之间的共有多少个月份，\n\t\t\tconst months = uni.$u.range(\n\t\t\t\t1,\n\t\t\t\tthis.monthNum,\n\t\t\t\tthis.getMonths(minDate, maxDate)\n\t\t\t)\n\t\t\t// 先清空数组\n\t\t\tthis.months = []\n\t\t\tfor (let i = 0; i < months; i++) {\n\t\t\t\tthis.months.push({\n\t\t\t\t\tdate: new Array(\n\t\t\t\t\t\tdayjs(minDate).add(i, 'month').daysInMonth()\n\t\t\t\t\t)\n\t\t\t\t\t\t.fill(1)\n\t\t\t\t\t\t.map((item, index) => {\n\t\t\t\t\t\t\t// 日期，取值1-31\n\t\t\t\t\t\t\tlet day = index + 1\n\t\t\t\t\t\t\t// 星期，0-6，0为周日\n\t\t\t\t\t\t\tconst week = dayjs(minDate)\n\t\t\t\t\t\t\t\t.add(i, 'month')\n\t\t\t\t\t\t\t\t.date(day)\n\t\t\t\t\t\t\t\t.day()\n\t\t\t\t\t\t\tconst date = dayjs(minDate)\n\t\t\t\t\t\t\t\t.add(i, 'month')\n\t\t\t\t\t\t\t\t.date(day)\n\t\t\t\t\t\t\t\t.format('YYYY-MM-DD')\n\t\t\t\t\t\t\tlet bottomInfo = ''\n\t\t\t\t\t\t\tif (this.showLunar) {\n\t\t\t\t\t\t\t\t// 将日期转为农历格式\n\t\t\t\t\t\t\t\tconst lunar = Calendar.solar2lunar(\n\t\t\t\t\t\t\t\t\tdayjs(date).year(),\n\t\t\t\t\t\t\t\t\tdayjs(date).month() + 1,\n\t\t\t\t\t\t\t\t\tdayjs(date).date()\n\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\tbottomInfo = lunar.IDayCn\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tlet config = {\n\t\t\t\t\t\t\t\tday,\n\t\t\t\t\t\t\t\tweek,\n\t\t\t\t\t\t\t\t// 小于最小允许的日期，或者大于最大的日期，则设置为disabled状态\n\t\t\t\t\t\t\t\tdisabled:\n\t\t\t\t\t\t\t\t\tdayjs(date).isBefore(\n\t\t\t\t\t\t\t\t\t\tdayjs(minDate).format('YYYY-MM-DD')\n\t\t\t\t\t\t\t\t\t) ||\n\t\t\t\t\t\t\t\t\tdayjs(date).isAfter(\n\t\t\t\t\t\t\t\t\t\tdayjs(maxDate).format('YYYY-MM-DD')\n\t\t\t\t\t\t\t\t\t),\n\t\t\t\t\t\t\t\t// 返回一个日期对象，供外部的formatter获取当前日期的年月日等信息，进行加工处理\n\t\t\t\t\t\t\t\tdate: new Date(date),\n\t\t\t\t\t\t\t\tbottomInfo,\n\t\t\t\t\t\t\t\tdot: false,\n\t\t\t\t\t\t\t\tmonth:\n\t\t\t\t\t\t\t\t\tdayjs(minDate).add(i, 'month').month() + 1\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tconst formatter =\n\t\t\t\t\t\t\t\tthis.formatter || this.innerFormatter\n\t\t\t\t\t\t\treturn formatter(config)\n\t\t\t\t\t\t}),\n\t\t\t\t\t// 当前所属的月份\n\t\t\t\t\tmonth: dayjs(minDate).add(i, 'month').month() + 1,\n\t\t\t\t\t// 当前年份\n\t\t\t\t\tyear: dayjs(minDate).add(i, 'month').year()\n\t\t\t\t})\n\t\t\t}\n\n\t\t},\n\t\t// 滚动到默认设置的月份\n\t\tscrollIntoDefaultMonth(selected) {\n\t\t\t// 查询默认日期在可选列表的下标\n\t\t\tconst _index = this.months.findIndex(({\n\t\t\t\t  year,\n\t\t\t\t  month\n\t\t\t  }) => {\n\t\t\t\tmonth = uni.$u.padZero(month)\n\t\t\t\treturn `${year}-${month}` === selected\n\t\t\t})\n\t\t\tif (_index !== -1) {\n\t\t\t\t// #ifndef MP-WEIXIN\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.scrollIntoView = `month-${_index}`\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\tthis.scrollTop = this.months[_index].top || 0;\n\t\t\t\t// #endif\n\t\t\t}\n\t\t},\n\t\t// scroll-view滚动监听\n\t\tonScroll(event) {\n\t\t\t// 不允许小于0的滚动值，如果scroll-view到顶了，继续下拉，会出现负数值\n\t\t\tconst scrollTop = Math.max(0, event.detail.scrollTop)\n\t\t\t// 将当前滚动条数值，除以滚动区域的高度，可以得出当前滚动到了哪一个月份的索引\n\t\t\tfor (let i = 0; i < this.months.length; i++) {\n\t\t\t\tif (scrollTop >= (this.months[i].top || this.listHeight)) {\n\t\t\t\t\tthis.monthIndex = i\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t// 更新月份的top值\n\t\tupdateMonthTop(topArr = []) {\n\t\t\t// 设置对应月份的top值，用于onScroll方法更新月份\n\t\t\ttopArr.map((item, index) => {\n\t\t\t\tthis.months[index].top = item\n\t\t\t})\n\n\t\t\t// 获取默认日期的下标\n\t\t\tif (!this.defaultDate) {\n\t\t\t\t// 如果没有设置默认日期，则将当天日期设置为默认选中的日期\n\t\t\t\tconst selected = dayjs().format(\"YYYY-MM\")\n\t\t\t\tthis.scrollIntoDefaultMonth(selected)\n\t\t\t\treturn\n\t\t\t}\n\t\t\tlet selected = dayjs().format(\"YYYY-MM\");\n\t\t\t// 单选模式，可以是字符串或数组，Date对象等\n\t\t\tif (!uni.$u.test.array(this.defaultDate)) {\n\t\t\t\tselected = dayjs(this.defaultDate).format(\"YYYY-MM\")\n\t\t\t} else {\n\t\t\t\tselected = dayjs(this.defaultDate[0]).format(\"YYYY-MM\");\n\t\t\t}\n\t\t\tthis.scrollIntoDefaultMonth(selected)\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '../../libs/css/components.scss';\n\n.u-calendar {\n\t&__confirm {\n\t\tpadding: 7px 18px;\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-calendar.vue?vue&type=style&index=0&id=b73440ae&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-calendar.vue?vue&type=style&index=0&id=b73440ae&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1747300420446\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}