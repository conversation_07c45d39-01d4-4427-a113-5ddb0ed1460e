{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/addOrder/addOrder.vue?9b80", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/addOrder/addOrder.vue?c608", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/addOrder/addOrder.vue?64ba", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/addOrder/addOrder.vue?b8ab", "uni-app:///pages/addOrder/addOrder.vue", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/addOrder/addOrder.vue?de76", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/addOrder/addOrder.vue?8be3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "baseUrl", "editMode", "workOrderId", "formData", "productId", "customerName", "modelType", "packingListImagePath", "totalPlannedQuantity", "overallPlannedStartTime", "overallPlannedEndTime", "remark", "status", "rules", "required", "message", "trigger", "tasks", "fileList", "productList", "taskList", "selectedTasks", "tempSelectedTasks", "selectedProduct", "showProductPickerFlag", "showTaskPickerFlag", "submitting", "isUploading", "uploadProgress", "computed", "selectedTasksText", "customBtn", "slot", "icon", "backgroundColor", "color", "size", "onLoad", "methods", "getOrderDetail", "res", "orderData", "product", "url", "fileName", "taskId", "taskName", "console", "uni", "title", "getProductList", "getTaskList", "item", "selected", "showProductPicker", "confirmProduct", "cancelProductPicker", "showTaskPicker", "toggleTaskSelection", "confirmTask", "cancelTaskPicker", "startDateChange", "endDateChange", "afterRead", "file", "mask", "progressTimer", "uploadPromises", "filePath", "name", "success", "resolve", "reject", "fail", "Promise", "uploadedFiles", "clearInterval", "map", "join", "setTimeout", "duration", "deletePic", "submitForm", "submitData", "workOrderItems", "productionTaskId", "plannedQuantity", "orderRes"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,gZAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1GA;AAAA;AAAA;AAAA;AAAmoB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC+KvpB;AACA;AACA;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;QACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;;MACAC;QACAT,YACA;UAAAU;UAAAC;UAAAC;QAAA,EACA;QACAC,QACA;UAAAH;UAAAC;UAAAC;QAAA,EACA;QACAR,uBACA;UAAAM;UAAAC;UAAAC;QAAA;MAEA;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QAAA;MAAA;IACA;IACAC;MACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IAAA;IACA;;IAEA;IACA;MACA;QACA;QACA;UACA;UACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACAC,sBAEA;kBACA;oBACArC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;kBACA;;kBAEA;kBACA;oBACA8B;sBAAA;oBAAA;oBACA;sBACA;oBACA;kBACA;;kBAEA;kBACA;oBACA;sBAAA;oBAAA;sBAAA;wBACAC;wBACA/B;wBACAG;wBACA6B;sBACA;oBAAA;kBACA;;kBAEA;kBACA;oBACA;sBACA;wBAAA;sBAAA;sBACA;wBAAAC;wBAAAC;sBAAA;oBACA;sBAAA;oBAAA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAC;kBACAC;kBACAhB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAV;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAO;gBACAC;kBACAC;kBACAhB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAkB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAX;gBACA;kBACA;oBAAA,uCACAY;sBACAC;oBAAA;kBAAA,CACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAN;gBACAC;kBACAC;kBACAhB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAqB;MACA;IACA;IAEA;IACAC;MACAR;MACA;MACA;MACA;MACAA;MACA;IACA;IAEA;IACAS;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QAAA;MAAA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC,mBAEA;gBACA;gBACA;;gBAEA;gBACAhB;kBACAC;kBACAgB;gBACA;gBAAA;gBAGA;gBACAC;kBACA;oBACA;kBACA;gBACA;gBAEAC;kBACA;oBACAnB;sBACAL;sBACAyB;sBACAC;sBACAC;wBACA;wBACA;0BACAC;wBACA;0BACAC;wBACA;sBACA;sBACAC;wBACAD;sBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA,OAEAE;cAAA;gBAAAC;gBAEA;gBACAC;gBACA;;gBAEA;gBACAD;kBACA;oBACAhC;oBACA/B;oBACAG;oBACA6B;kBACA;gBACA;;gBAEA;gBACA,uDACAiC;kBAAA;gBAAA,GACAC;;gBAEA;gBACAC;kBACA/B;kBACAA;oBACAC;oBACAhB;oBACA+C;kBACA;;kBAEA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBACA;gBACA;gBAEAhC;gBACAA;kBACAC;kBACAhB;kBACA+C;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;;MAEA;MACA,mDACAJ;QAAA;MAAA,GACAC;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAI;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAnC;kBACAC;kBACAhB;gBACA;gBAAA;cAAA;gBAGAc;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAhB;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAe;kBACAC;kBACAhB;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAGA;gBACAmD;kBAAA;oBACAC;oBACAC;kBACA;gBAAA,IAEA;gBACA7C;kBACArC;kBACAC;kBACAC;kBACAC;kBACA;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAwE;gBACA;gBAAA,KAGA;kBAAA;kBAAA;gBAAA;gBACA;gBACA3C;gBAAA;gBAAA,OACA;cAAA;gBAAA8C;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAA;cAAA;gBAAA,MAGAA;kBAAA;kBAAA;gBAAA;gBACAvC;kBACAC;kBACAhB;gBACA;;gBAEA;gBACA8C;kBACA/B;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAD;gBACAC;kBACAC;kBACAhB;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtlBA;AAAA;AAAA;AAAA;AAAssC,CAAgB,imCAAG,EAAC,C;;;;;;;;;;;ACA1tC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/addOrder/addOrder.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/addOrder/addOrder.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./addOrder.vue?vue&type=template&id=7a4ac926&\"\nvar renderjs\nimport script from \"./addOrder.vue?vue&type=script&lang=js&\"\nexport * from \"./addOrder.vue?vue&type=script&lang=js&\"\nimport style0 from \"./addOrder.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/addOrder/addOrder.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addOrder.vue?vue&type=template&id=7a4ac926&\"", "var components\ntry {\n  components = {\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form/u-form\" */ \"@/uni_modules/uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form-item/u-form-item\" */ \"@/uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-input/u-input\" */ \"@/uni_modules/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uUpload: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-upload/u-upload\" */ \"@/uni_modules/uview-ui/components/u-upload/u-upload.vue\"\n      )\n    },\n    uniDatetimePicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker\" */ \"@/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.vue\"\n      )\n    },\n    uTextarea: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-textarea/u-textarea\" */ \"@/uni_modules/uview-ui/components/u-textarea/u-textarea.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-picker/u-picker\" */ \"@/uni_modules/uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.taskList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = _vm.tempSelectedTasks.some(function (t) {\n      return t.taskId === item.taskId\n    })\n    var g1 = _vm.tempSelectedTasks.some(function (t) {\n      return t.taskId === item.taskId\n    })\n    var g2 = _vm.tempSelectedTasks.some(function (t) {\n      return t.taskId === item.taskId\n    })\n    return {\n      $orig: $orig,\n      g0: g0,\n      g1: g1,\n      g2: g2,\n    }\n  })\n  var g3 = _vm.tempSelectedTasks.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addOrder.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addOrder.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"add-order-container\">\n    <u-form :model=\"formData\" ref=\"uForm\" :error-type=\"['message']\">\n      <!-- 产品选择 -->\n      <u-form-item label=\"产品选择\" prop=\"productId\" required>\n        <view class=\"form-input-wrapper\" @click=\"showProductPicker\">\n          <text class=\"selected-text\" :class=\"{'placeholder': !selectedProduct}\">\n            {{ selectedProduct ? selectedProduct.name : '请选择产品' }}\n          </text>\n          <u-icon name=\"arrow-right\" color=\"#8c9fba\" size=\"32rpx\"></u-icon>\n        </view>\n      </u-form-item>\n      \n      <!-- 生产任务 -->\n      <u-form-item label=\"生产任务\" prop=\"tasks\" required>\n        <view class=\"form-input-wrapper\" @click=\"showTaskPicker\">\n          <text class=\"selected-text\" :class=\"{'placeholder': !selectedTasksText}\">\n            {{ selectedTasksText || '请选择生产任务' }}\n          </text>\n          <u-icon name=\"arrow-right\" color=\"#8c9fba\" size=\"32rpx\"></u-icon>\n        </view>\n      </u-form-item>\n      \n      <!-- 下单客户 -->\n      <u-form-item label=\"下单客户\" prop=\"customerName\">\n        <u-input v-model=\"formData.customerName\" placeholder=\"请输入客户名称\" />\n      </u-form-item>\n      \n      <!-- 型号 -->\n      <u-form-item label=\"型号\" prop=\"modelType\">\n        <u-input v-model=\"formData.modelType\" placeholder=\"请输入型号\" />\n      </u-form-item>\n      \n      <!-- 装箱单图片 -->\n      <u-form-item label=\"装箱单图片\" prop=\"packingListImagePath\">\n        <view class=\"upload-container\">\n          <u-upload\n            :file-list=\"fileList\"\n            @afterRead=\"afterRead\"\n            @delete=\"deletePic\"\n            name=\"packingListImagePath\"\n            multiple\n            :max-count=\"5\"\n            :custom-btn=\"customBtn\"\n            :disabled=\"isUploading\"\n          ></u-upload>\n          \n          <!-- 上传进度指示器 -->\n          <view class=\"upload-progress\" v-if=\"isUploading\">\n            <view class=\"progress-bar\">\n              <view class=\"progress-fill\" :style=\"{ width: uploadProgress + '%' }\"></view>\n            </view>\n            <text class=\"progress-text\">上传中 {{ uploadProgress }}%</text>\n          </view>\n        </view>\n      </u-form-item>\n      \n      <!-- 订单产品编号版本 -->\n      <!-- <u-form-item label=\"订单产品编号版本\" prop=\"orderProductNumberVersion\">\n        <u-textarea\n          v-model=\"formData.orderProductNumberVersion\"\n          placeholder=\"请输入订单产品编号版本\"\n          height=\"100\"\n          count\n          maxlength=\"500\"\n        />\n      </u-form-item> -->\n      \n      <!-- 计划数量 -->\n      <u-form-item label=\"计划数量\" prop=\"totalPlannedQuantity\" required>\n        <u-input\n          v-model=\"formData.totalPlannedQuantity\"\n          type=\"number\"\n          placeholder=\"请输入计划数量\"\n        />\n      </u-form-item>\n      \n      <!-- 计划开始时间 -->\n      <u-form-item label=\"计划开始时间\" prop=\"overallPlannedStartTime\">\n        <uni-datetime-picker\n          type=\"date\"\n          :value=\"formData.overallPlannedStartTime\"\n          @change=\"startDateChange\"\n          class=\"date-picker\"\n        />\n      </u-form-item>\n      \n      <!-- 计划结束时间 -->\n      <u-form-item label=\"计划结束时间\" prop=\"overallPlannedEndTime\">\n        <uni-datetime-picker\n          type=\"date\"\n          :value=\"formData.overallPlannedEndTime\"\n          @change=\"endDateChange\"\n          class=\"date-picker\"\n        />\n      </u-form-item>\n      \n      <!-- 备注 -->\n      <u-form-item label=\"备注\" prop=\"remark\">\n        <u-textarea\n          v-model=\"formData.remark\"\n          placeholder=\"请输入备注信息\"\n          height=\"100\"\n          count\n          maxlength=\"500\"\n        />\n      </u-form-item>\n    </u-form>\n    \n    <!-- 提交按钮 -->\n    <view class=\"submit-btn-wrapper\">\n      <u-button \n        type=\"primary\" \n        @click=\"submitForm\" \n        :loading=\"submitting\"\n        :disabled=\"isUploading || submitting\"\n      >\n        {{ isUploading ? '图片上传中...' : (submitting ? (editMode ? '修改中...' : '提交中...') : (editMode ? '修改工单' : '提交工单')) }}\n      </u-button>\n    </view>\n    \n    <!-- 产品选择弹窗 -->\n    <u-picker\n      :show=\"showProductPickerFlag\"\n      :columns=\"[productList]\"\n      @confirm=\"confirmProduct\"\n      @cancel=\"cancelProductPicker\"\n      keyName=\"name\"\n      value-key=\"id\"\n    ></u-picker>\n    \n    <!-- 任务选择弹窗 -->\n    <u-popup\n      mode=\"bottom\"\n      :show=\"showTaskPickerFlag\"\n      @close=\"cancelTaskPicker\"\n      border-radius=\"24\"\n      safe-area-inset-bottom\n    >\n      <view class=\"task-picker-container\">\n        <view class=\"task-picker-header\">\n          <text class=\"task-picker-title\">选择生产任务</text>\n          <view class=\"task-picker-actions\">\n            <u-button plain size=\"mini\" @click=\"cancelTaskPicker\" style=\"margin-right: 20rpx;\">取消</u-button>\n            <u-button type=\"primary\" size=\"mini\" @click=\"confirmTask\">确认</u-button>\n          </view>\n        </view>\n        <view class=\"task-list\">\n          <view\n            class=\"task-item\"\n            v-for=\"(item, index) in taskList\"\n            :key=\"index\"\n            @click=\"toggleTaskSelection(item)\"\n            :class=\"{'task-item-selected': tempSelectedTasks.some(t => t.taskId === item.taskId)}\"\n          >\n            <view class=\"task-info\">\n              <text class=\"task-name\">{{ item.taskName }}</text>\n              <text class=\"task-desc\">{{ item.taskDesc || '无描述' }}</text>\n            </view>\n            <u-icon\n              :name=\"tempSelectedTasks.some(t => t.taskId === item.taskId) ? 'checkmark-circle' : ''\"\n              :color=\"tempSelectedTasks.some(t => t.taskId === item.taskId) ? '#1e3a8a' : '#8c9fba'\"\n              size=\"40\"\n            ></u-icon>\n          </view>\n        </view>\n        <view class=\"task-picker-footer\">\n          <text class=\"selected-count\">已选择 {{ tempSelectedTasks.length }} 项任务</text>\n        </view>\n      </view>\n    </u-popup>\n  </view>\n</template>\n\n<script>\nimport { addOrders,updateOrders,getOrders, } from \"@/api/work/orders.js\"\nimport { listGoods } from \"@/api/work/goods.js\"\nimport { baseUrl } from \"../../config\"\nimport { listTasks } from \"@/api/work/tasks.js\"\n\nexport default {\n  data() {\n    return {\n      baseUrl,\n      editMode: false, // 编辑模式标识\n      workOrderId: null, // 工单ID\n      formData: {\n        productId: '',\n        customerName: '',\n        modelType: '',\n        packingListImagePath: '',\n        // orderProductNumberVersion: '',\n        totalPlannedQuantity: '',\n        overallPlannedStartTime: '',\n        overallPlannedEndTime: '',\n        remark: '',\n        status: '0' // 添加状态字段\n      },\n      rules: {\n        productId: [\n          { required: true, message: '请选择产品', trigger: 'change' }\n        ],\n        tasks: [\n          { required: true, message: '请选择生产任务', trigger: 'change' }\n        ],\n        totalPlannedQuantity: [\n          { required: true, message: '请输入计划数量', trigger: 'blur' }\n        ]\n      },\n      fileList: [],\n      productList: [],\n      taskList: [],\n      selectedTasks: [],\n      tempSelectedTasks: [],\n      selectedProduct: null,\n      showProductPickerFlag: false,\n      showTaskPickerFlag: false,\n      submitting: false,\n      isUploading: false,\n      uploadProgress: 0\n    }\n  },\n  computed: {\n    selectedTasksText() {\n      if (this.selectedTasks.length === 0) return '';\n      return this.selectedTasks.map(task => task.taskName).join('，');\n    },\n    customBtn() {\n      return {\n        slot: 'addBtn',\n        icon: 'camera-fill',\n        backgroundColor: '#f2f3f5',\n        color: '#8c9fba',\n        size: 140\n      };\n    }\n  },\n  onLoad(options) {\n    this.$refs.uForm.setRules(this.rules);\n    \n    // 先获取产品和任务列表，再检查是否为编辑模式\n    this.getProductList().then(() => {\n      this.getTaskList().then(() => {\n        // 检查是否为编辑模式\n        if (options.id) {\n          this.editMode = true;\n          this.workOrderId = options.id;\n          this.getOrderDetail(options.id);\n        }\n      });\n    });\n  },\n  methods: {\n    // 获取工单详情（编辑模式使用）\n    async getOrderDetail(workOrderId) {\n      try {\n        const res = await getOrders(workOrderId);\n        if (res && res.data) {\n          const orderData = res.data;\n          \n          // 填充表单数据\n          this.formData = {\n            productId: orderData.productId || '',\n            customerName: orderData.customerName || '',\n            modelType: orderData.modelType || '',\n            packingListImagePath: orderData.packingListImagePath || '',\n            totalPlannedQuantity: orderData.totalPlannedQuantity || '',\n            overallPlannedStartTime: orderData.overallPlannedStartTime || '',\n            overallPlannedEndTime: orderData.overallPlannedEndTime || '',\n            remark: orderData.remark || '',\n            status: orderData.status || '0'\n          };\n          \n          // 设置选中的产品\n          if (orderData.productId) {\n            const product = this.productList.find(p => p.id == orderData.productId);\n            if (product) {\n              this.selectedProduct = product;\n            }\n          }\n          \n          // 设置图片列表\n          if (orderData.packingListImagePath) {\n            this.fileList = orderData.packingListImagePath.split(',').filter(path => path).map(path => ({\n              url: this.baseUrl + path,\n              status: 'success',\n              message: '上传成功',\n              fileName: path\n            }));\n          }\n          \n          // 获取并设置选中的任务（需要从工单项目中获取）\n          if (orderData.workOrderItems && orderData.workOrderItems.length > 0) {\n            this.selectedTasks = orderData.workOrderItems.map(item => {\n              const task = this.taskList.find(t => t.taskId == item.productionTaskId);\n              return task || { taskId: item.productionTaskId, taskName: '未知任务' };\n            }).filter(task => task);\n            this.tempSelectedTasks = [...this.selectedTasks];\n          }\n        }\n      } catch (error) {\n        console.error('获取工单详情失败', error);\n        uni.showToast({\n          title: '获取工单详情失败',\n          icon: 'none'\n        });\n      }\n    },\n    \n    // 获取产品列表\n    async getProductList() {\n      try {\n        const res = await listGoods();\n        if (res && res.rows) {\n          this.productList = res.rows\n        }\n      } catch (error) {\n        console.error('获取产品列表失败', error);\n        uni.showToast({\n          title: '获取产品列表失败',\n          icon: 'none'\n        });\n      }\n    },\n    \n    // 获取任务列表\n    async getTaskList() {\n      try {\n        const res = await listTasks();\n        if (res && res.rows) {\n          this.taskList = res.rows.map(item => ({\n            ...item,\n            selected: false\n          }));\n        }\n      } catch (error) {\n        console.error('获取任务列表失败', error);\n        uni.showToast({\n          title: '获取任务列表失败',\n          icon: 'none'\n        });\n      }\n    },\n    \n    // 显示产品选择器\n    showProductPicker() {\n      this.showProductPickerFlag = true;\n    },\n    \n    // 确认产品选择\n    confirmProduct(e) {\n      console.log(e)\n      const selected = e.value[0];\n      this.selectedProduct = selected;\n      this.formData.productId = this.selectedProduct.id;\n      console.log(this.formData.productId)\n      this.showProductPickerFlag = false;\n    },\n    \n    // 取消产品选择\n    cancelProductPicker() {\n      this.showProductPickerFlag = false;\n    },\n    \n    // 显示任务选择器\n    showTaskPicker() {\n      this.showTaskPickerFlag = true;\n    },\n    \n    // 切换任务选择状态\n    toggleTaskSelection(task) {\n      const index = this.tempSelectedTasks.findIndex(t => t.taskId === task.taskId);\n      if (index > -1) {\n        this.tempSelectedTasks.splice(index, 1);\n      } else {\n        this.tempSelectedTasks.push(task);\n      }\n    },\n    \n    // 确认任务选择\n    confirmTask() {\n      this.selectedTasks = [...this.tempSelectedTasks];\n      this.showTaskPickerFlag = false;\n    },\n    \n    // 取消任务选择\n    cancelTaskPicker() {\n      this.tempSelectedTasks = [...this.selectedTasks];\n      this.showTaskPickerFlag = false;\n    },\n    \n    // 开始日期变化\n    startDateChange(value) {\n      this.formData.overallPlannedStartTime = value;\n    },\n    \n    // 结束日期变化\n    endDateChange(value) {\n      this.formData.overallPlannedEndTime = value;\n    },\n    \n    // 上传图片后的回调\n    async afterRead(event) {\n      const { file } = event;\n      \n      // 设置上传状态\n      this.isUploading = true;\n      this.uploadProgress = 0;\n      \n      // 显示上传中\n      uni.showLoading({\n        title: '图片上传中...',\n        mask: true\n      });\n      \n      try {\n        // 模拟上传进度\n        const progressTimer = setInterval(() => {\n          if (this.uploadProgress < 80) {\n            this.uploadProgress += 10;\n          }\n        }, 150);\n        \n        const uploadPromises = file.map(item => {\n          return new Promise((resolve, reject) => {\n            uni.uploadFile({\n              url: this.baseUrl + '/common/upload',\n              filePath: item.url,\n              name: 'file',\n              success: (res) => {\n                const data = JSON.parse(res.data);\n                if (data.code === 200) {\n                  resolve(data.fileName);\n                } else {\n                  reject(new Error(data.msg || '上传失败'));\n                }\n              },\n              fail: (err) => {\n                reject(err);\n              }\n            });\n          });\n        });\n        \n        const uploadedFiles = await Promise.all(uploadPromises);\n        \n        // 清除进度定时器，设置完成状态\n        clearInterval(progressTimer);\n        this.uploadProgress = 100;\n        \n        // 更新文件列表\n        uploadedFiles.forEach(fileName => {\n          this.fileList.push({\n            url: this.baseUrl + fileName,\n            status: 'success',\n            message: '上传成功',\n            fileName: fileName\n          });\n        });\n        \n        // 更新表单数据\n        this.formData.packingListImagePath = this.fileList\n          .map(file => file.fileName)\n          .join(',');\n        \n        // 延迟一下再隐藏加载，让用户看到100%的进度\n        setTimeout(() => {\n          uni.hideLoading();\n          uni.showToast({\n            title: `成功上传${uploadedFiles.length}张图片`,\n            icon: 'success',\n            duration: 1500\n          });\n          \n          // 重置上传状态\n          this.isUploading = false;\n          this.uploadProgress = 0;\n        }, 300);\n        \n      } catch (error) {\n        // 重置状态\n        this.isUploading = false;\n        this.uploadProgress = 0;\n        \n        uni.hideLoading();\n        uni.showToast({\n          title: '上传失败: ' + error.message,\n          icon: 'error',\n          duration: 2000\n        });\n      }\n    },\n    \n    // 删除图片\n    deletePic(event) {\n      const index = event.index;\n      this.fileList.splice(index, 1);\n      \n      // 更新表单数据\n      this.formData.packingListImagePath = this.fileList\n        .map(file => file.fileName)\n        .join(',');\n      \n      // 重置上传状态（如果没有正在上传的话）\n      if (!this.isUploading) {\n        this.uploadProgress = 0;\n      }\n    },\n    \n    // 提交表单\n    submitForm() {\n      this.submitData();\n    },\n    \n    // 提交数据\n    async submitData() {\n      if (this.formData.productId == null) {\n        uni.showToast({\n          title: '请选择产品',\n          icon: 'none'\n        });\n        return;\n      }\n      console.log(this.selectedTasks)\n      if (!this.selectedTasks || this.selectedTasks.length === 0) {\n        uni.showToast({\n          title: '请选择生产任务',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      if (!this.formData.totalPlannedQuantity) {\n        uni.showToast({\n          title: '请输入计划数量',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      this.submitting = true;\n      \n      try {\n        // 构建任务数据数组\n        const workOrderItems = this.selectedTasks.map(task => ({\n          productionTaskId: task.taskId,\n          plannedQuantity: this.formData.totalPlannedQuantity\n        }));\n        \n        // 构建提交数据\n        const orderData = {\n          productId: this.formData.productId,\n          customerName: this.formData.customerName,\n          modelType: this.formData.modelType,\n          packingListImagePath: this.formData.packingListImagePath,\n          // orderProductNumberVersion: this.formData.orderProductNumberVersion,\n          totalPlannedQuantity: this.formData.totalPlannedQuantity,\n          overallPlannedStartTime: this.formData.overallPlannedStartTime,\n          overallPlannedEndTime: this.formData.overallPlannedEndTime,\n          remark: this.formData.remark,\n          status: this.formData.status,\n          workOrderItems: workOrderItems\n        };\n        \n        let orderRes;\n        if (this.editMode) {\n          // 编辑模式：调用修改接口\n          orderData.workOrderId = this.workOrderId;\n          orderRes = await updateOrders(orderData);\n        } else {\n          // 新增模式：调用新增接口\n          orderRes = await addOrders(orderData);\n        }\n        \n        if (orderRes && orderRes.data) {\n          uni.showToast({\n            title: this.editMode ? '工单修改成功' : '工单创建成功',\n            icon: 'success'\n          });\n          \n          // 返回上一页\n          setTimeout(() => {\n            uni.navigateBack();\n          }, 1500);\n        } else {\n          throw new Error(this.editMode ? '工单修改失败' : '工单创建失败');\n        }\n      } catch (error) {\n        console.error('提交数据失败', error);\n        uni.showToast({\n          title: '提交失败: ' + (error.message || '未知错误'),\n          icon: 'none'\n        });\n      } finally {\n        this.submitting = false;\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.add-order-container {\n  padding: 30rpx;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n  \n  .u-form {\n    background-color: #ffffff;\n    padding: 30rpx 20rpx;\n    border-radius: 16rpx;\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n    \n    ::v-deep .u-form-item {\n      margin-bottom: 30rpx;\n      \n      .u-form-item__body {\n        flex-direction: column;\n        align-items: flex-start;\n        \n        .u-form-item__body__left {\n          width: 100%;\n          margin-bottom: 16rpx;\n          \n          .u-form-item__body__left__content {\n            color: #2c3e50;\n            font-weight: 500;\n            font-size: 28rpx;\n            \n            .u-form-item__body__left__content__label {\n              font-size: 28rpx;\n            }\n          }\n        }\n        \n        .u-form-item__body__right {\n          width: 100%;\n          \n          .u-form-item__body__right__content {\n            width: 100%;\n          }\n        }\n      }\n      \n      &.required {\n        ::v-deep .u-form-item__body__left__content:before {\n          color: #ff5252;\n        }\n      }\n    }\n  }\n  \n  .form-input-wrapper {\n    width: 100%;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    height: 80rpx;\n    padding: 0 24rpx;\n    background-color: #f8f9fa;\n    border-radius: 8rpx;\n    border: 1px solid #ebeef5;\n    transition: all 0.3s;\n    \n    &:active {\n      border-color: #1e3a8a;\n    }\n    \n    .selected-text {\n      font-size: 28rpx;\n      color: #2c3e50;\n      \n      &.placeholder {\n        color: #8c9fba;\n      }\n    }\n  }\n  \n  ::v-deep .u-input {\n    width: 100%;\n    background-color: #f8f9fa;\n    border-radius: 8rpx;\n    border: 1px solid #ebeef5;\n    padding: 0 24rpx;\n    \n    .u-input__input {\n      color: #2c3e50;\n      font-size: 28rpx;\n    }\n  }\n  \n  .upload-container {\n    width: 100%;\n    padding: 20rpx 0;\n    position: relative;\n    \n    ::v-deep .u-upload {\n      .u-upload__button {\n        border-radius: 8rpx;\n        border: 1px dashed #dcdfe6;\n        \n        &:active {\n          opacity: 0.8;\n        }\n      }\n      \n      .u-upload__preview {\n        border-radius: 8rpx;\n        overflow: hidden;\n        \n        .u-upload__preview__image {\n          border-radius: 8rpx;\n        }\n      }\n    }\n    \n    .upload-progress {\n      margin-top: 20rpx;\n      padding: 20rpx;\n      background-color: rgba(30, 58, 138, 0.05);\n      border-radius: 12rpx;\n      border: 1px solid rgba(30, 58, 138, 0.1);\n      \n      .progress-bar {\n        width: 100%;\n        height: 10rpx;\n        background-color: #ebeef5;\n        border-radius: 5rpx;\n        overflow: hidden;\n        margin-bottom: 16rpx;\n        \n        .progress-fill {\n          height: 100%;\n          background: linear-gradient(90deg, #1e3a8a 0%, #3b82f6 100%);\n          border-radius: 5rpx;\n          transition: width 0.3s ease;\n          position: relative;\n          \n          &::after {\n            content: '';\n            position: absolute;\n            top: 0;\n            left: 0;\n            right: 0;\n            bottom: 0;\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n            animation: shimmer 1.5s infinite;\n          }\n        }\n      }\n      \n      .progress-text {\n        font-size: 26rpx;\n        color: #1e3a8a;\n        text-align: center;\n        display: block;\n        font-weight: 600;\n        letter-spacing: 1rpx;\n      }\n    }\n  }\n  \n  .date-picker {\n    width: 100%;\n    \n    :deep(.uni-date) {\n      border: 1px solid #ebeef5;\n      border-radius: 8rpx;\n      background-color: #f8f9fa;\n      \n      .uni-date__icon-clear {\n        display: none;\n      }\n      \n      .uni-date__input {\n        color: #2c3e50;\n        font-size: 28rpx;\n      }\n    }\n  }\n  \n  .submit-btn-wrapper {\n    margin-top: 80rpx;\n    padding: 0 40rpx;\n    \n    ::v-deep .u-button {\n      height: 90rpx;\n      border-radius: 8rpx;\n      box-shadow: 0 4rpx 12rpx rgba(30, 58, 138, 0.2);\n      \n      &.u-button--primary {\n        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);\n      }\n      \n      .u-button__text {\n        font-size: 32rpx;\n        letter-spacing: 4rpx;\n      }\n    }\n  }\n  \n  .task-picker-container {\n    padding: 30rpx;\n    max-height: 70vh;\n    background-color: #ffffff;\n    \n    .task-picker-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 30rpx;\n      padding-bottom: 20rpx;\n      border-bottom: 1px solid #ebeef5;\n      \n      .task-picker-title {\n        font-size: 32rpx;\n        font-weight: 500;\n        color: #2c3e50;\n      }\n      \n      .task-picker-actions {\n        display: flex;\n        align-items: center;\n      }\n    }\n    \n    .task-list {\n      max-height: 60vh;\n      overflow-y: auto;\n      padding: 10rpx 0;\n      \n      .task-item {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 24rpx 20rpx;\n        margin-bottom: 16rpx;\n        border-radius: 8rpx;\n        background-color: #f8f9fa;\n        transition: all 0.3s;\n        \n        &:active {\n          opacity: 0.8;\n        }\n        \n        &.task-item-selected {\n          background-color: rgba(30, 58, 138, 0.05);\n          border-left: 4rpx solid #1e3a8a;\n          \n          .task-name {\n            color: #1e3a8a;\n            font-weight: 600;\n          }\n        }\n        \n        .task-info {\n          flex: 1;\n          padding-right: 20rpx;\n          \n          .task-name {\n            font-size: 28rpx;\n            color: #2c3e50;\n            margin-bottom: 10rpx;\n            display: block;\n            font-weight: 500;\n          }\n          \n          .task-desc {\n            font-size: 24rpx;\n            color: #8c9fba;\n            line-height: 1.4;\n          }\n        }\n      }\n    }\n    \n    .task-picker-footer {\n      padding-top: 20rpx;\n      margin-top: 20rpx;\n      border-top: 1px solid #ebeef5;\n      display: flex;\n      justify-content: flex-end;\n      \n      .selected-count {\n        font-size: 26rpx;\n        color: #606266;\n      }\n    }\n  }\n}\n\n@keyframes shimmer {\n  0% {\n    transform: translateX(-100%);\n  }\n  100% {\n    transform: translateX(100%);\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addOrder.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addOrder.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752425864390\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}