import request from '@/utils/request'

// 查询工单任务项列表
export function listOrderItems(query) {
  return request({
    url: '/work/orderItems/list',
    method: 'get',
    params: query
  })
}

// 查询工单任务项详细
export function getOrderItems(workOrderItemId) {
  return request({
    url: '/work/orderItems/' + workOrderItemId,
    method: 'get'
  })
}

// 新增工单任务项
export function addOrderItems(data) {
  return request({
    url: '/work/orderItems',
    method: 'post',
    data: data
  })
}

// 修改工单任务项
export function updateOrderItems(data) {
  return request({
    url: '/work/orderItems',
    method: 'put',
    data: data
  })
}

// 删除工单任务项
export function delOrderItems(workOrderItemId) {
  return request({
    url: '/work/orderItems/' + workOrderItemId,
    method: 'delete'
  })
}
