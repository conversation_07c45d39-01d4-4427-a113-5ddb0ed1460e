<view class="help-container data-v-00369c57"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="findex" wx:key="findex"><view class="list-title data-v-00369c57" title="{{item.$orig.title}}"><view class="text-title data-v-00369c57"><view class="{{['data-v-00369c57',item.$orig.icon]}}"></view>{{item.$orig.title+''}}</view><view class="childList data-v-00369c57"><block wx:for="{{item.$orig.childList}}" wx:for-item="child" wx:for-index="zindex" wx:key="zindex"><view class="question data-v-00369c57" hover-class="hover" data-event-opts="{{[['tap',[['handleText',['$0'],[[['list','',findex],['childList','',zindex]]]]]]]}}" bindtap="__e"><view class="text-item data-v-00369c57">{{child.title}}</view><block wx:if="{{zindex!==item.g0-1}}"><view class="line data-v-00369c57"></view></block></view></block></view></view></block></view>