import request from '@/utils/request'

// 查询工单任务指派列表
export function listTaskAssignments(query) {
  return request({
    url: '/work/taskAssignments/list',
    method: 'get',
    params: query
  })
}

// 查询工单任务指派详细
export function getTaskAssignments(assignmentId) {
  return request({
    url: '/work/taskAssignments/' + assignmentId,
    method: 'get'
  })
}

// 新增工单任务指派
export function addTaskAssignments(data) {
  return request({
    url: '/work/taskAssignments',
    method: 'post',
    data: data
  })
}

// 修改工单任务指派
export function updateTaskAssignments(data) {
  return request({
    url: '/work/taskAssignments',
    method: 'put',
    data: data
  })
}

// 删除工单任务指派
export function delTaskAssignments(assignmentId) {
  return request({
    url: '/work/taskAssignments/' + assignmentId,
    method: 'delete'
  })
}
