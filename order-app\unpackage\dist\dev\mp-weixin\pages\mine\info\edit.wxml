<view class="container"><view class="example"><uni-forms class="vue-ref" vue-id="3fe9d4be-1" model="{{user}}" labelWidth="80px" data-ref="form" bind:__l="__l" vue-slots="{{['default']}}"><uni-forms-item vue-id="{{('3fe9d4be-2')+','+('3fe9d4be-1')}}" label="用户昵称" name="nickName" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('3fe9d4be-3')+','+('3fe9d4be-2')}}" placeholder="请输入昵称" value="{{user.nickName}}" data-event-opts="{{[['^input',[['__set_model',['$0','nickName','$event',[]],['user']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('3fe9d4be-4')+','+('3fe9d4be-1')}}" label="手机号码" name="phonenumber" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('3fe9d4be-5')+','+('3fe9d4be-4')}}" placeholder="请输入手机号码" value="{{user.phonenumber}}" data-event-opts="{{[['^input',[['__set_model',['$0','phonenumber','$event',[]],['user']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('3fe9d4be-6')+','+('3fe9d4be-1')}}" label="邮箱" name="email" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('3fe9d4be-7')+','+('3fe9d4be-6')}}" placeholder="请输入邮箱" value="{{user.email}}" data-event-opts="{{[['^input',[['__set_model',['$0','email','$event',[]],['user']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('3fe9d4be-8')+','+('3fe9d4be-1')}}" label="性别" name="sex" required="{{true}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-data-checkbox bind:input="__e" vue-id="{{('3fe9d4be-9')+','+('3fe9d4be-8')}}" localdata="{{sexs}}" value="{{user.sex}}" data-event-opts="{{[['^input',[['__set_model',['$0','sex','$event',[]],['user']]]]]}}" bind:__l="__l"></uni-data-checkbox></uni-forms-item></uni-forms><button type="primary" data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">提交</button></view></view>