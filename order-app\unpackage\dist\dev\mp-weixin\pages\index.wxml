<view class="order-list-container"><block wx:if="{{hasPermission}}"><view data-event-opts="{{[['tap',[['goToAddOrder',['$event']]]]]}}" class="fab-button" bindtap="__e"><u-icon vue-id="18efaafd-1" name="plus" size="50rpx" color="#ffffff" bind:__l="__l"></u-icon></view><view class="search-section"><view class="search-box"><u-icon vue-id="18efaafd-2" name="search" size="36rpx" color="#8c9fba" bind:__l="__l"></u-icon><input type="text" placeholder="请输入产品编号版本搜索" confirm-type="search" data-event-opts="{{[['input',[['__set_model',['$0','orderProductNumberVersion','$event',[]],['queryParams']],['onSearchInput',['$event']]]],['confirm',[['handleSearch',['$event']]]]]}}" value="{{queryParams.orderProductNumberVersion}}" bindinput="__e" bindconfirm="__e"/><text data-event-opts="{{[['tap',[['handleSearch',['$event']]]]]}}" class="search-btn" bindtap="__e">搜索</text></view></view><block wx:if="{{hasPermission}}"><view class="stats-section"><view class="stats-item"><text class="stats-label">工单总量：</text><text class="stats-value">{{total||0}}</text></view><view class="stats-item"><text class="stats-label">当前显示：</text><text class="stats-value">{{$root.g0}}</text></view></view></block><scroll-view class="order-list-scroll" scroll-y="{{true}}" enable-back-to-top="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{isRefreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><block wx:if="{{$root.g1}}"><view class="empty-container"><u-empty vue-id="18efaafd-3" mode="data" text="暂无工单数据" bind:__l="__l"></u-empty></view></block><block wx:else><view class="order-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toOrderDetail',['$0'],[[['orderList','',index]]]]]]]}}" class="order-card" bindtap="__e"><view class="order-header"><view class="order-no">{{"工单编号："+item.$orig.workOrderNo}}</view><view class="{{['order-status',(item.$orig.status==='0')?'status-pending':'',(item.$orig.status==='1')?'status-processing':'',(item.$orig.status==='2')?'status-completed':'']}}">{{''+item.m0+''}}</view></view><view class="order-content"><view class="info-row"><text class="info-label">产品名称：</text><text class="info-value">{{item.$orig.productName}}</text></view><block wx:if="{{item.$orig.orderProductNumberVersion}}"><view class="info-row"><text class="info-label">产品编号：</text><text class="info-value">{{item.$orig.orderProductNumberVersion}}</text></view></block><view class="info-row"><text class="info-label">计划数量：</text><text class="info-value">{{item.$orig.totalPlannedQuantity}}</text></view><view class="info-row"><text class="info-label">计划开始：</text><text class="info-value">{{item.m1}}</text></view><view class="info-row"><text class="info-label">计划结束：</text><text class="info-value">{{item.m2}}</text></view><block wx:if="{{item.$orig.remark}}"><view class="info-row"><text class="info-label">备注：</text><text class="info-value remark-text">{{item.$orig.remark}}</text></view></block><view class="action-buttons"><button data-event-opts="{{[['tap',[['deleteOrder',['$0'],[[['orderList','',index]]]]]]]}}" class="delete-btn" catchtap="__e">删除</button><button data-event-opts="{{[['tap',[['goToEditOrder',['$0'],[[['orderList','',index]]]]]]]}}" class="edit-btn" catchtap="__e">修改</button></view></view></view></block><block wx:if="{{loading}}"><view class="loading-more"><u-loading vue-id="18efaafd-4" mode="circle" size="24" bind:__l="__l"></u-loading><text class="loading-text">加载中...</text></view></block><block wx:if="{{$root.g2}}"><view class="no-more"><text>没有更多数据了</text></view></block></view></block></scroll-view></block><block wx:else><view class="no-permission-container"><view class="no-permission-content"><view class="permission-icon"><u-icon vue-id="18efaafd-5" name="lock" size="120rpx" color="#909399" bind:__l="__l"></u-icon></view><view class="permission-title">访问受限</view><view class="permission-description">抱歉，您暂无权限访问工单管理页面</view></view></view></block></view>