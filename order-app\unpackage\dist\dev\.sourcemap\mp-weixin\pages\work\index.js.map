{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/work/index.vue?52ef", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/work/index.vue?8cd5", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/work/index.vue?fde5", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/work/index.vue?1d09", "uni-app:///pages/work/index.vue", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/work/index.vue?27c9", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/work/index.vue?0052"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "queryParams", "assignedToUserId", "pageSize", "pageNum", "assignmentStatus", "orderProductNumberVersion", "taskList", "total", "isRefreshing", "hasMore", "baseUrl", "showSubmitFormFlag", "currentTask", "formData", "goodQuantity", "defectiveQuantity", "pic", "byteNo", "locationAddress", "description", "repairGoodNo", "fileList", "searchKeyword", "searchTimer", "isUploading", "uploadProgress", "computed", "currentTaskType", "onLoad", "methods", "getList", "isRefresh", "res", "console", "uni", "title", "icon", "changeStatus", "onRefresh", "loadMore", "getStatusText", "showSubmitForm", "url", "status", "message", "closeSubmitForm", "afterRead", "mask", "file", "progressTimer", "filePath", "name", "uploadErr", "uploadRes", "clearInterval", "result", "newPic", "setTimeout", "duration", "deletePic", "pics", "submitForm", "totalQuantity", "recordData", "assignmentId", "updateData", "onSearchInput", "clearTimeout", "performSearch", "clearSearch"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChFA;AAAA;AAAA;AAAA;AAAgoB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACwTppB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA;gBAEA;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;gBACA;kBACA;gBACA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;QACA;UACA3B;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QACA;QACA;UACA;YAAA;UAAA;YAAA;cACAsB;cACAC;cACAC;YACA;UAAA;QACA;UACA;QACA;MACA;QACA;QACA;UACA9B;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;IACA;IAEA;IACAyB;MACA;MACA;MACA;QACA/B;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACA0B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAb;;gBAEA;gBACA;gBACA;;gBAEA;gBACAC;kBACAC;kBACAY;gBACA;gBAAA;gBAGAC,mBAEA;gBACAC;kBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA,OAEAf;kBACAQ;kBACAQ;kBACAC;gBACA;cAAA;gBAAA;gBAAA;gBAJAC;gBAAAC;gBAMA;gBACAC;gBACA;gBAAA,KAEAF;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGAG;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAC;gBACA;kBACA;gBACA;kBACA;gBACA;;gBAEA;gBACA;kBAAA;gBAAA;kBAAA;oBACAd;oBACAC;oBACAC;kBACA;gBAAA;;gBAEA;gBACAa;kBACAvB;kBACAA;oBACAC;oBACAC;oBACAsB;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAzB;;gBAEA;gBACA;gBACA;gBAEAC;gBACAA;kBACAC;kBACAC;kBACAsB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MACA1B;MACA;QAAA;MAAA;MACA2B;MACA;;MAEA;MACA;QAAA;UACAlB;UACAC;UACAC;QACA;MAAA;IACA;IAEA;IACAiB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACA3B;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIAtB;gBACAC;gBACA+C;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA5B;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIAtB;gBACAC;gBACA+C;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA5B;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAMA;gBACA2B;kBACAC;kBACAhD;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,GAEA;gBACA;kBACA2C;kBACAA;gBACA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBACA;gBACAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAGA;gBACAE;kBACAD;kBACA5D;gBACA,GAEA;gBACA;kBACA6D;kBACAA;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAEA/B;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAH;gBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA8B;MAAA;MACA;MACA;MACA;QACAC;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACAF;QACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvvBA;AAAA;AAAA;AAAA;AAAmsC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAvtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/work/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/work/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=51b5538d&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/work/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=51b5538d&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uUpload: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-upload/u-upload\" */ \"@/uni_modules/uview-ui/components/u-upload/u-upload.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.taskList, function (task, index) {\n    var $orig = _vm.__get_orig(task)\n    var m0 = _vm.getStatusText(task.assignmentStatus)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g0 = _vm.taskList.length\n  var g1 = _vm.taskList.length\n  var g2 =\n    _vm.currentTaskType === \"packaging\"\n      ? (_vm.formData.byteNo || \"\").length\n      : null\n  var g3 =\n    !(_vm.currentTaskType === \"packaging\") &&\n    !(_vm.currentTaskType === \"shipping\") &&\n    _vm.currentTaskType === \"maintenance\"\n      ? (_vm.formData.repairGoodNo || \"\").length\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"task-list-container\">\r\n\t\t<!-- 状态筛选tabs -->\r\n\t\t<view class=\"status-tabs\">\r\n\t\t\t<view \r\n\t\t\t\tclass=\"tab-item\" \r\n\t\t\t\t:class=\"{ active: queryParams.assignmentStatus === null }\"\r\n\t\t\t\t@click=\"changeStatus(null)\"\r\n\t\t\t>全部</view>\r\n\t\t\t<view \r\n\t\t\t\tclass=\"tab-item\" \r\n\t\t\t\t:class=\"{ active: queryParams.assignmentStatus === '1' }\"\r\n\t\t\t\t@click=\"changeStatus('1')\"\r\n\t\t\t>进行中</view>\r\n\t\t\t<view \r\n\t\t\t\tclass=\"tab-item\" \r\n\t\t\t\t:class=\"{ active: queryParams.assignmentStatus === '2' }\"\r\n\t\t\t\t@click=\"changeStatus('2')\"\r\n\t\t\t>已完成</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 搜索框 -->\r\n\t\t<view class=\"search-container\">\r\n\t\t\t<view class=\"search-box\">\r\n\t\t\t\t<u-icon name=\"search\" size=\"32\" color=\"#909399\" class=\"search-icon\"></u-icon>\r\n\t\t\t\t<input \r\n\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\tv-model=\"searchKeyword\"\r\n\t\t\t\t\tclass=\"search-input\"\r\n\t\t\t\t\tplaceholder=\"请输入产品编号版本搜索\"\r\n\t\t\t\t\t@input=\"onSearchInput\"\r\n\t\t\t\t\t@confirm=\"performSearch\"\r\n\t\t\t\t/>\r\n\t\t\t\t<view class=\"search-clear\" v-if=\"searchKeyword\" @click=\"clearSearch\">\r\n\t\t\t\t\t<u-icon name=\"close-circle-fill\" size=\"32\" color=\"#c0c4cc\"></u-icon>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 任务列表 -->\r\n\t\t<scroll-view \r\n\t\t\tclass=\"task-list\"\r\n\t\t\tscroll-y\r\n\t\t\t@scrolltolower=\"loadMore\"\r\n\t\t\trefresher-enabled\r\n\t\t\t:refresher-triggered=\"isRefreshing\"\r\n\t\t\t@refresherrefresh=\"onRefresh\"\r\n\t\t>\r\n\t\t\t<view class=\"task-item\" v-for=\"(task, index) in taskList\" :key=\"index\">\r\n\t\t\t\t<view class=\"task-header\">\r\n\t\t\t\t\t<text class=\"task-name\">{{ task.taskName }}</text>\r\n\t\t\t\t\t<text class=\"task-status\" :class=\"'status-' + task.assignmentStatus\">\r\n\t\t\t\t\t\t{{ getStatusText(task.assignmentStatus) }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"task-info\">\r\n\t\t\t\t\t<view class=\"info-row\">\r\n\t\t\t\t\t\t<text class=\"label\">工单号：</text>\r\n\t\t\t\t\t\t<text class=\"value\">{{ task.workOrderNo }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-row\">\r\n\t\t\t\t\t\t<text class=\"label\">指派数量：</text>\r\n\t\t\t\t\t\t<text class=\"value\">{{ task.assignedQuantity }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-row\">\r\n\t\t\t\t\t\t<text class=\"label\">下单客户：</text>\r\n\t\t\t\t\t\t<text class=\"value\">{{ task.orderCustomerName || '暂无' }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-row\">\r\n\t\t\t\t\t\t<text class=\"label\">订单备注：</text>\r\n\t\t\t\t\t\t<text class=\"value\">{{ task.ordersRemark || '暂无' }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<template v-if=\"task.assignmentStatus === '2'\">\r\n\t\t\t\t\t\t<view class=\"info-row\">\r\n\t\t\t\t\t\t\t<text class=\"label\">良品数：</text>\r\n\t\t\t\t\t\t\t<text class=\"value\">{{ task.reportedGoodQuantity }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info-row\">\r\n\t\t\t\t\t\t\t<text class=\"label\">不良品数：</text>\r\n\t\t\t\t\t\t\t<text class=\"value\">{{ task.reportedDefectiveQuantity }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"task-footer\">\r\n\t\t\t\t\t<button \r\n\t\t\t\t\t\tv-if=\"task.recordId\"\r\n\t\t\t\t\t\tclass=\"submit-btn secondary\"\r\n\t\t\t\t\t\t@click=\"showSubmitForm(task)\"\r\n\t\t\t\t\t>修改</button>\r\n\t\t\t\t\t<button \r\n\t\t\t\t\t\tv-if=\"task.assignmentStatus === '1'\"\r\n\t\t\t\t\t\tclass=\"submit-btn\"\r\n\t\t\t\t\t\t@click=\"showSubmitForm(task)\"\r\n\t\t\t\t\t>完成任务</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 加载更多提示 -->\r\n\t\t\t<view class=\"load-more\" v-if=\"taskList.length > 0\">\r\n\t\t\t\t<text v-if=\"hasMore\">加载中...</text>\r\n\t\t\t\t<text v-else>没有更多数据了</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 空状态提示 -->\r\n\t\t\t<view class=\"empty-state\" v-if=\"taskList.length === 0\">\r\n\t\t\t\t<text>暂无任务</text>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t\t\r\n\t\t<!-- 提交表单弹窗 -->\r\n\t\t<u-popup\r\n\t\t\tmode=\"bottom\"\r\n\t\t\t:show=\"showSubmitFormFlag\"\r\n\t\t\t@close=\"closeSubmitForm\"\r\n\t\t\tborder-radius=\"24\"\r\n\t\t\tsafe-area-inset-bottom\r\n\t\t>\r\n\t\t\t<view class=\"submit-form-container\">\r\n\t\t\t\t<view class=\"form-header\">\r\n\t\t\t\t\t<text class=\"form-title\">提交任务</text>\r\n\t\t\t\t\t<view class=\"form-actions\">\r\n\t\t\t\t\t\t<u-button plain size=\"mini\" @click=\"closeSubmitForm\" style=\"margin-right: 20rpx;\">取消</u-button>\r\n\t\t\t\t\t\t<u-button type=\"primary\" size=\"mini\" @click=\"submitForm\" :disabled=\"isUploading\">\r\n\t\t\t\t\t\t\t{{ isUploading ? '上传中...' : '确认' }}\r\n\t\t\t\t\t\t</u-button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<scroll-view class=\"form-content\" scroll-y>\r\n\t\t\t\t\t<!-- 包装任务字段 -->\r\n\t\t\t\t\t<template v-if=\"currentTaskType === 'packaging'\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<text class=\"form-label\">字节号 *</text>\r\n\t\t\t\t\t\t<textarea \r\n\t\t\t\t\t\t\tv-model=\"formData.byteNo\"\r\n\t\t\t\t\t\t\tclass=\"form-textarea\"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入字节号\"\r\n\t\t\t\t\t\t\t:auto-height=\"true\"\r\n\t\t\t\t\t\t\t:maxlength=\"500\"\r\n\t\t\t\t\t\t></textarea>\r\n\t\t\t\t\t\t<view class=\"char-count\">\r\n\t\t\t\t\t\t\t<text>{{ (formData.byteNo || '').length }}/500</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\">良品数 *</text>\r\n\t\t\t\t\t\t\t<input \r\n\t\t\t\t\t\t\t\ttype=\"number\" \r\n\t\t\t\t\t\t\t\tv-model=\"formData.goodQuantity\"\r\n\t\t\t\t\t\t\t\tclass=\"form-input\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入良品数\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\">不良品数 *</text>\r\n\t\t\t\t\t\t\t<input \r\n\t\t\t\t\t\t\t\ttype=\"number\" \r\n\t\t\t\t\t\t\t\tv-model=\"formData.defectiveQuantity\"\r\n\t\t\t\t\t\t\t\tclass=\"form-input\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入不良品数\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\">图片 *</text>\r\n\t\t\t\t\t\t\t<view class=\"upload-container\">\r\n\t\t\t\t\t\t\t\t<u-upload\r\n\t\t\t\t\t\t\t\t\t:fileList=\"fileList\"\r\n\t\t\t\t\t\t\t\t\t@afterRead=\"afterRead\"\r\n\t\t\t\t\t\t\t\t\t@delete=\"deletePic\"\r\n\t\t\t\t\t\t\t\t\t:maxCount=\"100\"\r\n\t\t\t\t\t\t\t\t\t:maxSize=\"5242880\"\r\n\t\t\t\t\t\t\t\t\twidth=\"200\"\r\n\t\t\t\t\t\t\t\t\theight=\"200\"\r\n\t\t\t\t\t\t\t\t\t:previewFullImage=\"true\"\r\n\t\t\t\t\t\t\t\t\t:disabled=\"isUploading\"\r\n\t\t\t\t\t\t\t\t></u-upload>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<!-- 上传进度指示器 -->\r\n\t\t\t\t\t\t\t\t<view class=\"upload-progress\" v-if=\"isUploading\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"progress-bar\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"progress-fill\" :style=\"{ width: uploadProgress + '%' }\"></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<text class=\"progress-text\">上传中 {{ uploadProgress }}%</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 发货任务字段 -->\r\n\t\t\t\t\t<template v-else-if=\"currentTaskType === 'shipping'\">\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\">发货地址 *</text>\r\n\t\t\t\t\t\t\t<textarea \r\n\t\t\t\t\t\t\t\tv-model=\"formData.locationAddress\"\r\n\t\t\t\t\t\t\t\tclass=\"form-textarea\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入发货地址\"\r\n\t\t\t\t\t\t\t\t:auto-height=\"true\"\r\n\t\t\t\t\t\t\t\t:maxlength=\"500\"\r\n\t\t\t\t\t\t\t></textarea>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 维修任务字段 -->\r\n\t\t\t\t\t<template v-else-if=\"currentTaskType === 'maintenance'\">\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\">图片 *</text>\r\n\t\t\t\t\t\t\t<view class=\"upload-container\">\r\n\t\t\t\t\t\t\t\t<u-upload\r\n\t\t\t\t\t\t\t\t\t:fileList=\"fileList\"\r\n\t\t\t\t\t\t\t\t\t@afterRead=\"afterRead\"\r\n\t\t\t\t\t\t\t\t\t@delete=\"deletePic\"\r\n\t\t\t\t\t\t\t\t\t:maxCount=\"100\"\r\n\t\t\t\t\t\t\t\t\t:maxSize=\"5242880\"\r\n\t\t\t\t\t\t\t\t\twidth=\"200\"\r\n\t\t\t\t\t\t\t\t\theight=\"200\"\r\n\t\t\t\t\t\t\t\t\t:previewFullImage=\"true\"\r\n\t\t\t\t\t\t\t\t\t:disabled=\"isUploading\"\r\n\t\t\t\t\t\t\t\t></u-upload>\r\n\r\n\t\t\t\t\t\t\t\t<!-- 上传进度指示器 -->\r\n\t\t\t\t\t\t\t\t<view class=\"upload-progress\" v-if=\"isUploading\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"progress-bar\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"progress-fill\" :style=\"{ width: uploadProgress + '%' }\"></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<text class=\"progress-text\">上传中 {{ uploadProgress }}%</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\">维修产品编号</text>\r\n\t\t\t\t\t\t\t<textarea\r\n\t\t\t\t\t\t\t\tv-model=\"formData.repairGoodNo\"\r\n\t\t\t\t\t\t\t\tclass=\"form-textarea\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入维修产品编号\"\r\n\t\t\t\t\t\t\t\t:auto-height=\"true\"\r\n\t\t\t\t\t\t\t\t:maxlength=\"200\"\r\n\t\t\t\t\t\t\t></textarea>\r\n\t\t\t\t\t\t\t<view class=\"char-count\">\r\n\t\t\t\t\t\t\t\t<text>{{ (formData.repairGoodNo || '').length }}/200</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\">文字说明 *</text>\r\n\t\t\t\t\t\t\t<textarea\r\n\t\t\t\t\t\t\t\tv-model=\"formData.description\"\r\n\t\t\t\t\t\t\t\tclass=\"form-textarea\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入文字说明\"\r\n\t\t\t\t\t\t\t\t:auto-height=\"true\"\r\n\t\t\t\t\t\t\t\t:maxlength=\"500\"\r\n\t\t\t\t\t\t\t></textarea>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 其他任务字段（保持原有逻辑） -->\r\n\t\t\t\t\t<template v-else>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\">良品数 *</text>\r\n\t\t\t\t\t\t\t<input \r\n\t\t\t\t\t\t\t\ttype=\"number\" \r\n\t\t\t\t\t\t\t\tv-model=\"formData.goodQuantity\"\r\n\t\t\t\t\t\t\t\tclass=\"form-input\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入良品数\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\">不良品数 *</text>\r\n\t\t\t\t\t\t\t<input \r\n\t\t\t\t\t\t\t\ttype=\"number\" \r\n\t\t\t\t\t\t\t\tv-model=\"formData.defectiveQuantity\"\r\n\t\t\t\t\t\t\t\tclass=\"form-input\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入不良品数\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"form-label\">图片 *</text>\r\n\t\t\t\t\t\t\t<view class=\"upload-container\">\r\n\t\t\t\t\t\t\t\t<u-upload\r\n\t\t\t\t\t\t\t\t\t:fileList=\"fileList\"\r\n\t\t\t\t\t\t\t\t\t@afterRead=\"afterRead\"\r\n\t\t\t\t\t\t\t\t\t@delete=\"deletePic\"\r\n\t\t\t\t\t\t\t\t\t:maxCount=\"100\"\r\n\t\t\t\t\t\t\t\t\t:maxSize=\"5242880\"\r\n\t\t\t\t\t\t\t\t\twidth=\"200\"\r\n\t\t\t\t\t\t\t\t\theight=\"200\"\r\n\t\t\t\t\t\t\t\t\t:previewFullImage=\"true\"\r\n\t\t\t\t\t\t\t\t\t:disabled=\"isUploading\"\r\n\t\t\t\t\t\t\t\t></u-upload>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<!-- 上传进度指示器 -->\r\n\t\t\t\t\t\t\t\t<view class=\"upload-progress\" v-if=\"isUploading\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"progress-bar\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"progress-fill\" :style=\"{ width: uploadProgress + '%' }\"></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<text class=\"progress-text\">上传中 {{ uploadProgress }}%</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { listTaskAssignments,updateTaskAssignments } from \"@/api/work/taskAssignments.js\"\r\n\timport { updateProductionRecords,addProductionRecords } from \"@/api/work/productionRecords.js\"\r\n\timport { baseUrl } from \"../../config\"\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tqueryParams:{\r\n\t\t\t\t\tassignedToUserId:this.$store.state.user.userId,\r\n\t\t\t\t\tpageSize:5,\r\n\t\t\t\t\tpageNum:1,\r\n\t\t\t\t\tassignmentStatus:null,\r\n\t\t\t\t\torderProductNumberVersion: ''\r\n\t\t\t\t},\r\n\t\t\t\ttaskList: [],\r\n\t\t\t\ttotal: 0,\r\n\t\t\t\tisRefreshing: false,\r\n\t\t\t\thasMore: true,\r\n\t\t\t\tbaseUrl,\r\n\t\t\t\tshowSubmitFormFlag: false,\r\n\t\t\t\tcurrentTask: null,\r\n\t\t\t\tformData: {\r\n\t\t\t\t\tgoodQuantity: '',\r\n\t\t\t\t\tdefectiveQuantity: '',\r\n\t\t\t\t\tpic: '',\r\n\t\t\t\t\tbyteNo: '', // 字节号（包装任务用）\r\n\t\t\t\t\tlocationAddress: '', // 地址（发货任务用）\r\n\t\t\t\t\tdescription: '', // 文字说明（维修任务用）\r\n\t\t\t\t\trepairGoodNo: '' // 维修产品编号（维修任务用）\r\n\t\t\t\t},\r\n\t\t\t\tfileList: [], // 用于存储图片列表\r\n\t\t\t\tsearchKeyword: '',\r\n\t\t\t\tsearchTimer: null, // 搜索防抖定时器\r\n\t\t\t\tisUploading: false, // 上传状态\r\n\t\t\t\tuploadProgress: 0 // 上传进度\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 获取当前任务类型\r\n\t\t\tcurrentTaskType() {\r\n\t\t\t\tif (!this.currentTask || !this.currentTask.taskName) return 'other';\r\n\t\t\t\tconst taskName = this.currentTask.taskName.toLowerCase();\r\n\t\t\t\tif (taskName.includes('包装')) return 'packaging';\r\n\t\t\t\tif (taskName.includes('发货')) return 'shipping';\r\n\t\t\t\tif (taskName.includes('维修')) return 'maintenance';\r\n\t\t\t\treturn 'other';\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.getList()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取任务列表\r\n\t\t\tasync getList(isRefresh = false) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 设置搜索参数\r\n\t\t\t\t\tthis.queryParams.orderProductNumberVersion = this.searchKeyword\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst res = await listTaskAssignments(this.queryParams)\r\n\t\t\t\t\tif (isRefresh) {\r\n\t\t\t\t\t\tthis.taskList = res.rows\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.taskList = [...this.taskList, ...res.rows]\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.total = res.total\r\n\t\t\t\t\tthis.hasMore = this.taskList.length < this.total\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取任务列表失败：', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取任务列表失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 切换状态\r\n\t\t\tchangeStatus(status) {\r\n\t\t\t\tthis.queryParams.assignmentStatus = status\r\n\t\t\t\tthis.queryParams.pageNum = 1\r\n\t\t\t\tthis.taskList = []\r\n\t\t\t\tthis.getList(true)\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 下拉刷新\r\n\t\t\tasync onRefresh() {\r\n\t\t\t\tthis.isRefreshing = true\r\n\t\t\t\tthis.queryParams.pageNum = 1\r\n\t\t\t\tawait this.getList(true)\r\n\t\t\t\tthis.isRefreshing = false\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 加载更多\r\n\t\t\tloadMore() {\r\n\t\t\t\tif (!this.hasMore) return\r\n\t\t\t\tthis.queryParams.pageNum++\r\n\t\t\t\tthis.getList()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取状态文本\r\n\t\t\tgetStatusText(status) {\r\n\t\t\t\tconst statusMap = {\r\n\t\t\t\t\t'1': '进行中',\r\n\t\t\t\t\t'2': '已完成'\r\n\t\t\t\t}\r\n\t\t\t\treturn statusMap[status] || '未知状态'\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 显示提交表单\r\n\t\t\tshowSubmitForm(task) {\r\n\t\t\t\tthis.currentTask = task\r\n\t\t\t\tif (task.recordId) {\r\n\t\t\t\t\t// 如果是修改，填充已有数据\r\n\t\t\t\t\tthis.formData = {\r\n\t\t\t\t\t\tgoodQuantity: task.goodQuantity != '' || task.goodQuantity != null ? task.goodQuantity : '',\r\n\t\t\t\t\t\tdefectiveQuantity: task.defectiveQuantity != '' || task.defectiveQuantity != null ? task.defectiveQuantity : '',\r\n\t\t\t\t\t\tpic: task.pic || '',\r\n\t\t\t\t\t\tbyteNo: task.byteNo || '',\r\n\t\t\t\t\t\tlocationAddress: task.locationAddress || '',\r\n\t\t\t\t\t\tdescription: task.description || '',\r\n\t\t\t\t\t\trepairGoodNo: task.repairGoodNo || ''\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 设置图片列表\r\n\t\t\t\t\tif (task.pic) {\r\n\t\t\t\t\t\tthis.fileList = task.pic.split(',').filter(url => url && url.trim()).map(url => ({\r\n\t\t\t\t\t\t\turl: this.baseUrl + url.trim(),\r\n\t\t\t\t\t\t\tstatus: 'success',\r\n\t\t\t\t\t\t\tmessage: '上传成功'\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.fileList = []\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果是新建，设置默认值：良品数为指派数量，不良品数为0\r\n\t\t\t\t\tthis.formData = {\r\n\t\t\t\t\t\tgoodQuantity: task.assignedQuantity || '',\r\n\t\t\t\t\t\tdefectiveQuantity: '0',\r\n\t\t\t\t\t\tpic: '',\r\n\t\t\t\t\t\tbyteNo: '',\r\n\t\t\t\t\t\tlocationAddress: '',\r\n\t\t\t\t\t\tdescription: '',\r\n\t\t\t\t\t\trepairGoodNo: ''\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.fileList = []\r\n\t\t\t\t}\r\n\t\t\t\tthis.showSubmitFormFlag = true\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 关闭提交表单\r\n\t\t\tcloseSubmitForm() {\r\n\t\t\t\tthis.showSubmitFormFlag = false\r\n\t\t\t\tthis.currentTask = null\r\n\t\t\t\tthis.formData = {\r\n\t\t\t\t\tgoodQuantity: '',\r\n\t\t\t\t\tdefectiveQuantity: '',\r\n\t\t\t\t\tpic: '',\r\n\t\t\t\t\tbyteNo: '',\r\n\t\t\t\t\tlocationAddress: '',\r\n\t\t\t\t\tdescription: '',\r\n\t\t\t\t\trepairGoodNo: ''\r\n\t\t\t\t}\r\n\t\t\t\tthis.fileList = []\r\n\t\t\t\t// 重置上传状态\r\n\t\t\t\tthis.isUploading = false\r\n\t\t\t\tthis.uploadProgress = 0\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 上传图片后\r\n\t\t\tasync afterRead(event) {\r\n\t\t\t\tconsole.log(event)\r\n\t\t\t\t\r\n\t\t\t\t// 设置上传状态\r\n\t\t\t\tthis.isUploading = true\r\n\t\t\t\tthis.uploadProgress = 0\r\n\t\t\t\t\r\n\t\t\t\t// 显示上传中提示\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '图片上传中...',\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst file = event.file\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 模拟上传进度（因为uni.uploadFile不支持进度回调）\r\n\t\t\t\t\tconst progressTimer = setInterval(() => {\r\n\t\t\t\t\t\tif (this.uploadProgress < 90) {\r\n\t\t\t\t\t\t\tthis.uploadProgress += 10\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 100)\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst [uploadErr, uploadRes] = await uni.uploadFile({\r\n\t\t\t\t\t\turl: this.baseUrl + '/common/upload',\r\n\t\t\t\t\t\tfilePath: file.url,\r\n\t\t\t\t\t\tname: 'file'\r\n\t\t\t\t\t})\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 清除进度定时器\r\n\t\t\t\t\tclearInterval(progressTimer)\r\n\t\t\t\t\tthis.uploadProgress = 100\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (uploadErr) {\r\n\t\t\t\t\t\tthrow new Error('上传图片失败')\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst result = JSON.parse(uploadRes.data)\r\n\t\t\t\t\tif (result.code === 200) {\r\n\t\t\t\t\t\t// 更新图片路径，用逗号分隔多张图片\r\n\t\t\t\t\t\tconst newPic = result.fileName\r\n\t\t\t\t\t\tif (this.formData.pic) {\r\n\t\t\t\t\t\t\tthis.formData.pic = this.formData.pic + ',' + newPic\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.formData.pic = newPic\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 更新文件列表显示\r\n\t\t\t\t\t\tthis.fileList = this.formData.pic.split(',').filter(url => url).map(url => ({\r\n\t\t\t\t\t\t\turl: this.baseUrl + url,\r\n\t\t\t\t\t\t\tstatus: 'success',\r\n\t\t\t\t\t\t\tmessage: '上传成功'\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 延迟一下再隐藏加载，让用户看到100%的进度\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '上传成功',\r\n\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tthis.isUploading = false\r\n\t\t\t\t\t\t\tthis.uploadProgress = 0\r\n\t\t\t\t\t\t}, 300)\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthrow new Error(result.msg || '上传失败')\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('上传图片失败：', error)\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 重置状态\r\n\t\t\t\t\tthis.isUploading = false\r\n\t\t\t\t\tthis.uploadProgress = 0\r\n\t\t\t\t\t\r\n\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: error.message || '上传图片失败',\r\n\t\t\t\t\t\ticon: 'error',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 删除图片\r\n\t\t\tdeletePic(event) {\r\n\t\t\t\tconsole.log(event)\r\n\t\t\t\tconst pics = this.formData.pic.split(',').filter(url => url)\r\n\t\t\t\tpics.splice(event.index, 1)\r\n\t\t\t\tthis.formData.pic = pics.join(',')\r\n\t\t\t\t\r\n\t\t\t\t// 更新文件列表显示\r\n\t\t\t\tthis.fileList = pics.map(url => ({\r\n\t\t\t\t\turl: this.baseUrl + url,\r\n\t\t\t\t\tstatus: 'success',\r\n\t\t\t\t\tmessage: '上传成功'\r\n\t\t\t\t}))\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 提交表单\r\n\t\t\tasync submitForm() {\r\n\t\t\t\t// 根据任务类型进行不同的表单验证\r\n\t\t\t\tif (this.currentTaskType === 'packaging') {\r\n\t\t\t\t\t// 包装任务验证\r\n\t\t\t\t\tif (!this.formData.byteNo) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请填写字节号',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.formData.goodQuantity || !this.formData.defectiveQuantity) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请填写良品数和不良品数',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst goodQuantity = Number(this.formData.goodQuantity)\r\n\t\t\t\t\tconst defectiveQuantity = Number(this.formData.defectiveQuantity)\r\n\t\t\t\t\tconst totalQuantity = goodQuantity + defectiveQuantity\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (totalQuantity > this.currentTask.assignedQuantity) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '良品数和不良品数之和不能大于指派数量',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (!this.formData.pic) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请上传图片',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (this.currentTaskType === 'shipping') {\r\n\t\t\t\t\t// 发货任务验证\r\n\t\t\t\t\tif (!this.formData.locationAddress) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请填写发货地址',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (this.currentTaskType === 'maintenance') {\r\n\t\t\t\t\t// 维修任务验证\r\n\t\t\t\t\tif (!this.formData.pic) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请上传图片',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.formData.description) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请填写文字说明',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 其他任务的原有验证逻辑\r\n\t\t\t\t\tif (!this.formData.goodQuantity || !this.formData.defectiveQuantity) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请填写良品数和不良品数',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst goodQuantity = Number(this.formData.goodQuantity)\r\n\t\t\t\t\tconst defectiveQuantity = Number(this.formData.defectiveQuantity)\r\n\t\t\t\t\tconst totalQuantity = goodQuantity + defectiveQuantity\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (totalQuantity > this.currentTask.assignedQuantity) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '良品数和不良品数之和不能大于指派数量',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (!this.formData.pic) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请上传图片',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 提交生产记录\r\n\t\t\t\t\tconst recordData = {\r\n\t\t\t\t\t\tassignmentId: this.currentTask.assignmentId,\r\n\t\t\t\t\t\tpic: this.formData.pic,\r\n\t\t\t\t\t\tbyteNo: this.formData.byteNo,\r\n\t\t\t\t\t\tlocationAddress: this.formData.locationAddress,\r\n\t\t\t\t\t\tdescription: this.formData.description,\r\n\t\t\t\t\t\trepairGoodNo: this.formData.repairGoodNo\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 根据任务类型添加相应字段\r\n\t\t\t\t\tif (this.currentTaskType === 'packaging' || this.currentTaskType === 'other') {\r\n\t\t\t\t\t\trecordData.goodQuantity = Number(this.formData.goodQuantity)\r\n\t\t\t\t\t\trecordData.defectiveQuantity = Number(this.formData.defectiveQuantity)\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (this.currentTask.recordId) {\r\n\t\t\t\t\t\t// 修改记录\r\n\t\t\t\t\t\trecordData.recordId = this.currentTask.recordId\r\n\t\t\t\t\t\tawait updateProductionRecords(recordData)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 新增记录\r\n\t\t\t\t\t\tawait addProductionRecords(recordData)\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新任务状态为已完成\r\n\t\t\t\t\tconst updateData = {\r\n\t\t\t\t\t\tassignmentId: this.currentTask.assignmentId,\r\n\t\t\t\t\t\tassignmentStatus: '2'\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 根据任务类型添加相应的上报数量字段\r\n\t\t\t\t\tif (this.currentTaskType === 'packaging' || this.currentTaskType === 'other') {\r\n\t\t\t\t\t\tupdateData.reportedGoodQuantity = Number(this.formData.goodQuantity)\r\n\t\t\t\t\t\tupdateData.reportedDefectiveQuantity = Number(this.formData.defectiveQuantity)\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tawait updateTaskAssignments(updateData)\r\n\t\t\t\t\t\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '提交成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t})\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 关闭弹窗并刷新列表\r\n\t\t\t\t\tthis.closeSubmitForm()\r\n\t\t\t\t\tthis.onRefresh()\r\n\t\t\t\t\t\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('提交失败：', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '提交失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tonSearchInput(event) {\r\n\t\t\t\tthis.searchKeyword = event.target.value;\r\n\t\t\t\t// 防抖处理，避免频繁请求\r\n\t\t\t\tif (this.searchTimer) {\r\n\t\t\t\t\tclearTimeout(this.searchTimer);\r\n\t\t\t\t}\r\n\t\t\t\tthis.searchTimer = setTimeout(() => {\r\n\t\t\t\t\tthis.performSearch();\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\t\t\tperformSearch() {\r\n\t\t\t\tthis.queryParams.pageNum = 1;\r\n\t\t\t\tthis.taskList = [];\r\n\t\t\t\tthis.getList(true);\r\n\t\t\t},\r\n\t\t\tclearSearch() {\r\n\t\t\t\tthis.searchKeyword = '';\r\n\t\t\t\t// 清除防抖定时器\r\n\t\t\t\tif (this.searchTimer) {\r\n\t\t\t\t\tclearTimeout(this.searchTimer);\r\n\t\t\t\t\tthis.searchTimer = null;\r\n\t\t\t\t}\r\n\t\t\t\t// 重新获取数据\r\n\t\t\t\tthis.queryParams.pageNum = 1;\r\n\t\t\t\tthis.taskList = [];\r\n\t\t\t\tthis.getList(true);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.task-list-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f5f7fa;\r\n\t\r\n\t.status-tabs {\r\n\t\tdisplay: flex;\r\n\t\tbackground-color: #ffffff;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tposition: sticky;\r\n\t\ttop: 0;\r\n\t\tz-index: 1;\r\n\t\t\r\n\t\t.tab-item {\r\n\t\t\tflex: 1;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #606266;\r\n\t\t\tpadding: 16rpx 0;\r\n\t\t\tposition: relative;\r\n\t\t\t\r\n\t\t\t&.active {\r\n\t\t\t\tcolor: #1e3a8a;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\r\n\t\t\t\t&::after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\theight: 4rpx;\r\n\t\t\t\t\tbackground-color: #1e3a8a;\r\n\t\t\t\t\tborder-radius: 2rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.search-container {\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tbackground-color: #ffffff;\r\n\t\tposition: sticky;\r\n\t\ttop: 100rpx;\r\n\t\tz-index: 1;\r\n\t\tborder-bottom: 1px solid #ebeef5;\r\n\t\t\r\n\t\t.search-box {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tbackground-color: #f8f9fa;\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tpadding: 16rpx 24rpx;\r\n\t\t\tborder: 1px solid #ebeef5;\r\n\t\t\ttransition: all 0.3s;\r\n\t\t\t\r\n\t\t\t&:focus-within {\r\n\t\t\t\tborder-color: #1e3a8a;\r\n\t\t\t\tbox-shadow: 0 0 0 4rpx rgba(30, 58, 138, 0.1);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.search-icon {\r\n\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t\tflex-shrink: 0;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.search-input {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t\tbackground-color: transparent;\r\n\t\t\t\tborder: none;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #2c3e50;\r\n\t\t\t\t\r\n\t\t\t\t&::placeholder {\r\n\t\t\t\t\tcolor: #909399;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.search-clear {\r\n\t\t\t\tmargin-left: 16rpx;\r\n\t\t\t\tpadding: 4rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\topacity: 0.8;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.task-list {\r\n\t\theight: 80vh;\r\n\t\tpadding: 20rpx;\r\n\t\t\r\n\t\t.task-item {\r\n\t\t\tbackground-color: #ffffff;\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tpadding: 24rpx;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\r\n\t\t\t\r\n\t\t\t.task-header {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t\r\n\t\t\t\t.task-name {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tcolor: #1e3a8a;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.task-status {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tpadding: 6rpx 20rpx;\r\n\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.status-1 {\r\n\t\t\t\t\t\tbackground-color: #ecf5ff;\r\n\t\t\t\t\t\tcolor: #409eff;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.status-2 {\r\n\t\t\t\t\t\tbackground-color: #f0f9eb;\r\n\t\t\t\t\t\tcolor: #67c23a;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.task-info {\r\n\t\t\t\t.info-row {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tmargin-bottom: 12rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.label {\r\n\t\t\t\t\t\twidth: 140rpx;\r\n\t\t\t\t\t\tcolor: #909399;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.value {\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\tcolor: #2c3e50;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.task-footer {\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\tpadding-top: 20rpx;\r\n\t\t\t\tborder-top: 1px solid #ebeef5;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: flex-end;\r\n\t\t\t\tgap: 20rpx;\r\n\t\t\t\t\r\n\t\t\t\t.submit-btn {\r\n\t\t\t\t\twidth: 200rpx;\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\tline-height: 60rpx;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t\tbackground: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);\r\n\t\t\t\t\tborder: none;\r\n\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&::after {\r\n\t\t\t\t\t\tborder: none;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&:active {\r\n\t\t\t\t\t\topacity: 0.8;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.secondary {\r\n\t\t\t\t\t\tbackground: linear-gradient(135deg, #606266 0%, #909399 100%);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.load-more {\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.empty-state {\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding: 100rpx 0;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.submit-form-container {\r\n\t\tpadding: 30rpx;\r\n\t\tbackground-color: #ffffff;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tmax-height: 80vh;\r\n\t\t\r\n\t\t.form-header {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\tpadding-bottom: 20rpx;\r\n\t\t\tborder-bottom: 1px solid #ebeef5;\r\n\t\t\tflex-shrink: 0;\r\n\t\t\t\r\n\t\t\t.form-title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #2c3e50;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.form-actions {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.form-content {\r\n\t\t\tflex: 1;\r\n\t\t\toverflow-y: auto;\r\n\t\t\tpadding-bottom: 30rpx;\r\n\t\t\t\r\n\t\t\t.form-item {\r\n\t\t\t\tmargin-bottom: 24rpx;\r\n\t\t\t\t\r\n\t\t\t\t.form-label {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #2c3e50;\r\n\t\t\t\t\tmargin-bottom: 12rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.form-input {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\tbackground-color: #f8f9fa;\r\n\t\t\t\t\tborder: 1px solid #ebeef5;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\tpadding: 0 24rpx;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #2c3e50;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.form-textarea {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tmin-height: 160rpx;\r\n\t\t\t\t\tbackground-color: #f8f9fa;\r\n\t\t\t\t\tborder: 1px solid #ebeef5;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\tpadding: 24rpx;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #2c3e50;\r\n\t\t\t\t\tline-height: 1.6;\r\n\t\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.char-count {\r\n\t\t\t\t\ttext-align: right;\r\n\t\t\t\t\tmargin-top: 8rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\ttext {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #909399;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.upload-container {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.upload-progress {\r\n\t\t\t\t\t\tmargin-top: 16rpx;\r\n\t\t\t\t\t\tpadding: 16rpx;\r\n\t\t\t\t\t\tbackground-color: rgba(30, 58, 138, 0.05);\r\n\t\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\t\tborder: 1px solid rgba(30, 58, 138, 0.1);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.progress-bar {\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\theight: 8rpx;\r\n\t\t\t\t\t\t\tbackground-color: #ebeef5;\r\n\t\t\t\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\t\tmargin-bottom: 12rpx;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.progress-fill {\r\n\t\t\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\t\t\tbackground: linear-gradient(90deg, #1e3a8a 0%, #3b82f6 100%);\r\n\t\t\t\t\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\t\t\t\t\ttransition: width 0.3s ease;\r\n\t\t\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t&::after {\r\n\t\t\t\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\t\t\t\tbackground: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\r\n\t\t\t\t\t\t\t\t\tanimation: shimmer 1.5s infinite;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.progress-text {\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tcolor: #1e3a8a;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t@keyframes shimmer {\r\n\t\t\t\t\t0% {\r\n\t\t\t\t\t\ttransform: translateX(-100%);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t100% {\r\n\t\t\t\t\t\ttransform: translateX(100%);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752425864409\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}