{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/index.vue?37bf", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/index.vue?5d34", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/index.vue?31cb", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/index.vue?cc7f", "uni-app:///pages/index.vue", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/index.vue?23c5", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/index.vue?2b85"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "queryParams", "pageSize", "pageNum", "orderProductNumberVersion", "orderList", "total", "loading", "isRefreshing", "hasMore", "searchTimer", "searchDelay", "roles", "computed", "hasMoreData", "hasPermission", "onLoad", "console", "methods", "getOrderList", "isRefresh", "res", "uni", "title", "icon", "duration", "errorMsg", "onSearchInput", "clearTimeout", "handleSearch", "loadMore", "onRefresh", "formatDate", "date", "getStatusText", "getStatusClass", "goToAddOrder", "url", "toOrderDetail", "goToEditOrder", "deleteOrder", "content", "confirmText", "confirmColor", "cancelText", "success", "resolve", "result", "mask"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpEA;AAAA;AAAA;AAAA;AAAinB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACwIroB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MACA;MACA;QAAA;MAAA;IACA;EACA;EACAC;IACAC;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,KAIA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBAEA;kBACA;oBACA;;oBAEA;oBACA;sBACAC;wBACAC;wBACAC;wBACAC;sBACA;oBACA;kBACA;oBACA;kBACA;kBAEA;kBACA;;kBAEA;kBACA;oBACAH;sBACAC;sBACAC;sBACAC;oBACA;kBACA;gBACA;kBACA;kBACAH;oBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAR;;gBAEA;gBACAS;gBACA;kBACAA,qDACA,eACA;gBACA;gBAEAJ;kBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACA;kBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MAAA;MACA;MACA;QACA;MACA;;MAEA;MACA;QACAC;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;;MAEA;MACA;QACAD;QACA;MACA;MAEA;MACA;MACA;IACA;IAEA;IACAE;MACA;MAEA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;QACA;QACA;UACA;UACAC;QACA;UACA;UACAA;QACA;;QAEA;QACA;UACA;QACA;QAEA;QACA;QACA;QAEA;MACA;QACAhB;QACA;MACA;IACA;IAEA;IACAiB;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACAd;UACAC;UACAC;QACA;QACA;MACA;MAEAF;QACAe;MACA;IACA;IAEA;IACAC;MACA;QACAhB;UACAC;UACAC;QACA;QACA;MACA;MAEAF;QACAe;MACA;IACA;IAEA;IACAE;MACA;QACAjB;UACAC;UACAC;QACA;QACA;MACA;MAEAF;QACAe;MACA;IACA;IAEA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAlB;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKA;kBACAF;oBACAC;oBACAkB;oBACAC;oBACAC;oBACAC;oBACAC;sBACAC;oBACA;kBACA;gBACA;cAAA;gBAXAC;gBAAA,KAaAA;kBAAA;kBAAA;gBAAA;gBACAzB;kBACAC;kBACAyB;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAEA1B;gBACAA;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;gBACAL;gBACAK;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrcA;AAAA;AAAA;AAAA;AAAwqC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACA5rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2a183b29&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=2a183b29&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-empty/u-empty\" */ \"@/uni_modules/uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.hasPermission && _vm.hasPermission ? _vm.orderList.length : null\n  var g1 = _vm.hasPermission ? _vm.orderList.length === 0 && !_vm.loading : null\n  var l0 =\n    _vm.hasPermission && !g1\n      ? _vm.__map(_vm.orderList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.getStatusText(item.status)\n          var m1 = _vm.formatDate(item.overallPlannedStartTime)\n          var m2 = _vm.formatDate(item.overallPlannedEndTime)\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n          }\n        })\n      : null\n  var g2 =\n    _vm.hasPermission && !g1 ? !_vm.hasMore && _vm.orderList.length > 0 : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"order-list-container\">\n    <!-- 权限检查：有权限的用户显示正常页面 -->\n    <template v-if=\"hasPermission\">\n      <!-- 悬浮按钮 -->\n      <view class=\"fab-button\" @click=\"goToAddOrder\">\n        <u-icon name=\"plus\" size=\"50rpx\" color=\"#ffffff\"></u-icon>\n      </view>\n      <!-- 搜索区域 -->\n      <view class=\"search-section\">\n        <view class=\"search-box\">\n          <u-icon name=\"search\" size=\"36rpx\" color=\"#8c9fba\"></u-icon>\n          <input \n            type=\"text\" \n            v-model=\"queryParams.orderProductNumberVersion\" \n            placeholder=\"请输入产品编号版本搜索\" \n            confirm-type=\"search\"\n            @input=\"onSearchInput\"\n            @confirm=\"handleSearch\"\n          />\n          <text class=\"search-btn\" @click=\"handleSearch\">搜索</text>\n        </view>\n      </view>\n      \n      <!-- 统计信息区域 -->\n      <view class=\"stats-section\" v-if=\"hasPermission\">\n        <view class=\"stats-item\">\n          <text class=\"stats-label\">工单总量：</text>\n          <text class=\"stats-value\">{{ total || 0 }}</text>\n        </view>\n        <view class=\"stats-item\">\n          <text class=\"stats-label\">当前显示：</text>\n          <text class=\"stats-value\">{{ orderList.length }}</text>\n        </view>\n      </view>\n      \n      <!-- 列表区域 -->\n      <scroll-view \n        class=\"order-list-scroll\" \n        :scroll-y=\"true\" \n        @scrolltolower=\"loadMore\" \n        :enable-back-to-top=\"true\"\n        :refresher-enabled=\"true\"\n        :refresher-triggered=\"isRefreshing\"\n        @refresherrefresh=\"onRefresh\"\n      >\n        <!-- 列表为空时显示 -->\n        <view class=\"empty-container\" v-if=\"orderList.length === 0 && !loading\">\n          <u-empty mode=\"data\" text=\"暂无工单数据\"></u-empty>\n        </view>\n        \n        <!-- 工单列表 -->\n        <view class=\"order-list\" v-else>\n          <view class=\"order-card\" v-for=\"(item, index) in orderList\" :key=\"index\" @click=\"toOrderDetail(item)\">\n            <view class=\"order-header\">\n              <view class=\"order-no\">工单编号：{{ item.workOrderNo }}</view>\n              <view class=\"order-status\" \n                    :class=\"{\n                      'status-pending': item.status === '0',\n                      'status-processing': item.status === '1',\n                      'status-completed': item.status === '2'\n                    }\">\n                {{ getStatusText(item.status) }}\n              </view>\n            </view>\n            \n            <view class=\"order-content\">\n              <view class=\"info-row\">\n                <text class=\"info-label\">产品名称：</text>\n                <text class=\"info-value\">{{ item.productName }}</text>\n              </view>\n              <view class=\"info-row\" v-if=\"item.orderProductNumberVersion\">\n                <text class=\"info-label\">产品编号：</text>\n                <text class=\"info-value\">{{ item.orderProductNumberVersion }}</text>\n              </view>\n              <view class=\"info-row\">\n                <text class=\"info-label\">计划数量：</text>\n                <text class=\"info-value\">{{ item.totalPlannedQuantity }}</text>\n              </view>\n              <view class=\"info-row\">\n                <text class=\"info-label\">计划开始：</text>\n                <text class=\"info-value\">{{ formatDate(item.overallPlannedStartTime) }}</text>\n              </view>\n              <view class=\"info-row\">\n                <text class=\"info-label\">计划结束：</text>\n                <text class=\"info-value\">{{ formatDate(item.overallPlannedEndTime) }}</text>\n              </view>\n              <view class=\"info-row\" v-if=\"item.remark\">\n                <text class=\"info-label\">备注：</text>\n                <text class=\"info-value remark-text\">{{ item.remark }}</text>\n              </view>\n              \n              <!-- 操作按钮区域 -->\n              <view class=\"action-buttons\">\n                <button class=\"delete-btn\" @click.stop=\"deleteOrder(item)\">\n                  删除\n                </button>\n                <button class=\"edit-btn\" @click.stop=\"goToEditOrder(item)\">\n                  修改\n                </button>\n              </view>\n            </view>\n          </view>\n          \n          <!-- 加载更多 -->\n          <view class=\"loading-more\" v-if=\"loading\">\n            <u-loading mode=\"circle\" size=\"24\"></u-loading>\n            <text class=\"loading-text\">加载中...</text>\n          </view>\n          \n          <!-- 没有更多数据 -->\n          <view class=\"no-more\" v-if=\"!hasMore && orderList.length > 0\">\n            <text>没有更多数据了</text>\n          </view>\n        </view>\n      </scroll-view>\n    </template>\n    \n    <!-- 无权限时显示 -->\n    <template v-else>\n      <view class=\"no-permission-container\">\n        <view class=\"no-permission-content\">\n          <view class=\"permission-icon\">\n            <u-icon name=\"lock\" size=\"120rpx\" color=\"#909399\"></u-icon>\n          </view>\n          <view class=\"permission-title\">访问受限</view>\n          <view class=\"permission-description\">\n            抱歉，您暂无权限访问工单管理页面\n          </view>\n        </view>\n      </view>\n    </template>\n  </view>\n</template>\n\n<script>\nimport { listOrders,delOrders } from \"@/api/work/orders.js\"\n\nexport default {\n      data() {\n        return {\n          queryParams: {\n            pageSize: 5,\n            pageNum: 1,\n            orderProductNumberVersion: null\n          },\n          orderList: [],\n          total: 0,\n          loading: false,\n          isRefreshing: false,\n          hasMore: true,\n          searchTimer: null, // 用于防抖处理\n          searchDelay: 500, // 搜索延迟时间（毫秒）\n          roles: this.$store.state.user.roles\n        }\n      },\n  computed: {\n    // 是否有更多数据\n    hasMoreData() {\n      return this.orderList.length < this.total;\n    },\n    \n    // 权限检查：是否包含admin、dispatcher或repairman角色\n    hasPermission() {\n      if (!this.roles || !Array.isArray(this.roles)) {\n        return false;\n      }\n      const allowedRoles = ['admin', 'dispatcher', 'repairman'];\n      return this.roles.some(role => allowedRoles.includes(role));\n    }\n  },\n  onLoad() {\n\t  console.log(this.roles);\n    // 只有有权限的用户才获取数据\n    if (this.hasPermission) {\n      this.getOrderList();\n    }\n  },\n  methods: {\n    // 获取工单列表\n    async getOrderList(isRefresh = false) {\n      // 权限检查\n      if (!this.hasPermission) {\n        return;\n      }\n      \n      if (this.loading) return;\n      \n      this.loading = true;\n      \n      try {\n        const res = await listOrders(this.queryParams);\n        \n        if (res && res.rows) {\n          if (isRefresh) {\n            this.orderList = res.rows;\n            \n            // 如果是刷新操作，显示成功提示\n            if (this.isRefreshing) {\n              uni.showToast({\n                title: '刷新成功',\n                icon: 'success',\n                duration: 1500\n              });\n            }\n          } else {\n            this.orderList = [...this.orderList, ...res.rows];\n          }\n          \n          this.total = res.total;\n          this.hasMore = this.orderList.length < this.total;\n          \n          // 如果没有数据，显示提示\n          if (isRefresh && this.orderList.length === 0) {\n            uni.showToast({\n              title: '暂无工单数据',\n              icon: 'none',\n              duration: 2000\n            });\n          }\n        } else {\n          // 处理响应格式不符合预期的情况\n          uni.showToast({\n            title: '数据格式异常',\n            icon: 'none',\n            duration: 2000\n          });\n        }\n      } catch (error) {\n        console.error('获取工单列表失败', error);\n        \n        // 提供更具体的错误信息\n        let errorMsg = '获取数据失败';\n        if (error.message) {\n          errorMsg = error.message.includes('timeout') \n            ? '请求超时，请检查网络' \n            : '获取数据失败: ' + error.message.substring(0, 20);\n        }\n        \n        uni.showToast({\n          title: errorMsg,\n          icon: 'none',\n          duration: 3000\n        });\n      } finally {\n        this.loading = false;\n        if (this.isRefreshing) {\n          this.isRefreshing = false;\n        }\n      }\n    },\n    \n    // 处理搜索输入（防抖）\n    onSearchInput() {\n      // 权限检查\n      if (!this.hasPermission) {\n        return;\n      }\n      \n      // 清除之前的定时器\n      if (this.searchTimer) {\n        clearTimeout(this.searchTimer);\n      }\n      \n      // 设置新的定时器\n      this.searchTimer = setTimeout(() => {\n        this.handleSearch();\n      }, this.searchDelay);\n    },\n    \n    // 搜索\n    handleSearch() {\n      // 权限检查\n      if (!this.hasPermission) {\n        return;\n      }\n      \n      // 清除可能存在的定时器\n      if (this.searchTimer) {\n        clearTimeout(this.searchTimer);\n        this.searchTimer = null;\n      }\n      \n      this.queryParams.pageNum = 1;\n      this.orderList = [];\n      this.getOrderList(true);\n    },\n    \n    // 加载更多\n    loadMore() {\n      if (!this.hasPermission || !this.hasMore || this.loading) return;\n      \n      this.queryParams.pageNum += 1;\n      this.getOrderList();\n    },\n    \n    // 下拉刷新\n    onRefresh() {\n      if (!this.hasPermission) return;\n      \n      this.isRefreshing = true;\n      this.queryParams.pageNum = 1;\n      this.getOrderList(true);\n    },\n    \n    // 格式化日期\n    formatDate(dateString) {\n      if (!dateString) return '--';\n      \n      try {\n        // 处理可能的日期格式\n        let date;\n        if (dateString.includes('T')) {\n          // 处理ISO格式的日期\n          date = new Date(dateString);\n        } else {\n          // 处理普通日期字符串\n          date = new Date(dateString.replace(/-/g, '/'));\n        }\n        \n        // 检查日期是否有效\n        if (isNaN(date.getTime())) {\n          return '--';\n        }\n        \n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        \n        return `${year}-${month}-${day}`;\n      } catch (error) {\n        console.error('日期格式化错误:', error);\n        return '--';\n      }\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        '0': '未开始',\n        '1': '处理中',\n        '2': '已完成'\n      };\n      return statusMap[status] || '未知状态';\n    },\n    \n    // 获取状态样式类\n    getStatusClass(status) {\n      const classMap = {\n        '0': 'status-pending',\n        '1': 'status-processing',\n        '2': 'status-completed'\n      };\n      return classMap[status] || '';\n    },\n    \n    // 跳转到新增工单页面\n    goToAddOrder() {\n      if (!this.hasPermission) {\n        uni.showToast({\n          title: '无权限执行此操作',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      uni.navigateTo({\n        url:\"/pages/addOrder/addOrder\"\n      });\n    },\n\t\n\t//跳转详情页\n\ttoOrderDetail(item){\n      if (!this.hasPermission) {\n        uni.showToast({\n          title: '无权限执行此操作',\n          icon: 'none'\n        });\n        return;\n      }\n      \n\t\tuni.navigateTo({\n\t\t\turl:\"/pages/orderDetail/orderDetail?id=\"+item.workOrderId\n\t\t})\n\t},\n\t\n\t//跳转到编辑工单页面\n\tgoToEditOrder(item) {\n      if (!this.hasPermission) {\n        uni.showToast({\n          title: '无权限执行此操作',\n          icon: 'none'\n        });\n        return;\n      }\n      \n\t\tuni.navigateTo({\n\t\t\turl:\"/pages/addOrder/addOrder?id=\"+item.workOrderId\n\t\t})\n\t},\n\t\n\t//删除工单\n\tasync deleteOrder(item) {\n      if (!this.hasPermission) {\n        uni.showToast({\n          title: '无权限执行此操作',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      try {\n        const result = await new Promise((resolve) => {\n          uni.showModal({\n            title: '确认删除',\n            content: `确定要删除工单 \"${item.workOrderNo}\" 吗？删除后无法恢复。`,\n            confirmText: '删除',\n            confirmColor: '#ff5252',\n            cancelText: '取消',\n            success: (res) => {\n              resolve(res.confirm);\n            }\n          });\n        });\n        \n        if (result) {\n          uni.showLoading({\n            title: '删除中...',\n            mask: true\n          });\n          \n          await delOrders(item.workOrderId);\n          \n          uni.hideLoading();\n          uni.showToast({\n            title: '删除成功',\n            icon: 'success'\n          });\n          \n          // 刷新列表\n          this.onRefresh();\n        }\n      } catch (error) {\n        uni.hideLoading();\n        console.error('删除工单失败：', error);\n        uni.showToast({\n          title: '删除失败: ' + (error.message || '未知错误'),\n          icon: 'none',\n          duration: 3000\n        });\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.order-list-container {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #f5f7fa;\n}\n\n/* 无权限页面样式 */\n.no-permission-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100vh;\n  background-color: #f5f7fa;\n  \n  .no-permission-content {\n    text-align: center;\n    padding: 60rpx 40rpx;\n    \n    .permission-icon {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      margin-bottom: 40rpx;\n    }\n    \n    .permission-title {\n      font-size: 36rpx;\n      font-weight: 600;\n      color: #2c3e50;\n      margin-bottom: 20rpx;\n    }\n    \n    .permission-description {\n      font-size: 28rpx;\n      color: #606266;\n      line-height: 1.6;\n    }\n  }\n}\n\n/* 搜索区域样式 */\n.search-section {\n  padding: 20rpx 30rpx;\n  background-color: #ffffff;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  \n  .search-box {\n    display: flex;\n    align-items: center;\n    height: 80rpx;\n    background-color: #f5f7fa;\n    border-radius: 40rpx;\n    padding: 0 30rpx;\n    \n    .u-icon {\n      margin-right: 20rpx;\n    }\n    \n    input {\n      flex: 1;\n      height: 80rpx;\n      font-size: 28rpx;\n      color: #333;\n    }\n    \n    .search-btn {\n      padding: 0 20rpx;\n      height: 60rpx;\n      line-height: 60rpx;\n      background-color: #1e3a8a;\n      color: #ffffff;\n      font-size: 26rpx;\n      border-radius: 30rpx;\n    }\n  }\n}\n\n/* 列表区域样式 */\n.order-list-scroll {\n  flex: 1;\n  width: 100%;\n  height: 75vh;\n}\n\n.order-list {\n  padding: 20rpx;\n}\n\n/* 工单卡片样式 */\n.order-card {\n  background-color: #ffffff;\n  border-radius: 12rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n  overflow: hidden;\n  \n  .order-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 20rpx 30rpx;\n    border-bottom: 2rpx solid #f0f2f5;\n    \n    .order-no {\n      font-size: 28rpx;\n      font-weight: 500;\n      color: #333;\n    }\n    \n    .order-status {\n      padding: 6rpx 20rpx;\n      border-radius: 20rpx;\n      font-size: 24rpx;\n      \n      &.status-pending {\n        background-color: #e6f7ff;\n        color: #1890ff;\n      }\n      \n      &.status-processing {\n        background-color: #fff7e6;\n        color: #fa8c16;\n      }\n      \n      &.status-completed {\n        background-color: #f6ffed;\n        color: #52c41a;\n      }\n    }\n  }\n  \n  .order-content {\n    padding: 20rpx 30rpx;\n    \n    .info-row {\n      display: flex;\n      margin-bottom: 16rpx;\n      \n      &:last-child {\n        margin-bottom: 0;\n      }\n      \n      .info-label {\n        width: 160rpx;\n        font-size: 26rpx;\n        color: #666;\n      }\n      \n      .info-value {\n        flex: 1;\n        font-size: 26rpx;\n        color: #333;\n      }\n      \n      .remark-text {\n        color: #8c9fba;\n      }\n    }\n    \n    /* 操作按钮区域 */\n    .action-buttons {\n      display: flex;\n      justify-content: flex-end;\n      align-items: center;\n      gap: 20rpx;\n      margin-top: 24rpx;\n      padding-top: 20rpx;\n      border-top: 1px solid #f0f2f5;\n      \n      .edit-btn, .delete-btn {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        padding: 12rpx 24rpx;\n        border: none;\n        border-radius: 8rpx;\n        font-size: 26rpx;\n        font-weight: 500;\n        min-width: 100rpx;\n        height: 60rpx;\n        color: #ffffff;\n        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);\n        transition: all 0.3s ease;\n        position: relative;\n        overflow: hidden;\n        \n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: -100%;\n          width: 100%;\n          height: 100%;\n          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n          transition: left 0.5s ease;\n        }\n        \n        &:active {\n          transform: translateY(1rpx);\n          \n          &::before {\n            left: 100%;\n          }\n        }\n      }\n      \n      .edit-btn {\n        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);\n        \n        &:active {\n          box-shadow: 0 1rpx 4rpx rgba(30, 58, 138, 0.3);\n        }\n      }\n      \n      .delete-btn {\n        background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);\n        \n        &:active {\n          box-shadow: 0 1rpx 4rpx rgba(220, 38, 38, 0.3);\n        }\n      }\n    }\n  }\n}\n\n/* 空数据样式 */\n.empty-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n  \n  .empty-image {\n    width: 200rpx;\n    height: 200rpx;\n    margin-bottom: 30rpx;\n  }\n  \n  .empty-text {\n    font-size: 28rpx;\n    color: #8c9fba;\n  }\n}\n\n/* 加载更多和无更多数据样式 */\n.loading-more, .no-more {\n  text-align: center;\n  padding: 30rpx 0;\n  font-size: 26rpx;\n  color: #8c9fba;\n}\n\n/* 悬浮按钮样式 */\n.fab-button {\n  position: fixed;\n  right: 40rpx;\n  bottom: 40rpx;\n  width: 100rpx;\n  height: 100rpx;\n  background-color: #1e3a8a;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4rpx 16rpx rgba(30, 58, 138, 0.4);\n  z-index: 100;\n  transition: all 0.3s;\n  \n  &:active {\n    transform: scale(0.95);\n  }\n  \n  /* 使用u-icon组件，不需要额外的图标样式 */\n}\n\n/* 统计信息区域样式 */\n.stats-section {\n  padding: 20rpx 30rpx;\n  background-color: #ffffff;\n  border-bottom: 1px solid #ebeef5;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  \n  .stats-item {\n    display: flex;\n    align-items: center;\n    \n    .stats-label {\n      font-size: 26rpx;\n      color: #606266;\n      margin-right: 8rpx;\n    }\n    \n    .stats-value {\n      font-size: 28rpx;\n      color: #1e3a8a;\n      font-weight: 600;\n    }\n  }\n}\n</style>", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752425864428\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}