import request from '@/utils/request'

// 查询生产提报记录列表
export function listProductionRecords(query) {
  return request({
    url: '/work/productionRecords/list',
    method: 'get',
    params: query
  })
}

// 查询生产提报记录详细
export function getProductionRecords(recordId) {
  return request({
    url: '/work/productionRecords/' + recordId,
    method: 'get'
  })
}

// 新增生产提报记录
export function addProductionRecords(data) {
  return request({
    url: '/work/productionRecords',
    method: 'post',
    data: data
  })
}

// 修改生产提报记录
export function updateProductionRecords(data) {
  return request({
    url: '/work/productionRecords',
    method: 'put',
    data: data
  })
}

// 删除生产提报记录
export function delProductionRecords(recordId) {
  return request({
    url: '/work/productionRecords/' + recordId,
    method: 'delete'
  })
}
