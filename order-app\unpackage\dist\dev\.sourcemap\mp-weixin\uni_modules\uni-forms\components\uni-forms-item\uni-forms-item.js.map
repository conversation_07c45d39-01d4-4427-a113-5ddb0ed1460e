{"version": 3, "sources": ["webpack:///D:/项目/工单小程序/work_order/order-app/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue?6290", "webpack:///D:/项目/工单小程序/work_order/order-app/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue?b5d8", "webpack:///D:/项目/工单小程序/work_order/order-app/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue?0e34", "webpack:///D:/项目/工单小程序/work_order/order-app/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue?aad4", "uni-app:///uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue", "webpack:///D:/项目/工单小程序/work_order/order-app/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue?3c56", "webpack:///D:/项目/工单小程序/work_order/order-app/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue?0a5e"], "names": ["name", "options", "virtualHost", "provide", "uniFormItem", "inject", "form", "from", "default", "props", "rules", "type", "required", "label", "labelWidth", "labelAlign", "errorMessage", "leftIcon", "iconColor", "data", "errMsg", "userRules", "localLabelAlign", "localLabelWidth", "localLabelPos", "border", "isFirstBorder", "computed", "msg", "watch", "created", "immediate", "destroyed", "methods", "setRules", "setValue", "onFieldChange", "formtrigger", "formData", "localData", "errShowType", "validate<PERSON><PERSON><PERSON>", "validate<PERSON><PERSON>ger", "_isRequiredField", "_realName", "value", "ruleLen", "isRequiredField", "result", "uni", "title", "icon", "content", "init", "validator", "formRules", "childrens", "_getDataValue", "_setDataValue", "unInit", "itemSetValue", "clearValidate", "_isRequired", "_justifyContent", "_labelWidthUnit", "_labelPosition", "isTrigger", "num2px"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACc;;;AAG3E;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAuqB,CAAgB,wnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgC3rB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtBA,gBAwBA;EACAA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACAH;QACA;MACA;IACA;IACA;IACAR;MACAW;MACAH;IACA;IACAI;MACAD;MACAH;IACA;IACAK;MACAF;MACAH;IACA;IACA;IACAM;MACAH;MACAH;IACA;IACA;IACAO;MACAJ;MACAH;IACA;IACA;IACAQ;MACAL;MACAH;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAS;IACAC;MACAP;MACAH;IACA;EACA;EACAW;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;;MAEA;IAEA;IACA;MACA;MACA;IAEA;IACA;MACA;MACA;IACA;IACA,iDAEA;EACA;EACAC;IAAA;IACA;IACA;MACA;;MAOA;MACA,YACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;UACA;UACA;QACA;MACA;QACAC;MACA,EACA;IACA;EAEA;EAEAC;IACA;IACA;EACA;EAQAC;IACA;AACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;IACA;IACA;IACAC;MACA;IAAA,CACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,cASA,aAPAC,iCACAC,mCACAC,uCACAC,2CACAC,+CACAC,iDACAC;gBAEA5C;gBACA;kBACA6C;gBACA;gBACA;gBACA;;gBAEA;gBACAC;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;gBACAC;gBACAC,eACA;gBAAA,MACAN;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA,kEACA1C,cAEAsC,SACA;cAAA;gBAJAU;gBAMA;gBACA;kBACAA;gBACA;;gBAEA;gBACA;kBACA;oBACA;oBACA;kBACA;kBACA;oBACAC;sBACAC;sBACAC;oBACA;kBACA;kBACA;oBACAF;sBACAC;sBACAE;oBACA;kBACA;gBACA;kBACA;gBACA;gBACA;gBACAX;gBAAA;gBAAA;cAAA;gBAEA;cAAA;gBAAA,iCAEAO;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;AACA;AACA;IACAK;MAAA;MACA,WAUA;QATAC;QACAC;QACAC;QACAlB;QACAC;QACAK;QACA9B;QACA2C;QACAC;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;QACA;QACA;MACA;;MAEA;MACA;QACA;UACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACAH;UACA7C;QACA;QACA4C;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAK;MAAA;MACA;QACA,iBAIA;UAHAH;UACAlB;UACAM;QAEAY;UACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAI;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA,IACAhD,aACA,UADAA;QAEA;QACA;QACA;QACA;MACA;MACA;IACA;IACA;IACAiD;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IAEA;IAEA;AACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;UACA;YACA;cACA;YACA;YACA;UACA;UACA;QACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACvcA;AAAA;AAAA;AAAA;AAAkwC,CAAgB,umCAAG,EAAC,C;;;;;;;;;;;ACAtxC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-forms-item.vue?vue&type=template&id=61dfc0d0&\"\nvar renderjs\nimport script from \"./uni-forms-item.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-forms-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-forms-item.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-forms-item.vue?vue&type=template&id=61dfc0d0&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-forms-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-forms-item.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-forms-item\"\r\n\t\t:class=\"['is-direction-' + localLabelPos ,border?'uni-forms-item--border':'' ,border && isFirstBorder?'is-first-border':'']\">\r\n\t\t<slot name=\"label\">\r\n\t\t\t<view class=\"uni-forms-item__label\" :class=\"{'no-label':!label && !required}\"\r\n\t\t\t\t:style=\"{width:localLabelWidth,justifyContent: localLabelAlign}\">\r\n\t\t\t\t<text v-if=\"required\" class=\"is-required\">*</text>\r\n\t\t\t\t<text>{{label}}</text>\r\n\t\t\t</view>\r\n\t\t</slot>\r\n\t\t<!-- #ifndef APP-NVUE -->\r\n\t\t<view class=\"uni-forms-item__content\">\r\n\t\t\t<slot></slot>\r\n\t\t\t<view class=\"uni-forms-item__error\" :class=\"{'msg--active':msg}\">\r\n\t\t\t\t<text>{{msg}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t<view class=\"uni-forms-item__nuve-content\">\r\n\t\t\t<view class=\"uni-forms-item__content\">\r\n\t\t\t\t<slot></slot>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"uni-forms-item__error\" :class=\"{'msg--active':msg}\">\r\n\t\t\t\t<text class=\"error-text\">{{msg}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * uni-fomrs-item 表单子组件\r\n\t * @description uni-fomrs-item 表单子组件，提供了基础布局已经校验能力\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=2773\r\n\t * @property {Boolean} required 是否必填，左边显示红色\"*\"号\r\n\t * @property {String } \tlabel \t\t\t\t输入框左边的文字提示\r\n\t * @property {Number } \tlabelWidth \t\t\tlabel的宽度，单位px（默认65）\r\n\t * @property {String } \tlabelAlign = [left|center|right] label的文字对齐方式（默认left）\r\n\t * \t@value left\t\tlabel 左侧显示\r\n\t * \t@value center\tlabel 居中\r\n\t * \t@value right\tlabel 右侧对齐\r\n\t * @property {String } \terrorMessage \t\t显示的错误提示内容，如果为空字符串或者false，则不显示错误信息\r\n\t * @property {String } \tname \t\t\t\t表单域的属性名，在使用校验规则时必填\r\n\t * @property {String } \tleftIcon \t\t\t【1.4.0废弃】label左边的图标，限 uni-ui 的图标名称\r\n\t * @property {String } \ticonColor \t\t【1.4.0废弃】左边通过icon配置的图标的颜色（默认#606266）\r\n\t * @property {String} validateTrigger = [bind|submit|blur]\t【1.4.0废弃】校验触发器方式 默认 submit\r\n\t * \t@value bind \t发生变化时触发\r\n\t * \t@value submit 提交时触发\r\n\t * \t@value blur \t失去焦点触发\r\n\t * @property {String } \tlabelPosition = [top|left] 【1.4.0废弃】label的文字的位置（默认left）\r\n\t * \t@value top\t顶部显示 label\r\n\t * \t@value left\t左侧显示 label\r\n\t */\r\n\r\n\texport default {\r\n\t\tname: 'uniFormsItem',\r\n\t\toptions: {\r\n\t\t\tvirtualHost: true\r\n\t\t},\r\n\t\tprovide() {\r\n\t\t\treturn {\r\n\t\t\t\tuniFormItem: this\r\n\t\t\t}\r\n\t\t},\r\n\t\tinject: {\r\n\t\t\tform: {\r\n\t\t\t\tfrom: 'uniForm',\r\n\t\t\t\tdefault: null\r\n\t\t\t},\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\t// 表单校验规则\r\n\t\t\trules: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 表单域的属性名，在使用校验规则时必填\r\n\t\t\tname: {\r\n\t\t\t\ttype: [String, Array],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\trequired: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tlabel: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// label的宽度 ，默认 80\r\n\t\t\tlabelWidth: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// label 居中方式，默认 left 取值 left/center/right\r\n\t\t\tlabelAlign: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 强制显示错误信息\r\n\t\t\terrorMessage: {\r\n\t\t\t\ttype: [String, Boolean],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 1.4.0 弃用，统一使用 form 的校验时机\r\n\t\t\t// validateTrigger: {\r\n\t\t\t// \ttype: String,\r\n\t\t\t// \tdefault: ''\r\n\t\t\t// },\r\n\t\t\t// 1.4.0 弃用，统一使用 form 的label 位置\r\n\t\t\t// labelPosition: {\r\n\t\t\t// \ttype: String,\r\n\t\t\t// \tdefault: ''\r\n\t\t\t// },\r\n\t\t\t// 1.4.0 以下属性已经废弃，请使用  #label 插槽代替\r\n\t\t\tleftIcon: String,\r\n\t\t\ticonColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#606266'\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\terrMsg: '',\r\n\t\t\t\tuserRules: null,\r\n\t\t\t\tlocalLabelAlign: 'left',\r\n\t\t\t\tlocalLabelWidth: '65px',\r\n\t\t\t\tlocalLabelPos: 'left',\r\n\t\t\t\tborder: false,\r\n\t\t\t\tisFirstBorder: false,\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 处理错误信息\r\n\t\t\tmsg() {\r\n\t\t\t\treturn this.errorMessage || this.errMsg;\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// 规则发生变化通知子组件更新\r\n\t\t\t'form.formRules'(val) {\r\n\t\t\t\t// TODO 处理头条vue3 watch不生效的问题\r\n\t\t\t\t// #ifndef MP-TOUTIAO\r\n\t\t\t\tthis.init()\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t'form.labelWidth'(val) {\r\n\t\t\t\t// 宽度\r\n\t\t\t\tthis.localLabelWidth = this._labelWidthUnit(val)\r\n\r\n\t\t\t},\r\n\t\t\t'form.labelPosition'(val) {\r\n\t\t\t\t// 标签位置\r\n\t\t\t\tthis.localLabelPos = this._labelPosition()\r\n\t\t\t},\r\n\t\t\t'form.labelAlign'(val) {\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.init(true)\r\n\t\t\tif (this.name && this.form) {\r\n\t\t\t\t// TODO 处理头条vue3 watch不生效的问题\r\n\t\t\t\t// #ifdef MP-TOUTIAO\r\n\t\t\t\tthis.$watch('form.formRules', () => {\r\n\t\t\t\t\tthis.init()\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// 监听变化\r\n\t\t\t\tthis.$watch(\r\n\t\t\t\t\t() => {\r\n\t\t\t\t\t\tconst val = this.form._getDataValue(this.name, this.form.localData)\r\n\t\t\t\t\t\treturn val\r\n\t\t\t\t\t},\r\n\t\t\t\t\t(value, oldVal) => {\r\n\t\t\t\t\t\tconst isEqual = this.form._isEqual(value, oldVal)\r\n\t\t\t\t\t\t// 简单判断前后值的变化，只有发生变化才会发生校验\r\n\t\t\t\t\t\t// TODO  如果 oldVal = undefined ，那么大概率是源数据里没有值导致 ，这个情况不哦校验 ,可能不严谨 ，需要在做观察\r\n\t\t\t\t\t\t// fix by mehaotian 暂时取消 && oldVal !== undefined ，如果formData 中不存在，可能会不校验\r\n\t\t\t\t\t\tif (!isEqual) {\r\n\t\t\t\t\t\t\tconst val = this.itemSetValue(value)\r\n\t\t\t\t\t\t\tthis.onFieldChange(val, false)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\timmediate: false\r\n\t\t\t\t\t}\r\n\t\t\t\t);\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\t// #ifndef VUE3\r\n\t\tdestroyed() {\r\n\t\t\tif (this.__isUnmounted) return\r\n\t\t\tthis.unInit()\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifdef VUE3\r\n\t\tunmounted() {\r\n\t\t\tthis.__isUnmounted = true\r\n\t\t\tthis.unInit()\r\n\t\t},\r\n\t\t// #endif\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 外部调用方法\r\n\t\t\t * 设置规则 ，主要用于小程序自定义检验规则\r\n\t\t\t * @param {Array} rules 规则源数据\r\n\t\t\t */\r\n\t\t\tsetRules(rules = null) {\r\n\t\t\t\tthis.userRules = rules\r\n\t\t\t\tthis.init(false)\r\n\t\t\t},\r\n\t\t\t// 兼容老版本表单组件\r\n\t\t\tsetValue() {\r\n\t\t\t\t// console.log('setValue 方法已经弃用，请使用最新版本的 uni-forms 表单组件以及其他关联组件。');\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 外部调用方法\r\n\t\t\t * 校验数据\r\n\t\t\t * @param {any} value 需要校验的数据\r\n\t\t\t * @param {boolean} 是否立即校验\r\n\t\t\t * @return {Array|null} 校验内容\r\n\t\t\t */\r\n\t\t\tasync onFieldChange(value, formtrigger = true) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tformData,\r\n\t\t\t\t\tlocalData,\r\n\t\t\t\t\terrShowType,\r\n\t\t\t\t\tvalidateCheck,\r\n\t\t\t\t\tvalidateTrigger,\r\n\t\t\t\t\t_isRequiredField,\r\n\t\t\t\t\t_realName\r\n\t\t\t\t} = this.form\r\n\t\t\t\tconst name = _realName(this.name)\r\n\t\t\t\tif (!value) {\r\n\t\t\t\t\tvalue = this.form.formData[name]\r\n\t\t\t\t}\r\n\t\t\t\t// fixd by mehaotian 不在校验前清空信息，解决闪屏的问题\r\n\t\t\t\t// this.errMsg = '';\r\n\r\n\t\t\t\t// fix by mehaotian 解决没有检验规则的情况下，抛出错误的问题\r\n\t\t\t\tconst ruleLen = this.itemRules.rules && this.itemRules.rules.length\r\n\t\t\t\tif (!this.validator || !ruleLen || ruleLen === 0) return;\r\n\r\n\t\t\t\t// 检验时机\r\n\t\t\t\t// let trigger = this.isTrigger(this.itemRules.validateTrigger, this.validateTrigger, validateTrigger);\r\n\t\t\t\tconst isRequiredField = _isRequiredField(this.itemRules.rules || []);\r\n\t\t\t\tlet result = null;\r\n\t\t\t\t// 只有等于 bind 时 ，才能开启时实校验\r\n\t\t\t\tif (validateTrigger === 'bind' || formtrigger) {\r\n\t\t\t\t\t// 校验当前表单项\r\n\t\t\t\t\tresult = await this.validator.validateUpdate({\r\n\t\t\t\t\t\t\t[name]: value\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tformData\r\n\t\t\t\t\t);\r\n\r\n\t\t\t\t\t// 判断是否必填,非必填，不填不校验，填写才校验 ,暂时只处理 undefined  和空的情况\r\n\t\t\t\t\tif (!isRequiredField && (value === undefined || value === '')) {\r\n\t\t\t\t\t\tresult = null;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 判断错误信息显示类型\r\n\t\t\t\t\tif (result && result.errorMessage) {\r\n\t\t\t\t\t\tif (errShowType === 'undertext') {\r\n\t\t\t\t\t\t\t// 获取错误信息\r\n\t\t\t\t\t\t\tthis.errMsg = !result ? '' : result.errorMessage;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (errShowType === 'toast') {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: result.errorMessage || '校验错误',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (errShowType === 'modal') {\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\t\tcontent: result.errorMessage || '校验错误'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.errMsg = ''\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 通知 form 组件更新事件\r\n\t\t\t\t\tvalidateCheck(result ? result : null)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.errMsg = ''\r\n\t\t\t\t}\r\n\t\t\t\treturn result ? result : null;\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 初始组件数据\r\n\t\t\t */\r\n\t\t\tinit(type = false) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tvalidator,\r\n\t\t\t\t\tformRules,\r\n\t\t\t\t\tchildrens,\r\n\t\t\t\t\tformData,\r\n\t\t\t\t\tlocalData,\r\n\t\t\t\t\t_realName,\r\n\t\t\t\t\tlabelWidth,\r\n\t\t\t\t\t_getDataValue,\r\n\t\t\t\t\t_setDataValue\r\n\t\t\t\t} = this.form || {}\r\n\t\t\t\t// 对齐方式\r\n\t\t\t\tthis.localLabelAlign = this._justifyContent()\r\n\t\t\t\t// 宽度\r\n\t\t\t\tthis.localLabelWidth = this._labelWidthUnit(labelWidth)\r\n\t\t\t\t// 标签位置\r\n\t\t\t\tthis.localLabelPos = this._labelPosition()\r\n\t\t\t\t// 将需要校验的子组件加入form 队列\r\n\t\t\t\tthis.form && type && childrens.push(this)\r\n\r\n\t\t\t\tif (!validator || !formRules) return\r\n\t\t\t\t// 判断第一个 item\r\n\t\t\t\tif (!this.form.isFirstBorder) {\r\n\t\t\t\t\tthis.form.isFirstBorder = true;\r\n\t\t\t\t\tthis.isFirstBorder = true;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 判断 group 里的第一个 item\r\n\t\t\t\tif (this.group) {\r\n\t\t\t\t\tif (!this.group.isFirstBorder) {\r\n\t\t\t\t\t\tthis.group.isFirstBorder = true;\r\n\t\t\t\t\t\tthis.isFirstBorder = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.border = this.form.border;\r\n\t\t\t\t// 获取子域的真实名称\r\n\t\t\t\tconst name = _realName(this.name)\r\n\t\t\t\tconst itemRule = this.userRules || this.rules\r\n\t\t\t\tif (typeof formRules === 'object' && itemRule) {\r\n\t\t\t\t\t// 子规则替换父规则\r\n\t\t\t\t\tformRules[name] = {\r\n\t\t\t\t\t\trules: itemRule\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvalidator.updateSchema(formRules);\r\n\t\t\t\t}\r\n\t\t\t\t// 注册校验规则\r\n\t\t\t\tconst itemRules = formRules[name] || {}\r\n\t\t\t\tthis.itemRules = itemRules\r\n\t\t\t\t// 注册校验函数\r\n\t\t\t\tthis.validator = validator\r\n\t\t\t\t// 默认值赋予\r\n\t\t\t\tthis.itemSetValue(_getDataValue(this.name, localData))\r\n\t\t\t},\r\n\t\t\tunInit() {\r\n\t\t\t\tif (this.form) {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tchildrens,\r\n\t\t\t\t\t\tformData,\r\n\t\t\t\t\t\t_realName\r\n\t\t\t\t\t} = this.form\r\n\t\t\t\t\tchildrens.forEach((item, index) => {\r\n\t\t\t\t\t\tif (item === this) {\r\n\t\t\t\t\t\t\tthis.form.childrens.splice(index, 1)\r\n\t\t\t\t\t\t\tdelete formData[_realName(item.name)]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 设置item 的值\r\n\t\t\titemSetValue(value) {\r\n\t\t\t\tconst name = this.form._realName(this.name)\r\n\t\t\t\tconst rules = this.itemRules.rules || []\r\n\t\t\t\tconst val = this.form._getValue(name, value, rules)\r\n\t\t\t\tthis.form._setDataValue(name, this.form.formData, val)\r\n\t\t\t\treturn val\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 移除该表单项的校验结果\r\n\t\t\t */\r\n\t\t\tclearValidate() {\r\n\t\t\t\tthis.errMsg = '';\r\n\t\t\t},\r\n\r\n\t\t\t// 是否显示星号\r\n\t\t\t_isRequired() {\r\n\t\t\t\t// TODO 不根据规则显示 星号，考虑后续兼容\r\n\t\t\t\t// if (this.form) {\r\n\t\t\t\t// \tif (this.form._isRequiredField(this.itemRules.rules || []) && this.required) {\r\n\t\t\t\t// \t\treturn true\r\n\t\t\t\t// \t}\r\n\t\t\t\t// \treturn false\r\n\t\t\t\t// }\r\n\t\t\t\treturn this.required\r\n\t\t\t},\r\n\r\n\t\t\t// 处理对齐方式\r\n\t\t\t_justifyContent() {\r\n\t\t\t\tif (this.form) {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tlabelAlign\r\n\t\t\t\t\t} = this.form\r\n\t\t\t\t\tlet labelAli = this.labelAlign ? this.labelAlign : labelAlign;\r\n\t\t\t\t\tif (labelAli === 'left') return 'flex-start';\r\n\t\t\t\t\tif (labelAli === 'center') return 'center';\r\n\t\t\t\t\tif (labelAli === 'right') return 'flex-end';\r\n\t\t\t\t}\r\n\t\t\t\treturn 'flex-start';\r\n\t\t\t},\r\n\t\t\t// 处理 label宽度单位 ,继承父元素的值\r\n\t\t\t_labelWidthUnit(labelWidth) {\r\n\r\n\t\t\t\t// if (this.form) {\r\n\t\t\t\t// \tconst {\r\n\t\t\t\t// \t\tlabelWidth\r\n\t\t\t\t// \t} = this.form\r\n\t\t\t\treturn this.num2px(this.labelWidth ? this.labelWidth : (labelWidth || (this.label ? 65 : 'auto')))\r\n\t\t\t\t// }\r\n\t\t\t\t// return '65px'\r\n\t\t\t},\r\n\t\t\t// 处理 label 位置\r\n\t\t\t_labelPosition() {\r\n\t\t\t\tif (this.form) return this.form.labelPosition || 'left'\r\n\t\t\t\treturn 'left'\r\n\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 触发时机\r\n\t\t\t * @param {Object} rule 当前规则内时机\r\n\t\t\t * @param {Object} itemRlue 当前组件时机\r\n\t\t\t * @param {Object} parentRule 父组件时机\r\n\t\t\t */\r\n\t\t\tisTrigger(rule, itemRlue, parentRule) {\r\n\t\t\t\t//  bind  submit\r\n\t\t\t\tif (rule === 'submit' || !rule) {\r\n\t\t\t\t\tif (rule === undefined) {\r\n\t\t\t\t\t\tif (itemRlue !== 'bind') {\r\n\t\t\t\t\t\t\tif (!itemRlue) {\r\n\t\t\t\t\t\t\t\treturn parentRule === '' ? 'bind' : 'submit';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn 'submit';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn 'bind';\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn 'submit';\r\n\t\t\t\t}\r\n\t\t\t\treturn 'bind';\r\n\t\t\t},\r\n\t\t\tnum2px(num) {\r\n\t\t\t\tif (typeof num === 'number') {\r\n\t\t\t\t\treturn `${num}px`\r\n\t\t\t\t}\r\n\t\t\t\treturn num\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.uni-forms-item {\r\n\t\tposition: relative;\r\n\t\tdisplay: flex;\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\t// 在 nvue 中，使用 margin-bottom error 信息会被隐藏\r\n\t\tpadding-bottom: 22px;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tmargin-bottom: 22px;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\r\n\t\t&__label {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: row;\r\n\t\t\talign-items: center;\r\n\t\t\ttext-align: left;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #606266;\r\n\t\t\theight: 36px;\r\n\t\t\tpadding: 0 12px 0 0;\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tvertical-align: middle;\r\n\t\t\tflex-shrink: 0;\r\n\t\t\t/* #endif */\r\n\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tbox-sizing: border-box;\r\n\r\n\t\t\t/* #endif */\r\n\t\t\t&.no-label {\r\n\t\t\t\tpadding: 0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__content {\r\n\t\t\t/* #ifndef MP-TOUTIAO */\r\n\t\t\t// display: flex;\r\n\t\t\t// align-items: center;\r\n\t\t\t/* #endif */\r\n\t\t\tposition: relative;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tflex: 1;\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\t/* #endif */\r\n\t\t\tflex-direction: row;\r\n\r\n\t\t\t/* #ifndef APP || H5 || MP-WEIXIN || APP-NVUE */\r\n\t\t\t// TODO 因为小程序平台会多一层标签节点 ，所以需要在多余节点继承当前样式\r\n\t\t\t&>uni-easyinput,\r\n\t\t\t&>uni-data-picker {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\r\n\t\t\t/* #endif */\r\n\r\n\t\t}\r\n\r\n\t\t& .uni-forms-item__nuve-content {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tflex: 1;\r\n\t\t}\r\n\r\n\t\t&__error {\r\n\t\t\tcolor: #f56c6c;\r\n\t\t\tfont-size: 12px;\r\n\t\t\tline-height: 1;\r\n\t\t\tpadding-top: 4px;\r\n\t\t\tposition: absolute;\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\ttop: 100%;\r\n\t\t\tleft: 0;\r\n\t\t\ttransition: transform 0.3s;\r\n\t\t\ttransform: translateY(-100%);\r\n\t\t\t/* #endif */\r\n\t\t\t/* #ifdef APP-NVUE */\r\n\t\t\tbottom: 5px;\r\n\t\t\t/* #endif */\r\n\r\n\t\t\topacity: 0;\r\n\r\n\t\t\t.error-text {\r\n\t\t\t\t// 只有 nvue 下这个样式才生效\r\n\t\t\t\tcolor: #f56c6c;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t}\r\n\r\n\t\t\t&.msg--active {\r\n\t\t\t\topacity: 1;\r\n\t\t\t\ttransform: translateY(0%);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// 位置修饰样式\r\n\t\t&.is-direction-left {\r\n\t\t\tflex-direction: row;\r\n\t\t}\r\n\r\n\t\t&.is-direction-top {\r\n\t\t\tflex-direction: column;\r\n\r\n\t\t\t.uni-forms-item__label {\r\n\t\t\t\tpadding: 0 0 8px;\r\n\t\t\t\tline-height: 1.5715;\r\n\t\t\t\ttext-align: left;\r\n\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\twhite-space: initial;\r\n\t\t\t\t/* #endif */\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.is-required {\r\n\t\t\t// color: $uni-color-error;\r\n\t\t\tcolor: #dd524d;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t.uni-forms-item--border {\r\n\t\tmargin-bottom: 0;\r\n\t\tpadding: 10px 0;\r\n\t\t// padding-bottom: 0;\r\n\t\tborder-top: 1px #eee solid;\r\n\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\t.uni-forms-item__content {\r\n\t\t\tflex-direction: column;\r\n\t\t\tjustify-content: flex-start;\r\n\t\t\talign-items: flex-start;\r\n\r\n\t\t\t.uni-forms-item__error {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttop: 5px;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tpadding-top: 0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/* #endif */\r\n\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\r\n\t\t.uni-forms-item__error {\r\n\t\t\tposition: relative;\r\n\t\t\ttop: 0px;\r\n\t\t\tleft: 0;\r\n\t\t\tpadding-top: 0;\r\n\t\t\tmargin-top: 5px;\r\n\t\t}\r\n\r\n\t\t/* #endif */\r\n\r\n\t}\r\n\r\n\t.is-first-border {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tborder: none;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tborder-width: 0;\r\n\t\t/* #endif */\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-forms-item.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-forms-item.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752425864219\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}