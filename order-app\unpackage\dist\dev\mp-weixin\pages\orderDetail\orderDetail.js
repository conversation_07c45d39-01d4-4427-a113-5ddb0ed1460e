(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/orderDetail/orderDetail"],{

/***/ 303:
/*!*********************************************************************************************!*\
  !*** D:/项目/工单小程序/work_order/order-app/main.js?{"page":"pages%2ForderDetail%2ForderDetail"} ***!
  \*********************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _orderDetail = _interopRequireDefault(__webpack_require__(/*! ./pages/orderDetail/orderDetail.vue */ 304));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_orderDetail.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 304:
/*!**************************************************************************!*\
  !*** D:/项目/工单小程序/work_order/order-app/pages/orderDetail/orderDetail.vue ***!
  \**************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _orderDetail_vue_vue_type_template_id_c547daf4___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./orderDetail.vue?vue&type=template&id=c547daf4& */ 305);
/* harmony import */ var _orderDetail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./orderDetail.vue?vue&type=script&lang=js& */ 307);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _orderDetail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _orderDetail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _orderDetail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./orderDetail.vue?vue&type=style&index=0&lang=scss& */ 310);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 45);

var renderjs





/* normalize component */

var component = Object(_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _orderDetail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _orderDetail_vue_vue_type_template_id_c547daf4___WEBPACK_IMPORTED_MODULE_0__["render"],
  _orderDetail_vue_vue_type_template_id_c547daf4___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _orderDetail_vue_vue_type_template_id_c547daf4___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/orderDetail/orderDetail.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 305:
/*!*********************************************************************************************************!*\
  !*** D:/项目/工单小程序/work_order/order-app/pages/orderDetail/orderDetail.vue?vue&type=template&id=c547daf4& ***!
  \*********************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDetail_vue_vue_type_template_id_c547daf4___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDetail.vue?vue&type=template&id=c547daf4& */ 306);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDetail_vue_vue_type_template_id_c547daf4___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDetail_vue_vue_type_template_id_c547daf4___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDetail_vue_vue_type_template_id_c547daf4___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDetail_vue_vue_type_template_id_c547daf4___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 306:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/项目/工单小程序/work_order/order-app/pages/orderDetail/orderDetail.vue?vue&type=template&id=c547daf4& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uIcon: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-icon/u-icon */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-icon/u-icon")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-icon/u-icon.vue */ 312))
    },
    uPopup: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-popup/u-popup */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-popup/u-popup")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-popup/u-popup.vue */ 329))
    },
    uButton: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-button/u-button */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-button/u-button")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-button/u-button.vue */ 337))
    },
    uUpload: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-upload/u-upload */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-upload/u-upload")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-upload/u-upload.vue */ 347))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.getStatusText(_vm.orderDetail && _vm.orderDetail.status)
  var l1 = _vm.__map(_vm.orderItemLists, function (task, taskIndex) {
    var $orig = _vm.__get_orig(task)
    var m1 = _vm.getTaskStatusText(task.itemStatus)
    var m2 = _vm.isMaintenanceTask(task.taskName)
    var m4 = _vm.isShippingTask(task.taskName)
    var m5 =
      !_vm.isMaintenanceTask(task.taskName) &&
      !_vm.isShippingTask(task.taskName)
    var m6 = _vm.isMaintenanceTask(task.taskName)
    var l0 = _vm.__map(
      task.assignments || [],
      function (assignment, assignmentIndex) {
        var $orig = _vm.__get_orig(assignment)
        var m3 = _vm.getAssignmentStatusText(assignment.assignmentStatus || "1")
        return {
          $orig: $orig,
          m3: m3,
        }
      }
    )
    var g0 = !task.assignments || task.assignments.length === 0
    return {
      $orig: $orig,
      m1: m1,
      m2: m2,
      m4: m4,
      m5: m5,
      m6: m6,
      l0: l0,
      g0: g0,
    }
  })
  var g1 = (_vm.maintenanceFormData.repairGoodNo || "").length
  if (!_vm._isMounted) {
    _vm.e0 = function (assignment, task) {
      var args = [],
        len = arguments.length - 2
      while (len-- > 0) args[len] = arguments[len + 2]

      var _temp = args[args.length - 1].currentTarget.dataset,
        _temp2 = _temp.eventParams || _temp["event-params"],
        assignment = _temp2.assignment,
        task = _temp2.task
      var _temp, _temp2
      return _vm.handleAssignmentClick(assignment, task)
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        l1: l1,
        g1: g1,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 307:
/*!***************************************************************************************************!*\
  !*** D:/项目/工单小程序/work_order/order-app/pages/orderDetail/orderDetail.vue?vue&type=script&lang=js& ***!
  \***************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDetail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDetail.vue?vue&type=script&lang=js& */ 308);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDetail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDetail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDetail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDetail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDetail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 308:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/项目/工单小程序/work_order/order-app/pages/orderDetail/orderDetail.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 75));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ 5));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 77));
var _orders = __webpack_require__(/*! @/api/work/orders.js */ 184);
var _orderItems = __webpack_require__(/*! @/api/work/orderItems.js */ 309);
var _config = __webpack_require__(/*! ../../config */ 30);
var _user = __webpack_require__(/*! @/api/system/user.js */ 227);
var _taskAssignments = __webpack_require__(/*! @/api/work/taskAssignments.js */ 209);
function _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  data: function data() {
    return {
      orderDetail: null,
      orderItemLists: [],
      baseUrl: _config.baseUrl,
      userList: [],
      showUserPickerFlag: false,
      selectedUser: null,
      currentTask: null,
      assignQuantity: '',
      taskAssignmentsParams: {
        assignedToUserId: '',
        assignedQuantity: '',
        workOrderItemId: ''
      },
      expandedTasks: {},
      // 用于存储任务的展开状态
      showAssignmentDetailFlag: false,
      currentAssignment: null,
      userId: this.$store.state.user.userId,
      roles: this.$store.state.user.roles,
      showMaintenanceFormFlag: false,
      maintenanceFileList: [],
      maintenanceFormData: {
        pic: '',
        description: '',
        repairGoodNo: ''
      },
      isUploading: false,
      uploadProgress: 0
    };
  },
  computed: {
    imageList: function imageList() {
      var _this = this;
      if (!this.orderDetail || !this.orderDetail.packingListImagePath) return [];
      return this.orderDetail.packingListImagePath.split(',').map(function (path) {
        return _this.baseUrl + path;
      });
    },
    assignmentImageList: function assignmentImageList() {
      var _this2 = this;
      if (!this.currentAssignment || !this.currentAssignment.pic) return [];
      return this.currentAssignment.pic.split(',').map(function (path) {
        return _this2.baseUrl + path;
      });
    },
    isRepairman: function isRepairman() {
      return this.roles.includes('repairman');
    },
    maintenanceTask: function maintenanceTask() {
      return this.orderItemLists.find(function (task) {
        return task.taskName && task.taskName.includes('维修');
      });
    },
    isMaintenanceAssignment: function isMaintenanceAssignment() {
      return this.currentAssignment && this.currentAssignment.taskName && this.currentAssignment.taskName.includes('维修');
    },
    isShippingAssignment: function isShippingAssignment() {
      return this.currentAssignment && this.currentAssignment.taskName && this.currentAssignment.taskName.includes('发货');
    },
    canDeleteAssignment: function canDeleteAssignment() {
      return this.roles.includes('admin') || this.roles.includes('dispatcher');
    },
    canAssignTask: function canAssignTask() {
      return this.roles.includes('admin') || this.roles.includes('dispatcher');
    }
  },
  onLoad: function onLoad(_ref) {
    var id = _ref.id;
    this.getDetail(id);
  },
  methods: {
    getDetail: function getDetail(id) {
      var _this3 = this;
      (0, _orders.getOrders)(id).then( /*#__PURE__*/function () {
        var _ref2 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee(res) {
          var itemRes, _iterator, _step, task, assignRes;
          return _regenerator.default.wrap(function _callee$(_context) {
            while (1) {
              switch (_context.prev = _context.next) {
                case 0:
                  _this3.orderDetail = res.data;
                  _context.next = 3;
                  return (0, _orderItems.listOrderItems)({
                    pageSize: 100000,
                    workOrderId: id
                  });
                case 3:
                  itemRes = _context.sent;
                  // 确保每个任务都有assignments数组
                  _this3.orderItemLists = itemRes.rows.map(function (task) {
                    return _objectSpread(_objectSpread({}, task), {}, {
                      assignments: []
                    });
                  });

                  // 查询每个任务的指派情况
                  _iterator = _createForOfIteratorHelper(_this3.orderItemLists);
                  _context.prev = 6;
                  _iterator.s();
                case 8:
                  if ((_step = _iterator.n()).done) {
                    _context.next = 17;
                    break;
                  }
                  task = _step.value;
                  _context.next = 12;
                  return (0, _taskAssignments.listTaskAssignments)({
                    workOrderItemId: task.workOrderItemId
                  });
                case 12:
                  assignRes = _context.sent;
                  task.assignments = assignRes.rows || [];
                  console.log('任务指派信息：', task.workOrderItemId, assignRes.rows);
                case 15:
                  _context.next = 8;
                  break;
                case 17:
                  _context.next = 22;
                  break;
                case 19:
                  _context.prev = 19;
                  _context.t0 = _context["catch"](6);
                  _iterator.e(_context.t0);
                case 22:
                  _context.prev = 22;
                  _iterator.f();
                  return _context.finish(22);
                case 25:
                  console.log('工单详情：', _this3.orderDetail);
                  console.log('任务列表：', _this3.orderItemLists);
                case 27:
                case "end":
                  return _context.stop();
              }
            }
          }, _callee, null, [[6, 19, 22, 25]]);
        }));
        return function (_x) {
          return _ref2.apply(this, arguments);
        };
      }()).catch(function (error) {
        console.error('获取工单详情失败：', error);
        uni.showToast({
          title: '获取工单详情失败',
          icon: 'none'
        });
      });
    },
    // 获取工单状态文本
    getStatusText: function getStatusText(status) {
      var statusMap = {
        '0': '未开始',
        '1': '处理中',
        '2': '已完成'
      };
      return statusMap[status] || '未知状态';
    },
    // 获取任务状态文本
    getTaskStatusText: function getTaskStatusText(status) {
      var statusMap = {
        '0': '未开始',
        '1': '处理中',
        '2': '已完成'
      };
      return statusMap[status] || '未知状态';
    },
    // 获取指派任务状态文本
    getAssignmentStatusText: function getAssignmentStatusText(status) {
      var statusMap = {
        '1': '处理中',
        '2': '已完成'
      };
      return statusMap[status] || '未知状态';
    },
    // 预览图片
    previewImage: function previewImage(imageIndex) {
      uni.previewImage({
        urls: this.imageList,
        current: imageIndex
      });
    },
    // 显示用户选择器
    showUserPicker: function showUserPicker(task) {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var res;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _this4.currentTask = task;
                _this4.selectedUser = null;
                _this4.assignQuantity = task.plannedQuantity || '';
                _context2.prev = 3;
                _context2.next = 6;
                return (0, _user.listUser)();
              case 6:
                res = _context2.sent;
                if (res && res.rows) {
                  _this4.userList = res.rows;
                  _this4.showUserPickerFlag = true;
                }
                _context2.next = 14;
                break;
              case 10:
                _context2.prev = 10;
                _context2.t0 = _context2["catch"](3);
                console.error('获取用户列表失败', _context2.t0);
                uni.showToast({
                  title: '获取用户列表失败',
                  icon: 'none'
                });
              case 14:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[3, 10]]);
      }))();
    },
    // 选择用户
    selectUser: function selectUser(user) {
      this.selectedUser = user;
    },
    // 确认选择用户
    confirmUser: function confirmUser() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var quantity;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                if (_this5.selectedUser) {
                  _context3.next = 3;
                  break;
                }
                uni.showToast({
                  title: '请选择执行人',
                  icon: 'none'
                });
                return _context3.abrupt("return");
              case 3:
                if (_this5.assignQuantity) {
                  _context3.next = 6;
                  break;
                }
                uni.showToast({
                  title: '请输入计划数量',
                  icon: 'none'
                });
                return _context3.abrupt("return");
              case 6:
                quantity = Number(_this5.assignQuantity);
                if (!(isNaN(quantity) || quantity <= 0)) {
                  _context3.next = 10;
                  break;
                }
                uni.showToast({
                  title: '请输入有效的计划数量',
                  icon: 'none'
                });
                return _context3.abrupt("return");
              case 10:
                if (!(quantity > _this5.currentTask.plannedQuantity)) {
                  _context3.next = 13;
                  break;
                }
                uni.showToast({
                  title: '计划数量不能大于任务计划数',
                  icon: 'none'
                });
                return _context3.abrupt("return");
              case 13:
                _context3.prev = 13;
                // 设置指派参数
                _this5.taskAssignmentsParams = {
                  assignedToUserId: _this5.selectedUser.userId,
                  assignedQuantity: quantity,
                  workOrderItemId: _this5.currentTask.workOrderItemId
                };

                // 调用指派接口
                _context3.next = 17;
                return (0, _taskAssignments.addTaskAssignments)(_this5.taskAssignmentsParams);
              case 17:
                if (!(_this5.orderDetail.status === '0')) {
                  _context3.next = 20;
                  break;
                }
                _context3.next = 20;
                return (0, _orders.updateOrders)({
                  workOrderId: _this5.orderDetail.workOrderId,
                  status: '1'
                });
              case 20:
                _context3.next = 22;
                return (0, _orderItems.updateOrderItems)({
                  workOrderItemId: _this5.currentTask.workOrderItemId,
                  itemStatus: '1'
                });
              case 22:
                uni.showToast({
                  title: '指派成功',
                  icon: 'success'
                });

                // 关闭弹窗
                _this5.closeUserPicker();

                // 刷新页面数据
                _this5.getDetail(_this5.orderDetail.workOrderId);
                _context3.next = 31;
                break;
              case 27:
                _context3.prev = 27;
                _context3.t0 = _context3["catch"](13);
                console.error('指派失败', _context3.t0);
                uni.showToast({
                  title: '指派失败',
                  icon: 'none'
                });
              case 31:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[13, 27]]);
      }))();
    },
    // 关闭用户选择器
    closeUserPicker: function closeUserPicker() {
      this.showUserPickerFlag = false;
      this.selectedUser = null;
      this.currentTask = null;
      this.assignQuantity = '';
    },
    // 切换任务展开状态
    toggleTask: function toggleTask(task) {
      this.$set(this.expandedTasks, task.workOrderItemId, !this.expandedTasks[task.workOrderItemId]);
    },
    // 处理指派点击
    handleAssignmentClick: function handleAssignmentClick(assignment, task) {
      if (!assignment) return;
      try {
        // 确保assignment对象包含所需的所有属性
        this.currentAssignment = {
          nickName: assignment.nickName || '未知用户',
          goodQuantity: assignment.reportedGoodQuantity || 0,
          defectiveQuantity: assignment.reportedDefectiveQuantity || 0,
          pic: assignment.pic || '',
          description: assignment.description || '',
          assignmentStatus: assignment.assignmentStatus || '1',
          taskName: task ? task.taskName : '',
          locationAddress: assignment.locationAddress || '',
          repairGoodNo: assignment.repairGoodNo || '',
          createTime: assignment.createTime || ''
        };
        this.showAssignmentDetailFlag = true;
      } catch (error) {
        console.error('处理指派点击失败：', error);
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },
    // 显示指派详情
    showAssignmentDetail: function showAssignmentDetail(assignment) {
      if (!assignment) return;
      this.currentAssignment = assignment;
      this.showAssignmentDetailFlag = true;
    },
    // 关闭指派详情
    closeAssignmentDetail: function closeAssignmentDetail() {
      this.showAssignmentDetailFlag = false;
      this.currentAssignment = null;
    },
    // 预览指派图片
    previewAssignmentImage: function previewAssignmentImage(detailImageIndex) {
      if (!this.assignmentImageList || !this.assignmentImageList.length) return;
      uni.previewImage({
        urls: this.assignmentImageList,
        current: detailImageIndex
      });
    },
    // 处理删除指派
    handleDeleteAssignment: function handleDeleteAssignment(event) {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var assignmentId, taskId;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                assignmentId = event.currentTarget.dataset.assignmentId;
                taskId = event.currentTarget.dataset.taskId;
                if (!(!assignmentId || !taskId)) {
                  _context5.next = 5;
                  break;
                }
                uni.showToast({
                  title: '无法获取指派信息',
                  icon: 'none'
                });
                return _context5.abrupt("return");
              case 5:
                try {
                  uni.showModal({
                    title: '提示',
                    content: '确定要删除这条指派记录吗？',
                    success: function () {
                      var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4(res) {
                        var assignRes;
                        return _regenerator.default.wrap(function _callee4$(_context4) {
                          while (1) {
                            switch (_context4.prev = _context4.next) {
                              case 0:
                                if (!res.confirm) {
                                  _context4.next = 11;
                                  break;
                                }
                                _context4.next = 3;
                                return (0, _taskAssignments.delTaskAssignments)(assignmentId);
                              case 3:
                                _context4.next = 5;
                                return (0, _taskAssignments.listTaskAssignments)({
                                  workOrderItemId: taskId
                                });
                              case 5:
                                assignRes = _context4.sent;
                                if (!(!assignRes || !assignRes.rows || assignRes.rows.length === 0)) {
                                  _context4.next = 9;
                                  break;
                                }
                                _context4.next = 9;
                                return (0, _orderItems.updateOrderItems)({
                                  workOrderItemId: taskId,
                                  itemStatus: '0' // 未开始
                                });
                              case 9:
                                uni.showToast({
                                  title: '删除成功',
                                  icon: 'success'
                                });

                                // 刷新页面数据
                                _this6.getDetail(_this6.orderDetail.workOrderId);
                              case 11:
                              case "end":
                                return _context4.stop();
                            }
                          }
                        }, _callee4);
                      }));
                      function success(_x2) {
                        return _success.apply(this, arguments);
                      }
                      return success;
                    }()
                  });
                } catch (error) {
                  console.error('删除指派失败：', error);
                  uni.showToast({
                    title: '删除失败',
                    icon: 'none'
                  });
                }
              case 6:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5);
      }))();
    },
    // 显示维修记录表单
    showMaintenanceForm: function showMaintenanceForm() {
      this.showMaintenanceFormFlag = true;
    },
    // 关闭维修记录表单
    closeMaintenanceForm: function closeMaintenanceForm() {
      this.showMaintenanceFormFlag = false;
      this.maintenanceFileList = [];
      this.maintenanceFormData = {
        pic: '',
        description: '',
        repairGoodNo: ''
      };
      // 重置上传状态
      this.isUploading = false;
      this.uploadProgress = 0;
    },
    // 处理维修图片上传
    afterMaintenanceRead: function afterMaintenanceRead(event) {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var file, progressTimer, _yield$uni$uploadFile, _yield$uni$uploadFile2, uploadErr, uploadRes, result, newPic;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                console.log('维修图片上传事件：', event);

                // 设置上传状态
                _this7.isUploading = true;
                _this7.uploadProgress = 0;

                // 显示上传中提示
                uni.showLoading({
                  title: '图片上传中...',
                  mask: true
                });
                _context6.prev = 4;
                file = event.file; // 模拟上传进度
                progressTimer = setInterval(function () {
                  if (_this7.uploadProgress < 90) {
                    _this7.uploadProgress += 10;
                  }
                }, 100);
                _context6.next = 9;
                return uni.uploadFile({
                  url: _this7.baseUrl + '/common/upload',
                  filePath: file.url,
                  name: 'file'
                });
              case 9:
                _yield$uni$uploadFile = _context6.sent;
                _yield$uni$uploadFile2 = (0, _slicedToArray2.default)(_yield$uni$uploadFile, 2);
                uploadErr = _yield$uni$uploadFile2[0];
                uploadRes = _yield$uni$uploadFile2[1];
                // 清除进度定时器
                clearInterval(progressTimer);
                _this7.uploadProgress = 100;
                if (!uploadErr) {
                  _context6.next = 17;
                  break;
                }
                throw new Error('上传图片失败');
              case 17:
                result = JSON.parse(uploadRes.data);
                if (!(result.code === 200)) {
                  _context6.next = 25;
                  break;
                }
                // 更新图片路径，用逗号分隔多张图片
                newPic = result.fileName;
                if (_this7.maintenanceFormData.pic) {
                  _this7.maintenanceFormData.pic = _this7.maintenanceFormData.pic + ',' + newPic;
                } else {
                  _this7.maintenanceFormData.pic = newPic;
                }

                // 更新文件列表显示
                _this7.maintenanceFileList = _this7.maintenanceFormData.pic.split(',').filter(function (url) {
                  return url;
                }).map(function (url) {
                  return {
                    url: _this7.baseUrl + url,
                    status: 'success',
                    message: '上传成功'
                  };
                });

                // 延迟一下再隐藏加载
                setTimeout(function () {
                  uni.hideLoading();
                  uni.showToast({
                    title: '上传成功',
                    icon: 'success',
                    duration: 1500
                  });
                  _this7.isUploading = false;
                  _this7.uploadProgress = 0;
                }, 300);
                _context6.next = 26;
                break;
              case 25:
                throw new Error(result.msg || '上传失败');
              case 26:
                _context6.next = 35;
                break;
              case 28:
                _context6.prev = 28;
                _context6.t0 = _context6["catch"](4);
                console.error('上传维修图片失败：', _context6.t0);

                // 重置状态
                _this7.isUploading = false;
                _this7.uploadProgress = 0;
                uni.hideLoading();
                uni.showToast({
                  title: _context6.t0.message || '上传图片失败',
                  icon: 'error',
                  duration: 2000
                });
              case 35:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[4, 28]]);
      }))();
    },
    // 删除维修图片
    deleteMaintenancePic: function deleteMaintenancePic(event) {
      var _this8 = this;
      console.log('删除维修图片：', event);
      var pics = this.maintenanceFormData.pic.split(',').filter(function (url) {
        return url;
      });
      pics.splice(event.index, 1);
      this.maintenanceFormData.pic = pics.join(',');

      // 更新文件列表显示
      this.maintenanceFileList = pics.map(function (url) {
        return {
          url: _this8.baseUrl + url,
          status: 'success',
          message: '上传成功'
        };
      });
    },
    // 提交维修记录
    submitMaintenanceRecord: function submitMaintenanceRecord() {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var maintenanceTaskItem, createTaskParams, createResult, submitParams;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                if (_this9.maintenanceFormData.pic) {
                  _context7.next = 3;
                  break;
                }
                uni.showToast({
                  title: '请上传维修图片',
                  icon: 'none'
                });
                return _context7.abrupt("return");
              case 3:
                if (_this9.maintenanceFormData.description) {
                  _context7.next = 6;
                  break;
                }
                uni.showToast({
                  title: '请填写维修说明',
                  icon: 'none'
                });
                return _context7.abrupt("return");
              case 6:
                _context7.prev = 6;
                // 首先尝试从现有任务列表中查找维修任务
                maintenanceTaskItem = _this9.orderItemLists.find(function (task) {
                  return task.productionTaskId === 3;
                }); // 如果没有维修任务，先创建一个
                if (maintenanceTaskItem) {
                  _context7.next = 40;
                  break;
                }
                createTaskParams = {
                  workOrderId: _this9.orderDetail.workOrderId,
                  productionTaskId: 3,
                  // 维修任务ID
                  plannedQuantity: _this9.orderDetail.totalPlannedQuantity,
                  itemStatus: '2',
                  // 已完成状态
                  itemGoodQuantity: null,
                  itemDefectiveQuantity: null
                };
                console.log('创建维修任务参数：', createTaskParams);
                _context7.next = 13;
                return (0, _orderItems.addOrderItems)(createTaskParams);
              case 13:
                createResult = _context7.sent;
                console.log('创建维修任务结果：', createResult);
                if (!(createResult && createResult.code === 200)) {
                  _context7.next = 38;
                  break;
                }
                // 显示加载动画
                uni.showLoading({
                  title: '正在创建维修任务...',
                  mask: true
                });

                // 等待一下，确保数据库事务完成
                _context7.next = 19;
                return new Promise(function (resolve) {
                  return setTimeout(resolve, 500);
                });
              case 19:
                _context7.next = 21;
                return _this9.getDetail(_this9.orderDetail.workOrderId);
              case 21:
                console.log('重新获取后的任务列表：', _this9.orderItemLists);
                console.log('查找维修任务，productionTaskId=3');

                // 再次根据productionTaskId=3查找维修任务
                maintenanceTaskItem = _this9.orderItemLists.find(function (task) {
                  console.log('任务：', task.taskName, 'productionTaskId：', task.productionTaskId);
                  return task.productionTaskId === 3;
                });
                console.log('找到的维修任务：', maintenanceTaskItem);

                // 如果还是找不到，再试一次
                if (maintenanceTaskItem) {
                  _context7.next = 35;
                  break;
                }
                console.log('第一次查找失败，等待1秒后重试...');
                uni.showLoading({
                  title: '正在重试获取任务...',
                  mask: true
                });
                _context7.next = 30;
                return new Promise(function (resolve) {
                  return setTimeout(resolve, 1000);
                });
              case 30:
                _context7.next = 32;
                return _this9.getDetail(_this9.orderDetail.workOrderId);
              case 32:
                console.log('重试后的任务列表：', _this9.orderItemLists);
                maintenanceTaskItem = _this9.orderItemLists.find(function (task) {
                  return task.productionTaskId === 3;
                });
                console.log('重试后找到的维修任务：', maintenanceTaskItem);
              case 35:
                // 隐藏加载动画
                uni.hideLoading();
                _context7.next = 40;
                break;
              case 38:
                uni.showToast({
                  title: '创建维修任务失败',
                  icon: 'none'
                });
                return _context7.abrupt("return");
              case 40:
                if (maintenanceTaskItem) {
                  _context7.next = 43;
                  break;
                }
                uni.showToast({
                  title: '无法获取维修任务信息',
                  icon: 'none'
                });
                return _context7.abrupt("return");
              case 43:
                // 准备提交参数
                submitParams = {
                  assignedToUserId: _this9.userId,
                  workOrderItemId: maintenanceTaskItem.workOrderItemId,
                  assignedQuantity: 1,
                  // 维修任务默认数量为1
                  pic: _this9.maintenanceFormData.pic,
                  description: _this9.maintenanceFormData.description,
                  repairGoodNo: _this9.maintenanceFormData.repairGoodNo
                };
                console.log('提交维修记录参数：', submitParams);

                // 调用addTaskAssignments方法
                _context7.next = 47;
                return (0, _taskAssignments.addTaskAssignments)(submitParams);
              case 47:
                uni.showToast({
                  title: '维修记录添加成功',
                  icon: 'success'
                });

                // 关闭弹窗
                _this9.closeMaintenanceForm();

                // 刷新页面数据
                _this9.getDetail(_this9.orderDetail.workOrderId);
                _context7.next = 56;
                break;
              case 52:
                _context7.prev = 52;
                _context7.t0 = _context7["catch"](6);
                console.error('提交维修记录失败：', _context7.t0);
                uni.showToast({
                  title: '提交失败',
                  icon: 'none'
                });
              case 56:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7, null, [[6, 52]]);
      }))();
    },
    // 判断是否为维修任务
    isMaintenanceTask: function isMaintenanceTask(taskName) {
      return taskName && taskName.includes('维修');
    },
    // 获取指派图片
    getAssignmentImages: function getAssignmentImages(pic) {
      var _this10 = this;
      if (!pic) return [];
      return pic.split(',').map(function (path) {
        return _this10.baseUrl + path;
      });
    },
    // 预览指派图片
    previewAssignmentImageFromList: function previewAssignmentImageFromList(pic, imageIndex) {
      uni.previewImage({
        urls: this.getAssignmentImages(pic),
        current: imageIndex
      });
    },
    // 判断是否为发货任务
    isShippingTask: function isShippingTask(taskName) {
      return taskName && taskName.includes('发货');
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 310:
/*!************************************************************************************************************!*\
  !*** D:/项目/工单小程序/work_order/order-app/pages/orderDetail/orderDetail.vue?vue&type=style&index=0&lang=scss& ***!
  \************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDetail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDetail.vue?vue&type=style&index=0&lang=scss& */ 311);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDetail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDetail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDetail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDetail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderDetail_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 311:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/项目/工单小程序/work_order/order-app/pages/orderDetail/orderDetail.vue?vue&type=style&index=0&lang=scss& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[303,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/orderDetail/orderDetail.js.map