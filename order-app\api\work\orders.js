import request from '@/utils/request'

// 查询工单主列表
export function listOrders(query) {
  return request({
    url: '/work/orders/list',
    method: 'get',
    params: query
  })
}

// 查询工单主详细
export function getOrders(workOrderId) {
  return request({
    url: '/work/orders/' + workOrderId,
    method: 'get'
  })
}

// 新增工单主
export function addOrders(data) {
  return request({
    url: '/work/orders',
    method: 'post',
    data: data
  })
}

// 修改工单主
export function updateOrders(data) {
  return request({
    url: '/work/orders',
    method: 'put',
    data: data
  })
}

// 删除工单主
export function delOrders(workOrderId) {
  return request({
    url: '/work/orders/' + workOrderId,
    method: 'delete'
  })
}
