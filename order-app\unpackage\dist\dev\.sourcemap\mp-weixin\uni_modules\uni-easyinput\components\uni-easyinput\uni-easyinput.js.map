{"version": 3, "sources": ["webpack:///D:/项目/工单小程序/work_order/order-app/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue?3753", "webpack:///D:/项目/工单小程序/work_order/order-app/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue?e973", "webpack:///D:/项目/工单小程序/work_order/order-app/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue?57ad", "webpack:///D:/项目/工单小程序/work_order/order-app/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue?8335", "uni-app:///uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue", "webpack:///D:/项目/工单小程序/work_order/order-app/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue?5f9b", "webpack:///D:/项目/工单小程序/work_order/order-app/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue?283e"], "names": ["classess", "style", "name", "emits", "model", "prop", "event", "options", "virtualHost", "inject", "form", "from", "default", "formItem", "props", "value", "modelValue", "type", "clearable", "autoHeight", "placeholder", "placeholder<PERSON><PERSON><PERSON>", "focus", "disabled", "maxlength", "confirmType", "clearSize", "inputBorder", "prefixIcon", "suffixIcon", "trim", "cursorSpacing", "passwordIcon", "primaryColor", "styles", "color", "backgroundColor", "disableColor", "borderColor", "errorMessage", "data", "focused", "val", "showMsg", "border", "isFirstBorder", "showClearIcon", "showPassword", "focusShow", "localMsg", "isEnter", "computed", "isVal", "msg", "inputMaxlength", "boxStyle", "inputContentClass", "inputContentStyle", "inputStyle", "watch", "created", "mounted", "methods", "init", "onClickIcon", "onEyes", "onInput", "onFocus", "_Focus", "onBlur", "_Blur", "onConfirm", "onClear", "onkeyboardheightchange", "trimStr"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACc;;;AAG1E;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAsqB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8E1rB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA;EACA;IACA;IACA;MACAA;IACA;EACA;EACA;AACA;AAEA;EACA;EACA;IACA;IACAC;EACA;EACA;AACA;AAAA,gBACA;EACAC;EACAC;EACAC;IACAC;IACAC;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACAZ;IACAa;IACAC;IACAC;MACAA;MACAL;IACA;IACAM;MACAD;MACAL;IACA;IACAO;MACAF;MACAL;IACA;IACAQ;MACAH;MACAL;IACA;IACAS;IACAC;MACAL;MACAL;IACA;IACAW;MACAN;MACAL;IACA;IACAY;MACAP;MACAL;IACA;IACAa;MACAR;MACAL;IACA;IACAc;MACAT;MACAL;IACA;IACAe;MACAV;MACAL;IACA;IACAgB;MACAX;MACAL;IACA;IACAiB;MACAZ;MACAL;IACA;IACAkB;MACAb;MACAL;IACA;IACAmB;MACAd;MACAL;IACA;IACAoB;MACAf;MACAL;IACA;IACAqB;MACAhB;MACAL;IACA;IACAsB;MACAjB;MACAL;QACA;UACAuB;UACAC;UACAC;UACAC;QACA;MACA;IACA;IACAC;MACAtB;MACAL;IACA;EACA;EACA4B;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA5C;MACA;IACA;IACAC;MACA;IACA;IACAM;MAAA;MACA;QACA;QACA;MACA;IACA;EACA;EACAsC;IAAA;IACA;IACA;IACA;MACA;QACA;MACA;IACA;EACA;EACAC;IAAA;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;QACA;UACAnD;QACA;QACA;UACAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAoD;MAAA;MACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;QACA;QACA;UACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AClfA;AAAA;AAAA;AAAA;AAAiwC,CAAgB,smCAAG,EAAC,C;;;;;;;;;;;ACArxC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-easyinput.vue?vue&type=template&id=abe12412&\"\nvar renderjs\nimport script from \"./uni-easyinput.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-easyinput.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-easyinput.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-easyinput.vue?vue&type=template&id=abe12412&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-easyinput.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-easyinput.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-easyinput\" :class=\"{ 'uni-easyinput-error': msg }\" :style=\"boxStyle\">\r\n\t\t<view class=\"uni-easyinput__content\" :class=\"inputContentClass\" :style=\"inputContentStyle\">\r\n\t\t\t<uni-icons v-if=\"prefixIcon\" class=\"content-clear-icon\" :type=\"prefixIcon\" color=\"#c0c4cc\" @click=\"onClickIcon('prefix')\" size=\"22\"></uni-icons>\r\n\t\t\t<textarea\r\n\t\t\t\tv-if=\"type === 'textarea'\"\r\n\t\t\t\tclass=\"uni-easyinput__content-textarea\"\r\n\t\t\t\t:class=\"{ 'input-padding': inputBorder }\"\r\n\t\t\t\t:name=\"name\"\r\n\t\t\t\t:value=\"val\"\r\n\t\t\t\t:placeholder=\"placeholder\"\r\n\t\t\t\t:placeholderStyle=\"placeholderStyle\"\r\n\t\t\t\t:disabled=\"disabled\"\r\n\t\t\t\tplaceholder-class=\"uni-easyinput__placeholder-class\"\r\n\t\t\t\t:maxlength=\"inputMaxlength\"\r\n\t\t\t\t:focus=\"focused\"\r\n\t\t\t\t:autoHeight=\"autoHeight\"\r\n\t\t\t\t:cursor-spacing=\"cursorSpacing\"\r\n\t\t\t\t@input=\"onInput\"\r\n\t\t\t\t@blur=\"_Blur\"\r\n\t\t\t\t@focus=\"_Focus\"\r\n\t\t\t\t@confirm=\"onConfirm\"\r\n        @keyboardheightchange=\"onkeyboardheightchange\"\r\n\t\t\t></textarea>\r\n\t\t\t<input\r\n\t\t\t\tv-else\r\n\t\t\t\t:type=\"type === 'password' ? 'text' : type\"\r\n\t\t\t\tclass=\"uni-easyinput__content-input\"\r\n\t\t\t\t:style=\"inputStyle\"\r\n\t\t\t\t:name=\"name\"\r\n\t\t\t\t:value=\"val\"\r\n\t\t\t\t:password=\"!showPassword && type === 'password'\"\r\n\t\t\t\t:placeholder=\"placeholder\"\r\n\t\t\t\t:placeholderStyle=\"placeholderStyle\"\r\n\t\t\t\tplaceholder-class=\"uni-easyinput__placeholder-class\"\r\n\t\t\t\t:disabled=\"disabled\"\r\n\t\t\t\t:maxlength=\"inputMaxlength\"\r\n\t\t\t\t:focus=\"focused\"\r\n\t\t\t\t:confirmType=\"confirmType\"\r\n\t\t\t\t:cursor-spacing=\"cursorSpacing\"\r\n\t\t\t\t@focus=\"_Focus\"\r\n\t\t\t\t@blur=\"_Blur\"\r\n\t\t\t\t@input=\"onInput\"\r\n\t\t\t\t@confirm=\"onConfirm\"\r\n        @keyboardheightchange=\"onkeyboardheightchange\"\r\n\t\t\t/>\r\n\t\t\t<template v-if=\"type === 'password' && passwordIcon\">\r\n\t\t\t\t<!-- 开启密码时显示小眼睛 -->\r\n\t\t\t\t<uni-icons\r\n\t\t\t\t\tv-if=\"isVal\"\r\n\t\t\t\t\tclass=\"content-clear-icon\"\r\n\t\t\t\t\t:class=\"{ 'is-textarea-icon': type === 'textarea' }\"\r\n\t\t\t\t\t:type=\"showPassword ? 'eye-slash-filled' : 'eye-filled'\"\r\n\t\t\t\t\t:size=\"22\"\r\n\t\t\t\t\t:color=\"focusShow ? primaryColor : '#c0c4cc'\"\r\n\t\t\t\t\t@click=\"onEyes\"\r\n\t\t\t\t></uni-icons>\r\n\t\t\t</template>\r\n\t\t\t<template v-else-if=\"suffixIcon\">\r\n\t\t\t\t<uni-icons v-if=\"suffixIcon\" class=\"content-clear-icon\" :type=\"suffixIcon\" color=\"#c0c4cc\" @click=\"onClickIcon('suffix')\" size=\"22\"></uni-icons>\r\n\t\t\t</template>\r\n\t\t\t<template v-else>\r\n\t\t\t\t<uni-icons\r\n\t\t\t\t\tv-if=\"clearable && isVal && !disabled && type !== 'textarea'\"\r\n\t\t\t\t\tclass=\"content-clear-icon\"\r\n\t\t\t\t\t:class=\"{ 'is-textarea-icon': type === 'textarea' }\"\r\n\t\t\t\t\ttype=\"clear\"\r\n\t\t\t\t\t:size=\"clearSize\"\r\n\t\t\t\t\t:color=\"msg ? '#dd524d' : focusShow ? primaryColor : '#c0c4cc'\"\r\n\t\t\t\t\t@click=\"onClear\"\r\n\t\t\t\t></uni-icons>\r\n\t\t\t</template>\r\n\t\t\t<slot name=\"right\"></slot>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * Easyinput 输入框\r\n * @description 此组件可以实现表单的输入与校验，包括 \"text\" 和 \"textarea\" 类型。\r\n * @tutorial https://ext.dcloud.net.cn/plugin?id=3455\r\n * @property {String}\tvalue\t输入内容\r\n * @property {String }\ttype\t输入框的类型（默认text） password/text/textarea/..\r\n * \t@value text\t\t\t文本输入键盘\r\n * \t@value textarea\t多行文本输入键盘\r\n * \t@value password\t密码输入键盘\r\n * \t@value number\t\t数字输入键盘，注意iOS上app-vue弹出的数字键盘并非9宫格方式\r\n * \t@value idcard\t\t身份证输入键盘，信、支付宝、百度、QQ小程序\r\n * \t@value digit\t\t带小数点的数字键盘\t，App的nvue页面、微信、支付宝、百度、头条、QQ小程序支持\r\n * @property {Boolean}\tclearable\t是否显示右侧清空内容的图标控件，点击可清空输入框内容（默认true）\r\n * @property {Boolean}\tautoHeight\t是否自动增高输入区域，type为textarea时有效（默认true）\r\n * @property {String }\tplaceholder\t输入框的提示文字\r\n * @property {String }\tplaceholderStyle\tplaceholder的样式(内联样式，字符串)，如\"color: #ddd\"\r\n * @property {Boolean}\tfocus\t是否自动获得焦点（默认false）\r\n * @property {Boolean}\tdisabled\t是否禁用（默认false）\r\n * @property {Number }\tmaxlength\t最大输入长度，设置为 -1 的时候不限制最大长度（默认140）\r\n * @property {String }\tconfirmType\t设置键盘右下角按钮的文字，仅在type=\"text\"时生效（默认done）\r\n * @property {Number }\tclearSize\t清除图标的大小，单位px（默认15）\r\n * @property {String}\tprefixIcon\t输入框头部图标\r\n * @property {String}\tsuffixIcon\t输入框尾部图标\r\n * @property {String}\tprimaryColor\t设置主题色（默认#2979ff）\r\n * @property {Boolean}\ttrim\t是否自动去除两端的空格\r\n * @property {Boolean}\tcursorSpacing\t指定光标与键盘的距离，单位 px\r\n * @value both\t去除两端空格\r\n * @value left\t去除左侧空格\r\n * @value right\t去除右侧空格\r\n * @value start\t去除左侧空格\r\n * @value end\t\t去除右侧空格\r\n * @value all\t\t去除全部空格\r\n * @value none\t不去除空格\r\n * @property {Boolean}\tinputBorder\t是否显示input输入框的边框（默认true）\r\n * @property {Boolean}\tpasswordIcon\ttype=password时是否显示小眼睛图标\r\n * @property {Object}\tstyles\t自定义颜色\r\n * @event {Function}\tinput\t输入框内容发生变化时触发\r\n * @event {Function}\tfocus\t输入框获得焦点时触发\r\n * @event {Function}\tblur\t输入框失去焦点时触发\r\n * @event {Function}\tconfirm\t点击完成按钮时触发\r\n * @event {Function}\ticonClick\t点击图标时触发\r\n * @example <uni-easyinput v-model=\"mobile\"></uni-easyinput>\r\n */\r\nfunction obj2strClass(obj) {\r\n\tlet classess = '';\r\n\tfor (let key in obj) {\r\n\t\tconst val = obj[key];\r\n\t\tif (val) {\r\n\t\t\tclassess += `${key} `;\r\n\t\t}\r\n\t}\r\n\treturn classess;\r\n}\r\n\r\nfunction obj2strStyle(obj) {\r\n\tlet style = '';\r\n\tfor (let key in obj) {\r\n\t\tconst val = obj[key];\r\n\t\tstyle += `${key}:${val};`;\r\n\t}\r\n\treturn style;\r\n}\r\nexport default {\r\n\tname: 'uni-easyinput',\r\n\temits: ['click', 'iconClick', 'update:modelValue', 'input', 'focus', 'blur', 'confirm', 'clear', 'eyes', 'change', 'keyboardheightchange'],\r\n\tmodel: {\r\n\t\tprop: 'modelValue',\r\n\t\tevent: 'update:modelValue'\r\n\t},\r\n\toptions: {\r\n\t\tvirtualHost: true\r\n\t},\r\n\tinject: {\r\n\t\tform: {\r\n\t\t\tfrom: 'uniForm',\r\n\t\t\tdefault: null\r\n\t\t},\r\n\t\tformItem: {\r\n\t\t\tfrom: 'uniFormItem',\r\n\t\t\tdefault: null\r\n\t\t}\r\n\t},\r\n\tprops: {\r\n\t\tname: String,\r\n\t\tvalue: [Number, String],\r\n\t\tmodelValue: [Number, String],\r\n\t\ttype: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'text'\r\n\t\t},\r\n\t\tclearable: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\tautoHeight: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tplaceholder: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ' '\r\n\t\t},\r\n\t\tplaceholderStyle: String,\r\n\t\tfocus: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tdisabled: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tmaxlength: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 140\r\n\t\t},\r\n\t\tconfirmType: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'done'\r\n\t\t},\r\n\t\tclearSize: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 24\r\n\t\t},\r\n\t\tinputBorder: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\tprefixIcon: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tsuffixIcon: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\ttrim: {\r\n\t\t\ttype: [Boolean, String],\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tcursorSpacing: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\tpasswordIcon: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\tprimaryColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#2979ff'\r\n\t\t},\r\n\t\tstyles: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tcolor: '#333',\r\n\t\t\t\t\tbackgroundColor: '#fff',\r\n\t\t\t\t\tdisableColor: '#F7F6F6',\r\n\t\t\t\t\tborderColor: '#e5e5e5'\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t},\r\n\t\terrorMessage: {\r\n\t\t\ttype: [String, Boolean],\r\n\t\t\tdefault: ''\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tfocused: false,\r\n\t\t\tval: '',\r\n\t\t\tshowMsg: '',\r\n\t\t\tborder: false,\r\n\t\t\tisFirstBorder: false,\r\n\t\t\tshowClearIcon: false,\r\n\t\t\tshowPassword: false,\r\n\t\t\tfocusShow: false,\r\n\t\t\tlocalMsg: '',\r\n\t\t\tisEnter: false // 用于判断当前是否是使用回车操作\r\n\t\t};\r\n\t},\r\n\tcomputed: {\r\n\t\t// 输入框内是否有值\r\n\t\tisVal() {\r\n\t\t\tconst val = this.val;\r\n\t\t\t// fixed by mehaotian 处理值为0的情况，字符串0不在处理范围\r\n\t\t\tif (val || val === 0) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t\treturn false;\r\n\t\t},\r\n\r\n\t\tmsg() {\r\n\t\t\t// console.log('computed', this.form, this.formItem);\r\n\t\t\t// if (this.form) {\r\n\t\t\t// \treturn this.errorMessage || this.formItem.errMsg;\r\n\t\t\t// }\r\n\t\t\t// TODO 处理头条 formItem 中 errMsg 不更新的问题\r\n\t\t\treturn this.localMsg || this.errorMessage;\r\n\t\t},\r\n\t\t// 因为uniapp的input组件的maxlength组件必须要数值，这里转为数值，用户可以传入字符串数值\r\n\t\tinputMaxlength() {\r\n\t\t\treturn Number(this.maxlength);\r\n\t\t},\r\n\r\n\t\t// 处理外层样式的style\r\n\t\tboxStyle() {\r\n\t\t\treturn `color:${this.inputBorder && this.msg ? '#e43d33' : this.styles.color};`;\r\n\t\t},\r\n\t\t// input 内容的类和样式处理\r\n\t\tinputContentClass() {\r\n\t\t\treturn obj2strClass({\r\n\t\t\t\t'is-input-border': this.inputBorder,\r\n\t\t\t\t'is-input-error-border': this.inputBorder && this.msg,\r\n\t\t\t\t'is-textarea': this.type === 'textarea',\r\n\t\t\t\t'is-disabled': this.disabled,\r\n\t\t\t\t'is-focused': this.focusShow\r\n\t\t\t});\r\n\t\t},\r\n\t\tinputContentStyle() {\r\n\t\t\tconst focusColor = this.focusShow ? this.primaryColor : this.styles.borderColor;\r\n\t\t\tconst borderColor = this.inputBorder && this.msg ? '#dd524d' : focusColor;\r\n\t\t\treturn obj2strStyle({\r\n\t\t\t\t'border-color': borderColor || '#e5e5e5',\r\n\t\t\t\t'background-color': this.disabled ? this.styles.disableColor : this.styles.backgroundColor\r\n\t\t\t});\r\n\t\t},\r\n\t\t// input右侧样式\r\n\t\tinputStyle() {\r\n\t\t\tconst paddingRight = this.type === 'password' || this.clearable || this.prefixIcon ? '' : '10px';\r\n\t\t\treturn obj2strStyle({\r\n\t\t\t\t'padding-right': paddingRight,\r\n\t\t\t\t'padding-left': this.prefixIcon ? '' : '10px'\r\n\t\t\t});\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\tvalue(newVal) {\r\n\t\t\tthis.val = newVal;\r\n\t\t},\r\n\t\tmodelValue(newVal) {\r\n\t\t\tthis.val = newVal;\r\n\t\t},\r\n\t\tfocus(newVal) {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.focused = this.focus;\r\n\t\t\t\tthis.focusShow = this.focus;\r\n\t\t\t});\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\tthis.init();\r\n\t\t// TODO 处理头条vue3 computed 不监听 inject 更改的问题（formItem.errMsg）\r\n\t\tif (this.form && this.formItem) {\r\n\t\t\tthis.$watch('formItem.errMsg', newVal => {\r\n\t\t\t\tthis.localMsg = newVal;\r\n\t\t\t});\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.$nextTick(() => {\r\n\t\t\tthis.focused = this.focus;\r\n\t\t\tthis.focusShow = this.focus;\r\n\t\t});\r\n\t},\r\n\tmethods: {\r\n\t\t/**\r\n\t\t * 初始化变量值\r\n\t\t */\r\n\t\tinit() {\r\n\t\t\tif (this.value || this.value === 0) {\r\n\t\t\t\tthis.val = this.value;\r\n\t\t\t} else if (this.modelValue || this.modelValue === 0 || this.modelValue === '') {\r\n\t\t\t\tthis.val = this.modelValue;\r\n\t\t\t} else {\r\n\t\t\t\tthis.val = null;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 点击图标时触发\r\n\t\t * @param {Object} type\r\n\t\t */\r\n\t\tonClickIcon(type) {\r\n\t\t\tthis.$emit('iconClick', type);\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 显示隐藏内容，密码框时生效\r\n\t\t */\r\n\t\tonEyes() {\r\n\t\t\tthis.showPassword = !this.showPassword;\r\n\t\t\tthis.$emit('eyes', this.showPassword);\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 输入时触发\r\n\t\t * @param {Object} event\r\n\t\t */\r\n\t\tonInput(event) {\r\n\t\t\tlet value = event.detail.value;\r\n\t\t\t// 判断是否去除空格\r\n\t\t\tif (this.trim) {\r\n\t\t\t\tif (typeof this.trim === 'boolean' && this.trim) {\r\n\t\t\t\t\tvalue = this.trimStr(value);\r\n\t\t\t\t}\r\n\t\t\t\tif (typeof this.trim === 'string') {\r\n\t\t\t\t\tvalue = this.trimStr(value, this.trim);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (this.errMsg) this.errMsg = '';\r\n\t\t\tthis.val = value;\r\n\t\t\t// TODO 兼容 vue2\r\n\t\t\tthis.$emit('input', value);\r\n\t\t\t// TODO　兼容　vue3\r\n\t\t\tthis.$emit('update:modelValue', value);\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 外部调用方法\r\n\t\t * 获取焦点时触发\r\n\t\t * @param {Object} event\r\n\t\t */\r\n\t\tonFocus() {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.focused = true;\r\n\t\t\t});\r\n\t\t\tthis.$emit('focus', null);\r\n\t\t},\r\n\r\n\t\t_Focus(event) {\r\n\t\t\tthis.focusShow = true;\r\n\t\t\tthis.$emit('focus', event);\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 外部调用方法\r\n\t\t * 失去焦点时触发\r\n\t\t * @param {Object} event\r\n\t\t */\r\n\t\tonBlur() {\r\n\t\t\tthis.focused = false;\r\n\t\t\tthis.$emit('focus', null);\r\n\t\t},\r\n\t\t_Blur(event) {\r\n\t\t\tlet value = event.detail.value;\r\n\t\t\tthis.focusShow = false;\r\n\t\t\tthis.$emit('blur', event);\r\n\t\t\t// 根据类型返回值，在event中获取的值理论上讲都是string\r\n\t\t\tif (this.isEnter === false) {\r\n\t\t\t\tthis.$emit('change', this.val);\r\n\t\t\t}\r\n\t\t\t// 失去焦点时参与表单校验\r\n\t\t\tif (this.form && this.formItem) {\r\n\t\t\t\tconst { validateTrigger } = this.form;\r\n\t\t\t\tif (validateTrigger === 'blur') {\r\n\t\t\t\t\tthis.formItem.onFieldChange();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 按下键盘的发送键\r\n\t\t * @param {Object} e\r\n\t\t */\r\n\t\tonConfirm(e) {\r\n\t\t\tthis.$emit('confirm', this.val);\r\n\t\t\tthis.isEnter = true;\r\n\t\t\tthis.$emit('change', this.val);\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.isEnter = false;\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 清理内容\r\n\t\t * @param {Object} event\r\n\t\t */\r\n\t\tonClear(event) {\r\n\t\t\tthis.val = '';\r\n\t\t\t// TODO 兼容 vue2\r\n\t\t\tthis.$emit('input', '');\r\n\t\t\t// TODO 兼容 vue2\r\n\t\t\t// TODO　兼容　vue3\r\n\t\t\tthis.$emit('update:modelValue', '');\r\n\t\t\t// 点击叉号触发\r\n\t\t\tthis.$emit('clear');\r\n\t\t},\r\n\r\n    /**\r\n     * 键盘高度发生变化的时候触发此事件\r\n     * 兼容性：微信小程序2.7.0+、App 3.1.0+\r\n     * @param {Object} event\r\n     */\r\n    onkeyboardheightchange(event) {\r\n      this.$emit(\"keyboardheightchange\",event);\r\n    },\r\n\r\n\t\t/**\r\n\t\t * 去除空格\r\n\t\t */\r\n\t\ttrimStr(str, pos = 'both') {\r\n\t\t\tif (pos === 'both') {\r\n\t\t\t\treturn str.trim();\r\n\t\t\t} else if (pos === 'left') {\r\n\t\t\t\treturn str.trimLeft();\r\n\t\t\t} else if (pos === 'right') {\r\n\t\t\t\treturn str.trimRight();\r\n\t\t\t} else if (pos === 'start') {\r\n\t\t\t\treturn str.trimStart();\r\n\t\t\t} else if (pos === 'end') {\r\n\t\t\t\treturn str.trimEnd();\r\n\t\t\t} else if (pos === 'all') {\r\n\t\t\t\treturn str.replace(/\\s+/g, '');\r\n\t\t\t} else if (pos === 'none') {\r\n\t\t\t\treturn str;\r\n\t\t\t}\r\n\t\t\treturn str;\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n$uni-error: #e43d33;\r\n$uni-border-1: #dcdfe6 !default;\r\n\r\n.uni-easyinput {\r\n\t/* #ifndef APP-NVUE */\r\n\twidth: 100%;\r\n\t/* #endif */\r\n\tflex: 1;\r\n\tposition: relative;\r\n\ttext-align: left;\r\n\tcolor: #333;\r\n\tfont-size: 14px;\r\n}\r\n\r\n.uni-easyinput__content {\r\n\tflex: 1;\r\n\t/* #ifndef APP-NVUE */\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\tbox-sizing: border-box;\r\n\t// min-height: 36px;\r\n\t/* #endif */\r\n\tflex-direction: row;\r\n\talign-items: center;\r\n\t// 处理border动画刚开始显示黑色的问题\r\n\tborder-color: #fff;\r\n\ttransition-property: border-color;\r\n\ttransition-duration: 0.3s;\r\n}\r\n\r\n.uni-easyinput__content-input {\r\n\t/* #ifndef APP-NVUE */\r\n\twidth: auto;\r\n\t/* #endif */\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n\tflex: 1;\r\n\tline-height: 1;\r\n\tfont-size: 14px;\r\n\theight: 35px;\r\n\t// min-height: 36px;\r\n}\r\n\r\n.uni-easyinput__placeholder-class {\r\n\tcolor: #999;\r\n\tfont-size: 12px;\r\n\t// font-weight: 200;\r\n}\r\n\r\n.is-textarea {\r\n\talign-items: flex-start;\r\n}\r\n\r\n.is-textarea-icon {\r\n\tmargin-top: 5px;\r\n}\r\n\r\n.uni-easyinput__content-textarea {\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n\tflex: 1;\r\n\tline-height: 1.5;\r\n\tfont-size: 14px;\r\n\tmargin: 6px;\r\n\tmargin-left: 0;\r\n\theight: 80px;\r\n\tmin-height: 80px;\r\n\t/* #ifndef APP-NVUE */\r\n\tmin-height: 80px;\r\n\twidth: auto;\r\n\t/* #endif */\r\n}\r\n\r\n.input-padding {\r\n\tpadding-left: 10px;\r\n}\r\n\r\n.content-clear-icon {\r\n\tpadding: 0 5px;\r\n}\r\n\r\n.label-icon {\r\n\tmargin-right: 5px;\r\n\tmargin-top: -1px;\r\n}\r\n\r\n// 显示边框\r\n.is-input-border {\r\n\t/* #ifndef APP-NVUE */\r\n\tdisplay: flex;\r\n\tbox-sizing: border-box;\r\n\t/* #endif */\r\n\tflex-direction: row;\r\n\talign-items: center;\r\n\tborder: 1px solid $uni-border-1;\r\n\tborder-radius: 4px;\r\n\t/* #ifdef MP-ALIPAY */\r\n\toverflow: hidden;\r\n\t/* #endif */\r\n}\r\n\r\n.uni-error-message {\r\n\tposition: absolute;\r\n\tbottom: -17px;\r\n\tleft: 0;\r\n\tline-height: 12px;\r\n\tcolor: $uni-error;\r\n\tfont-size: 12px;\r\n\ttext-align: left;\r\n}\r\n\r\n.uni-error-msg--boeder {\r\n\tposition: relative;\r\n\tbottom: 0;\r\n\tline-height: 22px;\r\n}\r\n\r\n.is-input-error-border {\r\n\tborder-color: $uni-error;\r\n\r\n\t.uni-easyinput__placeholder-class {\r\n\t\tcolor: mix(#fff, $uni-error, 50%);\r\n\t}\r\n}\r\n\r\n.uni-easyinput--border {\r\n\tmargin-bottom: 0;\r\n\tpadding: 10px 15px;\r\n\t// padding-bottom: 0;\r\n\tborder-top: 1px #eee solid;\r\n}\r\n\r\n.uni-easyinput-error {\r\n\tpadding-bottom: 0;\r\n}\r\n\r\n.is-first-border {\r\n\t/* #ifndef APP-NVUE */\r\n\tborder: none;\r\n\t/* #endif */\r\n\t/* #ifdef APP-NVUE */\r\n\tborder-width: 0;\r\n\t/* #endif */\r\n}\r\n\r\n.is-disabled {\r\n\tbackground-color: #f7f6f6;\r\n\tcolor: #d5d5d5;\r\n\r\n\t.uni-easyinput__placeholder-class {\r\n\t\tcolor: #d5d5d5;\r\n\t\tfont-size: 12px;\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-easyinput.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-easyinput.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752425864230\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}