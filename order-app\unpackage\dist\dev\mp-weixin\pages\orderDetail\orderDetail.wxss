@charset "UTF-8";
/**
 * uni-app内置的常用样式变量
 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.order-detail-container {
  padding: 30rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
}
.order-detail-container .detail-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.order-detail-container .detail-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #ebeef5;
}
.order-detail-container .detail-card .card-header .card-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #2c3e50;
}
.order-detail-container .detail-card .card-header .order-status {
  font-size: 28rpx;
  padding: 6rpx 20rpx;
  border-radius: 6rpx;
}
.order-detail-container .detail-card .card-header .order-status.status-0 {
  background-color: #f4f4f5;
  color: #909399;
}
.order-detail-container .detail-card .card-header .order-status.status-1 {
  background-color: #ecf5ff;
  color: #409eff;
}
.order-detail-container .detail-card .card-header .order-status.status-2 {
  background-color: #f0f9eb;
  color: #67c23a;
}
.order-detail-container .detail-card .info-list .info-item {
  display: flex;
  margin-bottom: 20rpx;
}
.order-detail-container .detail-card .info-list .info-item .label {
  width: 200rpx;
  color: #606266;
  font-size: 28rpx;
}
.order-detail-container .detail-card .info-list .info-item .value {
  flex: 1;
  color: #2c3e50;
  font-size: 28rpx;
}
.order-detail-container .detail-card .image-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}
.order-detail-container .detail-card .image-list .image-item {
  width: calc(33.33% - 20rpx);
  height: 200rpx;
  margin: 10rpx;
  border-radius: 8rpx;
  background-color: #f8f9fa;
}
.order-detail-container .detail-card .task-list .task-item {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}
.order-detail-container .detail-card .task-list .task-item:last-child {
  margin-bottom: 0;
}
.order-detail-container .detail-card .task-list .task-item .task-main {
  padding: 24rpx;
  cursor: pointer;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}
.order-detail-container .detail-card .task-list .task-item .task-main:active {
  background-color: #f0f2f5;
}
.order-detail-container .detail-card .task-list .task-item .task-main .task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.order-detail-container .detail-card .task-list .task-item .task-main .task-header .task-header-left {
  display: flex;
  align-items: center;
}
.order-detail-container .detail-card .task-list .task-item .task-main .task-header .task-header-left .expand-icon {
  margin-right: 12rpx;
  transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
}
.order-detail-container .detail-card .task-list .task-item .task-main .task-header .task-header-left .task-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e3a8a;
}
.order-detail-container .detail-card .task-list .task-item .task-main .task-header .task-status {
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 6rpx;
  font-weight: 500;
}
.order-detail-container .detail-card .task-list .task-item .task-main .task-header .task-status.status-0 {
  background-color: #f4f4f5;
  color: #909399;
}
.order-detail-container .detail-card .task-list .task-item .task-main .task-header .task-status.status-1 {
  background-color: #ecf5ff;
  color: #409eff;
}
.order-detail-container .detail-card .task-list .task-item .task-main .task-header .task-status.status-2 {
  background-color: #f0f9eb;
  color: #67c23a;
}
.order-detail-container .detail-card .task-list .task-item .task-main .task-info .info-row {
  display: flex;
  margin-bottom: 12rpx;
}
.order-detail-container .detail-card .task-list .task-item .task-main .task-info .info-row:last-child {
  margin-bottom: 0;
}
.order-detail-container .detail-card .task-list .task-item .task-main .task-info .info-row .info-label {
  width: 140rpx;
  color: #606266;
  font-size: 26rpx;
}
.order-detail-container .detail-card .task-list .task-item .task-main .task-info .info-row .info-value {
  flex: 1;
  color: #2c3e50;
  font-size: 26rpx;
  font-weight: 500;
}
.order-detail-container .detail-card .task-list .task-item .task-details {
  padding: 20rpx 24rpx;
  background-color: #ffffff;
  border-top: 1px solid #ebeef5;
  -webkit-animation: slideDown 0.3s ease-out;
          animation: slideDown 0.3s ease-out;
}
@-webkit-keyframes slideDown {
from {
    opacity: 0;
    -webkit-transform: translateY(-10px);
            transform: translateY(-10px);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes slideDown {
from {
    opacity: 0;
    -webkit-transform: translateY(-10px);
            transform: translateY(-10px);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-title {
  font-size: 28rpx;
  color: #606266;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-title::before {
  content: "";
  display: inline-block;
  width: 4rpx;
  height: 24rpx;
  background-color: #1e3a8a;
  margin-right: 12rpx;
  border-radius: 2rpx;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-list .assignment-item {
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  cursor: pointer;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-list .assignment-item:active {
  opacity: 0.8;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-list .assignment-item:last-child {
  margin-bottom: 0;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-list .assignment-item .assignment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1px dashed #ebeef5;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-list .assignment-item .assignment-header .assignee-name {
  font-size: 28rpx;
  color: #1e3a8a;
  font-weight: 600;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-list .assignment-item .assignment-header .assignment-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-list .assignment-item .assignment-header .assignment-actions .assignment-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 4rpx;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-list .assignment-item .assignment-header .assignment-actions .assignment-status.status-1 {
  background-color: #ecf5ff;
  color: #409eff;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-list .assignment-item .assignment-header .assignment-actions .assignment-status.status-2 {
  background-color: #f0f9eb;
  color: #67c23a;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-list .assignment-item .assignment-header .assignment-actions .delete-btn {
  padding: 8rpx;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-list .assignment-item .assignment-header .assignment-actions .delete-btn:active {
  opacity: 0.7;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-list .assignment-item .assignment-details .detail-row {
  display: flex;
  margin-bottom: 8rpx;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-list .assignment-item .assignment-details .detail-row:last-child {
  margin-bottom: 0;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-list .assignment-item .assignment-details .detail-row .detail-label {
  width: 140rpx;
  color: #909399;
  font-size: 24rpx;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-list .assignment-item .assignment-details .detail-row .detail-value {
  flex: 1;
  color: #606266;
  font-size: 24rpx;
}
.order-detail-container .detail-card .task-list .task-item .task-details .assignment-info .assignment-list .no-assignment {
  text-align: center;
  padding: 30rpx 0;
  color: #909399;
  font-size: 28rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}
.order-detail-container .detail-card .task-list .task-item .task-footer {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: flex-end;
}
.order-detail-container .detail-card .task-list .task-item .task-footer .assign-btn {
  width: 200rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 28rpx;
  color: #ffffff;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  border: none;
  border-radius: 6rpx;
}
.order-detail-container .detail-card .task-list .task-item .task-footer .assign-btn::after {
  border: none;
}
.order-detail-container .detail-card .task-list .task-item .task-footer .assign-btn:active {
  opacity: 0.8;
}
.order-detail-container .user-picker-container {
  padding: 30rpx;
  max-height: 70vh;
  background-color: #ffffff;
}
.order-detail-container .user-picker-container .user-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #ebeef5;
}
.order-detail-container .user-picker-container .user-picker-header .user-picker-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #2c3e50;
}
.order-detail-container .user-picker-container .user-picker-header .user-picker-actions {
  display: flex;
  align-items: center;
}
.order-detail-container .user-picker-container .user-list {
  max-height: 60vh;
  overflow-y: auto;
  padding: 10rpx 0;
}
.order-detail-container .user-picker-container .user-list .user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 20rpx;
  margin-bottom: 16rpx;
  border-radius: 8rpx;
  background-color: #f8f9fa;
  transition: all 0.3s;
}
.order-detail-container .user-picker-container .user-list .user-item:active {
  opacity: 0.8;
}
.order-detail-container .user-picker-container .user-list .user-item.user-item-selected {
  background-color: rgba(30, 58, 138, 0.05);
  border-left: 4rpx solid #1e3a8a;
}
.order-detail-container .user-picker-container .user-list .user-item.user-item-selected .user-name {
  color: #1e3a8a;
  font-weight: 600;
}
.order-detail-container .user-picker-container .user-list .user-item .user-name {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
}
.order-detail-container .user-picker-container .quantity-input {
  padding: 20rpx 24rpx;
  border-top: 1px solid #ebeef5;
}
.order-detail-container .user-picker-container .quantity-input .input-label {
  font-size: 28rpx;
  color: #2c3e50;
  margin-bottom: 16rpx;
}
.order-detail-container .user-picker-container .quantity-input .input-wrapper .quantity-input-field {
  width: 100%;
  height: 80rpx;
  background-color: #f8f9fa;
  border: 1px solid #ebeef5;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #2c3e50;
  margin-bottom: 12rpx;
}
.order-detail-container .user-picker-container .quantity-input .input-wrapper .quantity-tip {
  font-size: 24rpx;
  color: #909399;
}
.order-detail-container .assignment-detail-container {
  padding: 30rpx;
  background-color: #ffffff;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}
.order-detail-container .assignment-detail-container .detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #ebeef5;
}
.order-detail-container .assignment-detail-container .detail-header .detail-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #2c3e50;
}
.order-detail-container .assignment-detail-container .detail-header .detail-actions {
  display: flex;
  align-items: center;
}
.order-detail-container .assignment-detail-container .detail-content {
  flex: 1;
  overflow-y: auto;
}
.order-detail-container .assignment-detail-container .detail-content .detail-item {
  margin-bottom: 24rpx;
}
.order-detail-container .assignment-detail-container .detail-content .detail-item .detail-label {
  display: block;
  font-size: 28rpx;
  color: #606266;
  margin-bottom: 12rpx;
}
.order-detail-container .assignment-detail-container .detail-content .detail-item .detail-value {
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: 500;
}
.order-detail-container .assignment-detail-container .detail-content .detail-item .detail-value.description-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #606266;
  font-weight: normal;
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #1e3a8a;
}
.order-detail-container .assignment-detail-container .detail-content .detail-item .image-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}
.order-detail-container .assignment-detail-container .detail-content .detail-item .image-list .image-item {
  width: calc(33.33% - 20rpx);
  height: 200rpx;
  margin: 10rpx;
  border-radius: 8rpx;
  background-color: #f8f9fa;
}
.order-detail-container .assignment-detail-container .detail-content .detail-item .image-list .assignment-image {
  width: calc(25% - 20rpx);
  height: 120rpx;
  margin: 10rpx;
  border-radius: 6rpx;
  background-color: #f8f9fa;
}
.order-detail-container .repair-button-container {
  margin-bottom: 30rpx;
  text-align: right;
}
.order-detail-container .repair-button-container .repair-btn {
  padding: 12rpx 24rpx;
  font-size: 28rpx;
  color: #ffffff;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  border: none;
  border-radius: 6rpx;
}
.order-detail-container .repair-button-container .repair-btn:active {
  opacity: 0.8;
}
.order-detail-container .maintenance-form-container {
  padding: 30rpx;
  background-color: #ffffff;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}
.order-detail-container .maintenance-form-container .form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #ebeef5;
}
.order-detail-container .maintenance-form-container .form-header .form-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #2c3e50;
}
.order-detail-container .maintenance-form-container .form-header .form-actions {
  display: flex;
  align-items: center;
}
.order-detail-container .maintenance-form-container .form-content {
  flex: 1;
  overflow-y: auto;
}
.order-detail-container .maintenance-form-container .form-content .form-item {
  margin-bottom: 24rpx;
}
.order-detail-container .maintenance-form-container .form-content .form-item .form-label {
  display: block;
  font-size: 28rpx;
  color: #606266;
  margin-bottom: 12rpx;
}
.order-detail-container .maintenance-form-container .form-content .form-item .form-textarea {
  width: 100%;
  min-height: 160rpx;
  background-color: #f8f9fa;
  border: 1px solid #ebeef5;
  border-radius: 8rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #2c3e50;
  line-height: 1.6;
  box-sizing: border-box;
  resize: none;
}
.order-detail-container .maintenance-form-container .form-content .form-item .char-count {
  text-align: right;
  margin-top: 8rpx;
}
.order-detail-container .maintenance-form-container .form-content .form-item .char-count text {
  font-size: 24rpx;
  color: #909399;
}
.order-detail-container .maintenance-form-container .form-content .form-item .upload-container {
  position: relative;
}
.order-detail-container .maintenance-form-container .form-content .form-item .upload-container .upload-progress {
  margin-top: 16rpx;
  padding: 16rpx;
  background-color: rgba(30, 58, 138, 0.05);
  border-radius: 8rpx;
  border: 1px solid rgba(30, 58, 138, 0.1);
}
.order-detail-container .maintenance-form-container .form-content .form-item .upload-container .upload-progress .progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #ebeef5;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
}
.order-detail-container .maintenance-form-container .form-content .form-item .upload-container .upload-progress .progress-bar .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1e3a8a 0%, #3b82f6 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
  position: relative;
}
.order-detail-container .maintenance-form-container .form-content .form-item .upload-container .upload-progress .progress-bar .progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  -webkit-animation: shimmer 1.5s infinite;
          animation: shimmer 1.5s infinite;
}
.order-detail-container .maintenance-form-container .form-content .form-item .upload-container .upload-progress .progress-text {
  font-size: 24rpx;
  color: #1e3a8a;
  text-align: center;
  display: block;
  font-weight: 500;
}
.order-detail-container .maintenance-form-container .form-content .form-item:last-child {
  margin-bottom: 0;
}
@-webkit-keyframes shimmer {
0% {
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
}
100% {
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
}
}
@keyframes shimmer {
0% {
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
}
100% {
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
}
}
