{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/orderDetail/orderDetail.vue?3f31", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/orderDetail/orderDetail.vue?b039", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/orderDetail/orderDetail.vue?4c74", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/orderDetail/orderDetail.vue?d605", "uni-app:///pages/orderDetail/orderDetail.vue", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/orderDetail/orderDetail.vue?fefd", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/orderDetail/orderDetail.vue?46b0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "orderDetail", "orderItemLists", "baseUrl", "userList", "showUserPickerFlag", "selected<PERSON>ser", "currentTask", "assignQuantity", "taskAssignmentsParams", "assignedToUserId", "assignedQuantity", "workOrderItemId", "expandedTasks", "showAssignmentDetailFlag", "currentAssignment", "userId", "roles", "showMaintenanceFormFlag", "maintenanceFileList", "maintenanceFormData", "pic", "description", "repairGoodNo", "isUploading", "uploadProgress", "computed", "imageList", "assignmentImageList", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "maintenanceTask", "isMaintenanceAssignment", "isShippingAssignment", "canDeleteAssignment", "canAssignTask", "onLoad", "methods", "getDetail", "pageSize", "workOrderId", "itemRes", "task", "assignments", "assignRes", "console", "uni", "title", "icon", "getStatusText", "getTaskStatusText", "getAssignmentStatusText", "previewImage", "urls", "current", "showUserPicker", "res", "selectUser", "confirmUser", "quantity", "isNaN", "status", "itemStatus", "closeUserPicker", "toggleTask", "handleAssignmentClick", "nick<PERSON><PERSON>", "goodQuantity", "defectiveQuantity", "assignmentStatus", "taskName", "locationAddress", "createTime", "showAssignmentDetail", "closeAssignmentDetail", "previewAssignmentImage", "handleDeleteAssignment", "assignmentId", "taskId", "content", "success", "showMaintenanceForm", "closeMaintenanceForm", "afterMaintenanceRead", "mask", "file", "progressTimer", "url", "filePath", "name", "uploadErr", "uploadRes", "clearInterval", "result", "newPic", "message", "setTimeout", "duration", "deleteMaintenancePic", "pics", "submitMaintenanceRecord", "maintenanceTaskItem", "createTaskParams", "productionTaskId", "plannedQuantity", "itemGoodQuantity", "itemDefectiveQuantity", "createResult", "submitParams", "isMaintenanceTask", "getAssignmentImages", "previewAssignmentImageFromList", "isShippingTask"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1GA;AAAA;AAAA;AAAA;AAAsoB,CAAgB,qnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC6Y1pB;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;QAAA;MAAA;IACA;IACAC;MAAA;MACA;MACA;QAAA;MAAA;IACA;IACAC;MACA;IACA;IACAC;MACA;QAAA;MAAA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IAAA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QAAA;UAAA;UAAA;YAAA;cAAA;gBAAA;kBACA;kBAAA;kBAAA,OAEA;oBAAAC;oBAAAC;kBAAA;gBAAA;kBAAAC;kBACA;kBACA;oBAAA,uCACAC;sBACAC;oBAAA;kBAAA,CACA;;kBAEA;kBAAA,uCACA;kBAAA;kBAAA;gBAAA;kBAAA;oBAAA;oBAAA;kBAAA;kBAAAD;kBAAA;kBAAA,OACA;oBACA7B;kBACA;gBAAA;kBAFA+B;kBAGAF;kBACAG;gBAAA;kBAAA;kBAAA;gBAAA;kBAAA;kBAAA;gBAAA;kBAAA;kBAAA;kBAAA;gBAAA;kBAAA;kBAAA;kBAAA;gBAAA;kBAGAA;kBACAA;gBAAA;gBAAA;kBAAA;cAAA;YAAA;UAAA;QAAA,CACA;QAAA;UAAA;QAAA;MAAA;QACAA;QACAC;UACAC;UACAC;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACAN;QACAO;QACAC;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAX;gBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAS;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAZ;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIAW;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBACAd;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAIAW;kBAAA;kBAAA;gBAAA;gBACAb;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAKA;gBACA;kBACArC;kBACAC;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;kBACA2B;kBACAqB;gBACA;cAAA;gBAAA;gBAAA,OAIA;kBACAhD;kBACAiD;gBACA;cAAA;gBAEAhB;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAH;gBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAe;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MAEA;QACA;QACA;UACAC;UACAC;UACAC;UACA9C;UACAC;UACA8C;UACAC;UACAC;UACA/C;UACAgD;QACA;QAEA;MACA;QACA3B;QACAC;UACAC;UACAC;QACA;MACA;IACA;IACA;IACAyB;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA7B;QACAO;QACAC;MACA;IACA;IACA;IACAsB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAhC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIA;kBACAF;oBACAC;oBACAgC;oBACAC;sBAAA;wBAAA;wBAAA;0BAAA;4BAAA;8BAAA;gCAAA,KACAxB;kCAAA;kCAAA;gCAAA;gCAAA;gCAAA,OACA;8BAAA;gCAAA;gCAAA,OAGA;kCACA3C;gCACA;8BAAA;gCAFA+B;gCAAA,MAKA;kCAAA;kCAAA;gCAAA;gCAAA;gCAAA,OACA;kCACA/B;kCACAiD;gCACA;8BAAA;gCAGAhB;kCACAC;kCACAC;gCACA;;gCAEA;gCACA;8BAAA;8BAAA;gCAAA;4BAAA;0BAAA;wBAAA;sBAAA,CAEA;sBAAA;wBAAA;sBAAA;sBAAA;oBAAA;kBACA;gBACA;kBACAH;kBACAC;oBACAC;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAiC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA5D;QACAC;QACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACA2D;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAtC;;gBAEA;gBACA;gBACA;;gBAEA;gBACAC;kBACAC;kBACAqC;gBACA;gBAAA;gBAGAC,mBAEA;gBACAC;kBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA,OAEAxC;kBACAyC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAAA;gBAJAC;gBAAAC;gBAMA;gBACAC;gBACA;gBAAA,KAEAF;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGAG;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAC;gBACA;kBACA;gBACA;kBACA;gBACA;;gBAEA;gBACA;kBAAA;gBAAA;kBAAA;oBACAP;oBACA1B;oBACAkC;kBACA;gBAAA;;gBAEA;gBACAC;kBACAlD;kBACAA;oBACAC;oBACAC;oBACAiD;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIApD;;gBAEA;gBACA;gBACA;gBAEAC;gBACAA;kBACAC;kBACAC;kBACAiD;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAC;MAAA;MACArD;MACA;QAAA;MAAA;MACAsD;MACA;;MAEA;MACA;QAAA;UACAZ;UACA1B;UACAkC;QACA;MAAA;IACA;IACA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAtD;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAKA;gBACAqD;kBAAA;gBAAA,IAEA;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAC;kBACA9D;kBACA+D;kBAAA;kBACAC;kBACA1C;kBAAA;kBACA2C;kBACAC;gBACA;gBAEA7D;gBAAA;gBAAA,OACA;cAAA;gBAAA8D;gBACA9D;gBAAA,MAEA8D;kBAAA;kBAAA;gBAAA;gBACA;gBACA7D;kBACAC;kBACAqC;gBACA;;gBAEA;gBAAA;gBAAA,OACA;kBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBACAvC;gBACAA;;gBAEA;gBACAwD;kBACAxD;kBACA;gBACA;gBACAA;;gBAEA;gBAAA,IACAwD;kBAAA;kBAAA;gBAAA;gBACAxD;gBACAC;kBACAC;kBACAqC;gBACA;gBAAA;gBAAA,OACA;kBAAA;gBAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBACAvC;gBACAwD;kBAAA;gBAAA;gBACAxD;cAAA;gBAGA;gBACAC;gBAAA;gBAAA;cAAA;gBAEAA;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAMAqD;kBAAA;kBAAA;gBAAA;gBACAvD;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBACA4D;kBACAjG;kBACAE;kBACAD;kBAAA;kBACAU;kBACAC;kBACAC;gBACA;gBAEAqB;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAEAC;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAH;gBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACA6D;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QAAA;MAAA;IACA;IACA;IACAC;MACAjE;QACAO;QACAC;MACA;IACA;IACA;IACA0D;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACj/BA;AAAA;AAAA;AAAA;AAAysC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACA7tC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/orderDetail/orderDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/orderDetail/orderDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderDetail.vue?vue&type=template&id=c547daf4&\"\nvar renderjs\nimport script from \"./orderDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./orderDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderDetail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/orderDetail/orderDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDetail.vue?vue&type=template&id=c547daf4&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uUpload: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-upload/u-upload\" */ \"@/uni_modules/uview-ui/components/u-upload/u-upload.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getStatusText(_vm.orderDetail && _vm.orderDetail.status)\n  var l1 = _vm.__map(_vm.orderItemLists, function (task, taskIndex) {\n    var $orig = _vm.__get_orig(task)\n    var m1 = _vm.getTaskStatusText(task.itemStatus)\n    var m2 = _vm.isMaintenanceTask(task.taskName)\n    var m4 = _vm.isShippingTask(task.taskName)\n    var m5 =\n      !_vm.isMaintenanceTask(task.taskName) &&\n      !_vm.isShippingTask(task.taskName)\n    var m6 = _vm.isMaintenanceTask(task.taskName)\n    var l0 = _vm.__map(\n      task.assignments || [],\n      function (assignment, assignmentIndex) {\n        var $orig = _vm.__get_orig(assignment)\n        var m3 = _vm.getAssignmentStatusText(assignment.assignmentStatus || \"1\")\n        return {\n          $orig: $orig,\n          m3: m3,\n        }\n      }\n    )\n    var g0 = !task.assignments || task.assignments.length === 0\n    return {\n      $orig: $orig,\n      m1: m1,\n      m2: m2,\n      m4: m4,\n      m5: m5,\n      m6: m6,\n      l0: l0,\n      g0: g0,\n    }\n  })\n  var g1 = (_vm.maintenanceFormData.repairGoodNo || \"\").length\n  if (!_vm._isMounted) {\n    _vm.e0 = function (assignment, task) {\n      var args = [],\n        len = arguments.length - 2\n      while (len-- > 0) args[len] = arguments[len + 2]\n\n      var _temp = args[args.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        assignment = _temp2.assignment,\n        task = _temp2.task\n      var _temp, _temp2\n      return _vm.handleAssignmentClick(assignment, task)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l1: l1,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDetail.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"order-detail-container\">\n\t\t<!-- 维修记录按钮 -->\n\t\t<view class=\"repair-button-container\" v-if=\"isRepairman\">\n\t\t\t<button class=\"repair-btn\" @click=\"showMaintenanceForm\">\n\t\t\t\t添加维修记录\n\t\t\t</button>\n\t\t</view>\n\t\t\n\t\t<!-- 工单基本信息卡片 -->\n\t\t<view class=\"detail-card\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<text class=\"card-title\">工单信息</text>\n\t\t\t\t<text class=\"order-status\" :class=\"'status-' + (orderDetail && orderDetail.status)\">\n\t\t\t\t\t{{ getStatusText(orderDetail && orderDetail.status) }}\n\t\t\t\t</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"info-list\">\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"label\">工单编号</text>\n\t\t\t\t\t<text class=\"value\">{{ orderDetail && orderDetail.workOrderNo }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"label\">产品名称</text>\n\t\t\t\t\t<text class=\"value\">{{ orderDetail && orderDetail.productName }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"label\">下单客户</text>\n\t\t\t\t\t<text class=\"value\">{{ orderDetail && orderDetail.customerName }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"label\">型号</text>\n\t\t\t\t\t<text class=\"value\">{{ orderDetail && orderDetail.modelType }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"label\">产品编号版本</text>\n\t\t\t\t\t<text class=\"value\">{{ orderDetail && orderDetail.orderProductNumberVersion }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"label\">计划数量</text>\n\t\t\t\t\t<text class=\"value\">{{ orderDetail && orderDetail.totalPlannedQuantity }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"label\">计划开始时间</text>\n\t\t\t\t\t<text class=\"value\">{{ orderDetail && orderDetail.overallPlannedStartTime }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"label\">计划结束时间</text>\n\t\t\t\t\t<text class=\"value\">{{ orderDetail && orderDetail.overallPlannedEndTime }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 装箱单图片卡片 -->\n\t\t<view class=\"detail-card\" v-if=\"orderDetail && orderDetail.packingListImagePath\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<text class=\"card-title\">装箱单图片</text>\n\t\t\t</view>\n\t\t\t<view class=\"image-list\">\n\t\t\t\t<image\n\t\t\t\t\tv-for=\"(image, imageIndex) in imageList\"\n\t\t\t\t\t:key=\"imageIndex\"\n\t\t\t\t\t:src=\"image\"\n\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\tclass=\"image-item\"\n\t\t\t\t\t@click=\"previewImage(imageIndex)\"\n\t\t\t\t></image>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 任务列表卡片 -->\n\t\t<view class=\"detail-card\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<text class=\"card-title\">任务列表</text>\n\t\t\t</view>\n\t\t\t<view class=\"task-list\">\n\t\t\t\t<view class=\"task-item\" v-for=\"(task, taskIndex) in orderItemLists\" :key=\"taskIndex\">\n\t\t\t\t\t<view class=\"task-main\" @click=\"toggleTask(task)\">\n\t\t\t\t\t\t<view class=\"task-header\">\n\t\t\t\t\t\t\t<view class=\"task-header-left\">\n\t\t\t\t\t\t\t\t<u-icon \n\t\t\t\t\t\t\t\t\t:name=\"expandedTasks[task.workOrderItemId] ? 'arrow-up' : 'arrow-down'\" \n\t\t\t\t\t\t\t\t\tsize=\"28\" \n\t\t\t\t\t\t\t\t\tcolor=\"#2c3e50\"\n\t\t\t\t\t\t\t\t\tclass=\"expand-icon\"\n\t\t\t\t\t\t\t\t></u-icon>\n\t\t\t\t\t\t\t\t<text class=\"task-name\">{{ task.taskName }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"task-status\" :class=\"'status-' + task.itemStatus\">\n\t\t\t\t\t\t\t\t{{ getTaskStatusText(task.itemStatus) }}\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"task-info\">\n\t\t\t\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t\t\t\t<text class=\"info-label\">计划数量：</text>\n\t\t\t\t\t\t\t\t<text class=\"info-value\">{{ task.plannedQuantity }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<template v-if=\"!isMaintenanceTask(task.taskName)\">\n\t\t\t\t\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t\t\t\t\t<text class=\"info-label\">良品数：</text>\n\t\t\t\t\t\t\t\t\t<text class=\"info-value\">{{ task.itemGoodQuantity }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t\t\t\t\t<text class=\"info-label\">不良品数：</text>\n\t\t\t\t\t\t\t\t\t<text class=\"info-value\">{{ task.itemDefectiveQuantity }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 指派信息（折叠部分） -->\n\t\t\t\t\t<view class=\"task-details\" v-show=\"expandedTasks[task.workOrderItemId]\">\n\t\t\t\t\t\t<view class=\"assignment-info\">\n\t\t\t\t\t\t\t<view class=\"assignment-title\">指派情况</view>\n\t\t\t\t\t\t\t<view class=\"assignment-list\">\n\t\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t\tclass=\"assignment-item\" \n\t\t\t\t\t\t\t\t\tv-for=\"(assignment, assignmentIndex) in (task.assignments || [])\" \n\t\t\t\t\t\t\t\t\t:key=\"assignmentIndex\"\n\t\t\t\t\t\t\t\t\t@tap=\"() => handleAssignmentClick(assignment, task)\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<view class=\"assignment-header\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"assignee-name\">{{ assignment.nickName || '未知用户' }}</text>\n\t\t\t\t\t\t\t\t\t\t<view class=\"assignment-actions\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"assignment-status\" :class=\"'status-' + (assignment.assignmentStatus || '1')\">\n\t\t\t\t\t\t\t\t\t\t\t\t{{ getAssignmentStatusText(assignment.assignmentStatus || '1') }}\n\t\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t\t\t\t\tv-if=\"canDeleteAssignment\"\n\t\t\t\t\t\t\t\t\t\t\t\tclass=\"delete-btn\" \n\t\t\t\t\t\t\t\t\t\t\t\t:data-assignment-id=\"assignment.assignmentId\"\n\t\t\t\t\t\t\t\t\t\t\t\t:data-task-id=\"task.workOrderItemId\"\n\t\t\t\t\t\t\t\t\t\t\t\**********=\"handleDeleteAssignment\"\n\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t<u-icon name=\"trash\" size=\"32\" color=\"#909399\"></u-icon>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"assignment-details\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"detail-row\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-label\">指派数量：</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-value\">{{ assignment.assignedQuantity || 0 }}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<!-- 发货任务显示地址 -->\n\t\t\t\t\t\t\t\t\t\t<view class=\"detail-row\" v-if=\"isShippingTask(task.taskName)\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-label\">地址：</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-value\">{{ assignment.locationAddress || '未填写' }}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<!-- 非维修任务且非发货任务显示良品数和不良品数 -->\n\t\t\t\t\t\t\t\t\t\t<template v-if=\"!isMaintenanceTask(task.taskName) && !isShippingTask(task.taskName)\">\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"detail-row\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-label\">良品数：</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-value\">{{ assignment.reportedGoodQuantity || 0 }}</text>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"detail-row\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-label\">不良品数：</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-value\">{{ assignment.reportedDefectiveQuantity || 0 }}</text>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t<!-- 维修任务显示说明 -->\n\t\t\t\t\t\t\t\t\t\t<template v-if=\"isMaintenanceTask(task.taskName)\">\n\t\t\t\t\t\t\t\t\t\t\t<!-- <view class=\"detail-row\" v-if=\"assignment.pic\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-label\">图片：</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"image-list\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-for=\"(image, imageIndex) in getAssignmentImages(assignment.pic)\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:key=\"imageIndex\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:src=\"image\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"assignment-image\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"previewAssignmentImageFromList(assignment.pic, imageIndex)\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t></image>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t</view> -->\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"detail-row\" v-if=\"assignment.description\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-label\">说明：</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-value description-text\">{{ assignment.description }}</text>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"no-assignment\" v-if=\"!task.assignments || task.assignments.length === 0\">\n\t\t\t\t\t\t\t\t\t<text>暂无指派信息</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"task-footer\" v-if=\"task.itemStatus === '0' && canAssignTask\">\n\t\t\t\t\t\t\t<button \n\t\t\t\t\t\t\t\tclass=\"assign-btn\"\n\t\t\t\t\t\t\t\************=\"showUserPicker(task)\"\n\t\t\t\t\t\t\t>指派</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 用户选择弹窗 -->\n\t\t<u-popup\n\t\t\tmode=\"bottom\"\n\t\t\t:show=\"showUserPickerFlag\"\n\t\t\t@close=\"closeUserPicker\"\n\t\t\tborder-radius=\"24\"\n\t\t\tsafe-area-inset-bottom\n\t\t>\n\t\t\t<view class=\"user-picker-container\">\n\t\t\t\t<view class=\"user-picker-header\">\n\t\t\t\t\t<text class=\"user-picker-title\">选择执行人</text>\n\t\t\t\t\t<view class=\"user-picker-actions\">\n\t\t\t\t\t\t<u-button plain size=\"mini\" @click=\"closeUserPicker\" style=\"margin-right: 20rpx;\">取消</u-button>\n\t\t\t\t\t\t<u-button type=\"primary\" size=\"mini\" @click=\"confirmUser\">确认</u-button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"user-list\">\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"user-item\"\n\t\t\t\t\t\tv-for=\"(user, userIndex) in userList\"\n\t\t\t\t\t\t:key=\"userIndex\"\n\t\t\t\t\t\t@click=\"selectUser(user)\"\n\t\t\t\t\t\t:class=\"{'user-item-selected': selectedUser && selectedUser.userId === user.userId}\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<text class=\"user-name\">{{ user.nickName }}</text>\n\t\t\t\t\t\t<u-icon\n\t\t\t\t\t\t\t:name=\"selectedUser && selectedUser.userId === user.userId ? 'checkmark-circle' : ''\"\n\t\t\t\t\t\t\t:color=\"selectedUser && selectedUser.userId === user.userId ? '#1e3a8a' : '#8c9fba'\"\n\t\t\t\t\t\t\tsize=\"40\"\n\t\t\t\t\t\t></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 计划数输入 -->\n\t\t\t\t<view class=\"quantity-input\" v-if=\"selectedUser\">\n\t\t\t\t\t<view class=\"input-label\">计划数量</view>\n\t\t\t\t\t<view class=\"input-wrapper\">\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\ttype=\"number\" \n\t\t\t\t\t\t\tv-model=\"assignQuantity\"\n\t\t\t\t\t\t\tclass=\"quantity-input-field\"\n\t\t\t\t\t\t\tplaceholder=\"请输入计划数量\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t\t<text class=\"quantity-tip\">最大可分配数量：{{ currentTask ? currentTask.plannedQuantity : 0 }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-popup>\n\n\t\t<!-- 指派详情弹窗 -->\n\t\t<u-popup\n\t\t\tmode=\"bottom\"\n\t\t\t:show=\"showAssignmentDetailFlag\"\n\t\t\t@close=\"closeAssignmentDetail\"\n\t\t\tborder-radius=\"24\"\n\t\t\tsafe-area-inset-bottom\n\t\t>\n\t\t\t<view class=\"assignment-detail-container\">\n\t\t\t\t<view class=\"detail-header\">\n\t\t\t\t\t<text class=\"detail-title\">指派详情</text>\n\t\t\t\t\t<view class=\"detail-actions\">\n\t\t\t\t\t\t<u-button plain size=\"mini\" @click=\"closeAssignmentDetail\">关闭</u-button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<scroll-view class=\"detail-content\" scroll-y>\n\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t<text class=\"detail-label\">执行人</text>\n\t\t\t\t\t\t<text class=\"detail-value\">{{ currentAssignment.nickName || '未知用户' }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 发货任务显示地址 -->\n\t\t\t\t\t<view class=\"detail-item\" v-if=\"isShippingAssignment\">\n\t\t\t\t\t\t<text class=\"detail-label\">地址</text>\n\t\t\t\t\t\t<text class=\"detail-value\">{{ currentAssignment.locationAddress || '未填写' }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 非维修任务且非发货任务显示良品数和不良品数 -->\n\t\t\t\t\t<template v-if=\"!isMaintenanceAssignment && !isShippingAssignment\">\n\t\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">良品数</text>\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{ currentAssignment.goodQuantity || 0 }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t\t<text class=\"detail-label\">不良品数</text>\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{ currentAssignment.defectiveQuantity || 0 }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</template>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"detail-item\" v-if=\"currentAssignment.pic\">\n\t\t\t\t\t\t<text class=\"detail-label\">{{ isMaintenanceAssignment ? '维修图片' : '图片' }}</text>\n\t\t\t\t\t\t<view class=\"image-list\">\n\t\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t\tv-for=\"(image, detailImageIndex) in assignmentImageList\"\n\t\t\t\t\t\t\t\t:key=\"detailImageIndex\"\n\t\t\t\t\t\t\t\t:src=\"image\"\n\t\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t\t\tclass=\"image-item\"\n\t\t\t\t\t\t\t\t@click=\"previewAssignmentImage(detailImageIndex)\"\n\t\t\t\t\t\t\t></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"detail-item\" v-if=\"isMaintenanceAssignment && currentAssignment.repairGoodNo\">\n\t\t\t\t\t\t<text class=\"detail-label\">维修产品编号</text>\n\t\t\t\t\t\t<text class=\"detail-value description-text\">{{ currentAssignment.repairGoodNo }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"detail-item\" v-if=\"isMaintenanceAssignment && currentAssignment.createTime\">\n\t\t\t\t\t\t<text class=\"detail-label\">维修时间</text>\n\t\t\t\t\t\t<text class=\"detail-value\">{{ currentAssignment.createTime }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"detail-item\" v-if=\"isMaintenanceAssignment && currentAssignment.description\">\n\t\t\t\t\t\t<text class=\"detail-label\">维修说明</text>\n\t\t\t\t\t\t<text class=\"detail-value description-text\">{{ currentAssignment.description }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</u-popup>\n\n\t\t<!-- 维修记录表单弹窗 -->\n\t\t<u-popup\n\t\t\tmode=\"bottom\"\n\t\t\t:show=\"showMaintenanceFormFlag\"\n\t\t\t@close=\"closeMaintenanceForm\"\n\t\t\tborder-radius=\"24\"\n\t\t\tsafe-area-inset-bottom\n\t\t>\n\t\t\t<view class=\"maintenance-form-container\">\n\t\t\t\t<view class=\"form-header\">\n\t\t\t\t\t<text class=\"form-title\">添加维修记录</text>\n\t\t\t\t\t<view class=\"form-actions\">\n\t\t\t\t\t\t<u-button plain size=\"mini\" @click=\"closeMaintenanceForm\" style=\"margin-right: 20rpx;\">取消</u-button>\n\t\t\t\t\t\t<u-button type=\"primary\" size=\"mini\" @click=\"submitMaintenanceRecord\" :disabled=\"isUploading\">\n\t\t\t\t\t\t\t{{ isUploading ? '上传中...' : '确认' }}\n\t\t\t\t\t\t</u-button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<scroll-view class=\"form-content\" scroll-y>\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">维修图片 *</text>\n\t\t\t\t\t\t<view class=\"upload-container\">\n\t\t\t\t\t\t\t<u-upload\n\t\t\t\t\t\t\t\t:fileList=\"maintenanceFileList\"\n\t\t\t\t\t\t\t\t@afterRead=\"afterMaintenanceRead\"\n\t\t\t\t\t\t\t\t@delete=\"deleteMaintenancePic\"\n\t\t\t\t\t\t\t\t:maxCount=\"9\"\n\t\t\t\t\t\t\t\t:maxSize=\"5242880\"\n\t\t\t\t\t\t\t\twidth=\"200\"\n\t\t\t\t\t\t\t\theight=\"200\"\n\t\t\t\t\t\t\t\t:previewFullImage=\"true\"\n\t\t\t\t\t\t\t\t:disabled=\"isUploading\"\n\t\t\t\t\t\t\t></u-upload>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 上传进度指示器 -->\n\t\t\t\t\t\t\t<view class=\"upload-progress\" v-if=\"isUploading\">\n\t\t\t\t\t\t\t\t<view class=\"progress-bar\">\n\t\t\t\t\t\t\t\t\t<view class=\"progress-fill\" :style=\"{ width: uploadProgress + '%' }\"></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<text class=\"progress-text\">上传中 {{ uploadProgress }}%</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">维修产品编号</text>\n\t\t\t\t\t\t<textarea \n\t\t\t\t\t\t\tv-model=\"maintenanceFormData.repairGoodNo\"\n\t\t\t\t\t\t\tclass=\"form-textarea\"\n\t\t\t\t\t\t\tplaceholder=\"请输入维修产品编号\"\n\t\t\t\t\t\t\t:auto-height=\"true\"\n\t\t\t\t\t\t\t:maxlength=\"500\"\n\t\t\t\t\t\t></textarea>\n\t\t\t\t\t\t<view class=\"char-count\">\n\t\t\t\t\t\t\t<text>{{ (maintenanceFormData.repairGoodNo || '').length }}/500</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<text class=\"form-label\">维修说明 *</text>\n\t\t\t\t\t\t<textarea \n\t\t\t\t\t\t\tv-model=\"maintenanceFormData.description\"\n\t\t\t\t\t\t\tclass=\"form-textarea\"\n\t\t\t\t\t\t\tplaceholder=\"请输入维修说明\"\n\t\t\t\t\t\t\t:auto-height=\"true\"\n\t\t\t\t\t\t\t:maxlength=\"500\"\n\t\t\t\t\t\t></textarea>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</u-popup>\n\t</view>\n</template>\n\n<script>\n\timport { getOrders,updateOrders } from \"@/api/work/orders.js\"\n\timport { listOrderItems,updateOrderItems,addOrderItems } from \"@/api/work/orderItems.js\"\n\timport { baseUrl } from \"../../config\"\n\timport { listUser } from \"@/api/system/user.js\"\n\timport { addTaskAssignments,listTaskAssignments,delTaskAssignments } from \"@/api/work/taskAssignments.js\"\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\torderDetail:null,\n\t\t\t\torderItemLists:[],\n\t\t\t\tbaseUrl,\n\t\t\t\tuserList: [],\n\t\t\t\tshowUserPickerFlag: false,\n\t\t\t\tselectedUser: null,\n\t\t\t\tcurrentTask: null,\n\t\t\t\tassignQuantity: '',\n\t\t\t\ttaskAssignmentsParams:{\n\t\t\t\t\tassignedToUserId:'',\n\t\t\t\t\tassignedQuantity:'',\n\t\t\t\t\tworkOrderItemId:''\n\t\t\t\t},\n\t\t\t\texpandedTasks: {}, // 用于存储任务的展开状态\n\t\t\t\tshowAssignmentDetailFlag: false,\n\t\t\t\tcurrentAssignment: null,\n\t\t\t\tuserId:this.$store.state.user.userId,\n\t\t\t\troles:this.$store.state.user.roles,\n\t\t\t\tshowMaintenanceFormFlag: false,\n\t\t\t\tmaintenanceFileList: [],\n\t\t\t\tmaintenanceFormData: {\n\t\t\t\t\tpic: '',\n\t\t\t\t\tdescription: '',\n\t\t\t\t\trepairGoodNo: ''\n\t\t\t\t},\n\t\t\t\tisUploading: false,\n\t\t\t\tuploadProgress: 0\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\timageList() {\n\t\t\t\tif (!this.orderDetail || !this.orderDetail.packingListImagePath) return [];\n\t\t\t\treturn this.orderDetail.packingListImagePath.split(',').map(path => this.baseUrl + path);\n\t\t\t},\n\t\t\tassignmentImageList() {\n\t\t\t\tif (!this.currentAssignment || !this.currentAssignment.pic) return [];\n\t\t\t\treturn this.currentAssignment.pic.split(',').map(path => this.baseUrl + path);\n\t\t\t},\n\t\t\tisRepairman() {\n\t\t\t\treturn this.roles.includes('repairman');\n\t\t\t},\n\t\t\tmaintenanceTask() {\n\t\t\t\treturn this.orderItemLists.find(task => task.taskName && task.taskName.includes('维修'));\n\t\t\t},\n\t\t\tisMaintenanceAssignment() {\n\t\t\t\treturn this.currentAssignment && this.currentAssignment.taskName && this.currentAssignment.taskName.includes('维修');\n\t\t\t},\n\t\t\tisShippingAssignment() {\n\t\t\t\treturn this.currentAssignment && this.currentAssignment.taskName && this.currentAssignment.taskName.includes('发货');\n\t\t\t},\n\t\t\tcanDeleteAssignment() {\n\t\t\t\treturn this.roles.includes('admin') || this.roles.includes('dispatcher');\n\t\t\t},\n\t\t\tcanAssignTask() {\n\t\t\t\treturn this.roles.includes('admin') || this.roles.includes('dispatcher');\n\t\t\t}\n\t\t},\n\t\tonLoad({id}) {\n\t\t\tthis.getDetail(id)\n\t\t},\n\t\tmethods: {\n\t\t\tgetDetail(id){\n\t\t\t\tgetOrders(id).then(async res=>{\n\t\t\t\t\tthis.orderDetail = res.data\n\t\t\t\t\t\n\t\t\t\t\tconst itemRes = await listOrderItems({pageSize:100000,workOrderId:id})\n\t\t\t\t\t// 确保每个任务都有assignments数组\n\t\t\t\t\tthis.orderItemLists = itemRes.rows.map(task => ({\n\t\t\t\t\t\t...task,\n\t\t\t\t\t\tassignments: []\n\t\t\t\t\t}))\n\t\t\t\t\t\n\t\t\t\t\t// 查询每个任务的指派情况\n\t\t\t\t\tfor(let task of this.orderItemLists) {\n\t\t\t\t\t\tconst assignRes = await listTaskAssignments({\n\t\t\t\t\t\t\tworkOrderItemId: task.workOrderItemId\n\t\t\t\t\t\t})\n\t\t\t\t\t\ttask.assignments = assignRes.rows || []\n\t\t\t\t\t\tconsole.log('任务指派信息：', task.workOrderItemId, assignRes.rows)\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('工单详情：', this.orderDetail)\n\t\t\t\t\tconsole.log('任务列表：', this.orderItemLists)\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tconsole.error('获取工单详情失败：', error)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取工单详情失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 获取工单状态文本\n\t\t\tgetStatusText(status) {\n\t\t\t\tconst statusMap = {\n\t\t\t\t\t'0': '未开始',\n\t\t\t\t\t'1': '处理中',\n\t\t\t\t\t'2': '已完成'\n\t\t\t\t}\n\t\t\t\treturn statusMap[status] || '未知状态'\n\t\t\t},\n\t\t\t// 获取任务状态文本\n\t\t\tgetTaskStatusText(status) {\n\t\t\t\tconst statusMap = {\n\t\t\t\t\t'0': '未开始',\n\t\t\t\t\t'1': '处理中',\n\t\t\t\t\t'2': '已完成'\n\t\t\t\t}\n\t\t\t\treturn statusMap[status] || '未知状态'\n\t\t\t},\n\t\t\t// 获取指派任务状态文本\n\t\t\tgetAssignmentStatusText(status) {\n\t\t\t\tconst statusMap = {\n\t\t\t\t\t'1': '处理中',\n\t\t\t\t\t'2': '已完成'\n\t\t\t\t}\n\t\t\t\treturn statusMap[status] || '未知状态'\n\t\t\t},\n\t\t\t// 预览图片\n\t\t\tpreviewImage(imageIndex) {\n\t\t\t\tuni.previewImage({\n\t\t\t\t\turls: this.imageList,\n\t\t\t\t\tcurrent: imageIndex\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 显示用户选择器\n\t\t\tasync showUserPicker(task) {\n\t\t\t\tthis.currentTask = task;\n\t\t\t\tthis.selectedUser = null;\n\t\t\t\tthis.assignQuantity = task.plannedQuantity || '';\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await listUser();\n\t\t\t\t\tif (res && res.rows) {\n\t\t\t\t\t\tthis.userList = res.rows;\n\t\t\t\t\t\tthis.showUserPickerFlag = true;\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取用户列表失败', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取用户列表失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 选择用户\n\t\t\tselectUser(user) {\n\t\t\t\tthis.selectedUser = user;\n\t\t\t},\n\t\t\t\n\t\t\t// 确认选择用户\n\t\t\tasync confirmUser() {\n\t\t\t\tif (!this.selectedUser) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请选择执行人',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (!this.assignQuantity) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入计划数量',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst quantity = Number(this.assignQuantity);\n\t\t\t\tif (isNaN(quantity) || quantity <= 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入有效的计划数量',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (quantity > this.currentTask.plannedQuantity) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '计划数量不能大于任务计划数',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 设置指派参数\n\t\t\t\t\tthis.taskAssignmentsParams = {\n\t\t\t\t\t\tassignedToUserId: this.selectedUser.userId,\n\t\t\t\t\t\tassignedQuantity: quantity,\n\t\t\t\t\t\tworkOrderItemId: this.currentTask.workOrderItemId\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 调用指派接口\n\t\t\t\t\tawait addTaskAssignments(this.taskAssignmentsParams)\n\t\t\t\t\t\n\t\t\t\t\t// 如果工单状态为未开始，更新工单状态为处理中\n\t\t\t\t\tif (this.orderDetail.status === '0') {\n\t\t\t\t\t\tawait updateOrders({\n\t\t\t\t\t\t\tworkOrderId: this.orderDetail.workOrderId,\n\t\t\t\t\t\t\tstatus: '1'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 更新任务状态为处理中\n\t\t\t\t\tawait updateOrderItems({\n\t\t\t\t\t\tworkOrderItemId: this.currentTask.workOrderItemId,\n\t\t\t\t\t\titemStatus: '1'\n\t\t\t\t\t})\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '指派成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t\t\n\t\t\t\t\t// 关闭弹窗\n\t\t\t\t\tthis.closeUserPicker()\n\t\t\t\t\t\n\t\t\t\t\t// 刷新页面数据\n\t\t\t\t\tthis.getDetail(this.orderDetail.workOrderId)\n\t\t\t\t\t\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('指派失败', error)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '指派失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 关闭用户选择器\n\t\t\tcloseUserPicker() {\n\t\t\t\tthis.showUserPickerFlag = false;\n\t\t\t\tthis.selectedUser = null;\n\t\t\t\tthis.currentTask = null;\n\t\t\t\tthis.assignQuantity = '';\n\t\t\t},\n\t\t\t// 切换任务展开状态\n\t\t\ttoggleTask(task) {\n\t\t\t\tthis.$set(this.expandedTasks, task.workOrderItemId, !this.expandedTasks[task.workOrderItemId])\n\t\t\t},\n\t\t\t// 处理指派点击\n\t\t\thandleAssignmentClick(assignment, task) {\n\t\t\t\tif (!assignment) return;\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\t// 确保assignment对象包含所需的所有属性\n\t\t\t\tthis.currentAssignment = {\n\t\t\t\t\tnickName: assignment.nickName || '未知用户',\n\t\t\t\t\tgoodQuantity: assignment.reportedGoodQuantity || 0,\n\t\t\t\t\tdefectiveQuantity: assignment.reportedDefectiveQuantity || 0,\n\t\t\t\t\tpic: assignment.pic || '',\n\t\t\t\t\tdescription: assignment.description || '',\n\t\t\t\t\tassignmentStatus: assignment.assignmentStatus || '1',\n\t\t\t\t\ttaskName: task ? task.taskName : '',\n\t\t\t\t\tlocationAddress: assignment.locationAddress || '',\n\t\t\t\t\trepairGoodNo: assignment.repairGoodNo || '',\n\t\t\t\t\tcreateTime: assignment.createTime || ''\n\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\tthis.showAssignmentDetailFlag = true;\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('处理指派点击失败：', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '操作失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 显示指派详情\n\t\t\tshowAssignmentDetail(assignment) {\n\t\t\t\tif (!assignment) return;\n\t\t\t\tthis.currentAssignment = assignment;\n\t\t\t\tthis.showAssignmentDetailFlag = true;\n\t\t\t},\n\t\t\t// 关闭指派详情\n\t\t\tcloseAssignmentDetail() {\n\t\t\t\tthis.showAssignmentDetailFlag = false;\n\t\t\t\tthis.currentAssignment = null;\n\t\t\t},\n\t\t\t// 预览指派图片\n\t\t\tpreviewAssignmentImage(detailImageIndex) {\n\t\t\t\tif (!this.assignmentImageList || !this.assignmentImageList.length) return;\n\t\t\t\tuni.previewImage({\n\t\t\t\t\turls: this.assignmentImageList,\n\t\t\t\t\tcurrent: detailImageIndex\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 处理删除指派\n\t\t\tasync handleDeleteAssignment(event) {\n\t\t\t\tconst assignmentId = event.currentTarget.dataset.assignmentId;\n\t\t\t\tconst taskId = event.currentTarget.dataset.taskId;\n\t\t\t\tif (!assignmentId || !taskId) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '无法获取指派信息',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '确定要删除这条指派记录吗？',\n\t\t\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tawait delTaskAssignments(assignmentId);\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 检查任务是否还有其他指派\n\t\t\t\t\t\t\t\tconst assignRes = await listTaskAssignments({\n\t\t\t\t\t\t\t\t\tworkOrderItemId: taskId\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 如果没有指派了，将任务状态改为未开始\n\t\t\t\t\t\t\t\tif (!assignRes || !assignRes.rows || assignRes.rows.length === 0) {\n\t\t\t\t\t\t\t\t\tawait updateOrderItems({\n\t\t\t\t\t\t\t\t\t\tworkOrderItemId: taskId,\n\t\t\t\t\t\t\t\t\t\titemStatus: '0' // 未开始\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 刷新页面数据\n\t\t\t\t\t\t\t\tthis.getDetail(this.orderDetail.workOrderId);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('删除指派失败：', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '删除失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 显示维修记录表单\n\t\t\tshowMaintenanceForm() {\n\t\t\t\tthis.showMaintenanceFormFlag = true;\n\t\t\t},\n\t\t\t// 关闭维修记录表单\n\t\t\tcloseMaintenanceForm() {\n\t\t\t\tthis.showMaintenanceFormFlag = false;\n\t\t\t\tthis.maintenanceFileList = [];\n\t\t\t\tthis.maintenanceFormData = {\n\t\t\t\t\tpic: '',\n\t\t\t\t\tdescription: '',\n\t\t\t\t\trepairGoodNo: ''\n\t\t\t\t};\n\t\t\t\t// 重置上传状态\n\t\t\t\tthis.isUploading = false;\n\t\t\t\tthis.uploadProgress = 0;\n\t\t\t},\n\t\t\t// 处理维修图片上传\n\t\t\tasync afterMaintenanceRead(event) {\n\t\t\t\tconsole.log('维修图片上传事件：', event);\n\t\t\t\t\n\t\t\t\t// 设置上传状态\n\t\t\t\tthis.isUploading = true;\n\t\t\t\tthis.uploadProgress = 0;\n\t\t\t\t\n\t\t\t\t// 显示上传中提示\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '图片上传中...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst file = event.file;\n\t\t\t\t\t\n\t\t\t\t\t// 模拟上传进度\n\t\t\t\t\tconst progressTimer = setInterval(() => {\n\t\t\t\t\t\tif (this.uploadProgress < 90) {\n\t\t\t\t\t\t\tthis.uploadProgress += 10;\n\t\t\t\t\t\t}\n\t\t\t\t\t}, 100);\n\t\t\t\t\t\n\t\t\t\t\tconst [uploadErr, uploadRes] = await uni.uploadFile({\n\t\t\t\t\t\turl: this.baseUrl + '/common/upload',\n\t\t\t\t\t\tfilePath: file.url,\n\t\t\t\t\t\tname: 'file'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 清除进度定时器\n\t\t\t\t\tclearInterval(progressTimer);\n\t\t\t\t\tthis.uploadProgress = 100;\n\t\t\t\t\t\n\t\t\t\t\tif (uploadErr) {\n\t\t\t\t\t\tthrow new Error('上传图片失败');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconst result = JSON.parse(uploadRes.data);\n\t\t\t\t\tif (result.code === 200) {\n\t\t\t\t\t\t// 更新图片路径，用逗号分隔多张图片\n\t\t\t\t\t\tconst newPic = result.fileName;\n\t\t\t\t\t\tif (this.maintenanceFormData.pic) {\n\t\t\t\t\t\t\tthis.maintenanceFormData.pic = this.maintenanceFormData.pic + ',' + newPic;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.maintenanceFormData.pic = newPic;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 更新文件列表显示\n\t\t\t\t\t\tthis.maintenanceFileList = this.maintenanceFormData.pic.split(',').filter(url => url).map(url => ({\n\t\t\t\t\t\t\turl: this.baseUrl + url,\n\t\t\t\t\t\t\tstatus: 'success',\n\t\t\t\t\t\t\tmessage: '上传成功'\n\t\t\t\t\t\t}));\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 延迟一下再隐藏加载\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '上传成功',\n\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tthis.isUploading = false;\n\t\t\t\t\t\t\tthis.uploadProgress = 0;\n\t\t\t\t\t\t}, 300);\n\t\t\t\t\t\t\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(result.msg || '上传失败');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('上传维修图片失败：', error);\n\t\t\t\t\t\n\t\t\t\t\t// 重置状态\n\t\t\t\t\tthis.isUploading = false;\n\t\t\t\t\tthis.uploadProgress = 0;\n\t\t\t\t\t\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: error.message || '上传图片失败',\n\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 删除维修图片\n\t\t\tdeleteMaintenancePic(event) {\n\t\t\t\tconsole.log('删除维修图片：', event);\n\t\t\t\tconst pics = this.maintenanceFormData.pic.split(',').filter(url => url);\n\t\t\t\tpics.splice(event.index, 1);\n\t\t\t\tthis.maintenanceFormData.pic = pics.join(',');\n\t\t\t\t\n\t\t\t\t// 更新文件列表显示\n\t\t\t\tthis.maintenanceFileList = pics.map(url => ({\n\t\t\t\t\turl: this.baseUrl + url,\n\t\t\t\t\tstatus: 'success',\n\t\t\t\t\tmessage: '上传成功'\n\t\t\t\t}));\n\t\t\t},\n\t\t\t// 提交维修记录\n\t\t\tasync submitMaintenanceRecord() {\n\t\t\t\t// 表单验证\n\t\t\t\tif (!this.maintenanceFormData.pic) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请上传维修图片',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (!this.maintenanceFormData.description) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请填写维修说明',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 首先尝试从现有任务列表中查找维修任务\n\t\t\t\t\tlet maintenanceTaskItem = this.orderItemLists.find(task => task.productionTaskId === 3);\n\t\t\t\t\t\n\t\t\t\t\t// 如果没有维修任务，先创建一个\n\t\t\t\t\tif (!maintenanceTaskItem) {\n\t\t\t\t\t\tconst createTaskParams = {\n\t\t\t\t\t\t\tworkOrderId: this.orderDetail.workOrderId,\n\t\t\t\t\t\t\tproductionTaskId: 3, // 维修任务ID\n\t\t\t\t\t\t\tplannedQuantity: this.orderDetail.totalPlannedQuantity,\n\t\t\t\t\t\t\titemStatus: '2', // 已完成状态\n\t\t\t\t\t\t\titemGoodQuantity: null,\n\t\t\t\t\t\t\titemDefectiveQuantity: null\n\t\t\t\t\t\t};\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log('创建维修任务参数：', createTaskParams);\n\t\t\t\t\t\tconst createResult = await addOrderItems(createTaskParams);\n\t\t\t\t\t\tconsole.log('创建维修任务结果：', createResult);\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (createResult && createResult.code === 200) {\n\t\t\t\t\t\t\t// 显示加载动画\n\t\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\t\ttitle: '正在创建维修任务...',\n\t\t\t\t\t\t\t\tmask: true\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 等待一下，确保数据库事务完成\n\t\t\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 500));\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 重新获取工单详情以获取新创建的维修任务\n\t\t\t\t\t\t\tawait this.getDetail(this.orderDetail.workOrderId);\n\t\t\t\t\t\t\tconsole.log('重新获取后的任务列表：', this.orderItemLists);\n\t\t\t\t\t\t\tconsole.log('查找维修任务，productionTaskId=3');\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 再次根据productionTaskId=3查找维修任务\n\t\t\t\t\t\t\tmaintenanceTaskItem = this.orderItemLists.find(task => {\n\t\t\t\t\t\t\t\tconsole.log('任务：', task.taskName, 'productionTaskId：', task.productionTaskId);\n\t\t\t\t\t\t\t\treturn task.productionTaskId === 3;\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tconsole.log('找到的维修任务：', maintenanceTaskItem);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 如果还是找不到，再试一次\n\t\t\t\t\t\t\tif (!maintenanceTaskItem) {\n\t\t\t\t\t\t\t\tconsole.log('第一次查找失败，等待1秒后重试...');\n\t\t\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\t\t\ttitle: '正在重试获取任务...',\n\t\t\t\t\t\t\t\t\tmask: true\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 1000));\n\t\t\t\t\t\t\t\tawait this.getDetail(this.orderDetail.workOrderId);\n\t\t\t\t\t\t\t\tconsole.log('重试后的任务列表：', this.orderItemLists);\n\t\t\t\t\t\t\t\tmaintenanceTaskItem = this.orderItemLists.find(task => task.productionTaskId === 3);\n\t\t\t\t\t\t\t\tconsole.log('重试后找到的维修任务：', maintenanceTaskItem);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 隐藏加载动画\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '创建维修任务失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 最终检查是否成功获取到维修任务\n\t\t\t\t\tif (!maintenanceTaskItem) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '无法获取维修任务信息',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 准备提交参数\n\t\t\t\t\tconst submitParams = {\n\t\t\t\t\t\tassignedToUserId: this.userId,\n\t\t\t\t\t\tworkOrderItemId: maintenanceTaskItem.workOrderItemId,\n\t\t\t\t\t\tassignedQuantity: 1, // 维修任务默认数量为1\n\t\t\t\t\t\tpic: this.maintenanceFormData.pic,\n\t\t\t\t\t\tdescription: this.maintenanceFormData.description,\n\t\t\t\t\t\trepairGoodNo: this.maintenanceFormData.repairGoodNo\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('提交维修记录参数：', submitParams);\n\t\t\t\t\t\n\t\t\t\t\t// 调用addTaskAssignments方法\n\t\t\t\t\tawait addTaskAssignments(submitParams);\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '维修记录添加成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 关闭弹窗\n\t\t\t\t\tthis.closeMaintenanceForm();\n\t\t\t\t\t\n\t\t\t\t\t// 刷新页面数据\n\t\t\t\t\tthis.getDetail(this.orderDetail.workOrderId);\n\t\t\t\t\t\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('提交维修记录失败：', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '提交失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 判断是否为维修任务\n\t\t\tisMaintenanceTask(taskName) {\n\t\t\t\treturn taskName && taskName.includes('维修');\n\t\t\t},\n\t\t\t// 获取指派图片\n\t\t\tgetAssignmentImages(pic) {\n\t\t\t\tif (!pic) return [];\n\t\t\t\treturn pic.split(',').map(path => this.baseUrl + path);\n\t\t\t},\n\t\t\t// 预览指派图片\n\t\t\tpreviewAssignmentImageFromList(pic, imageIndex) {\n\t\t\t\tuni.previewImage({\n\t\t\t\t\turls: this.getAssignmentImages(pic),\n\t\t\t\t\tcurrent: imageIndex\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 判断是否为发货任务\n\t\t\tisShippingTask(taskName) {\n\t\t\t\treturn taskName && taskName.includes('发货');\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n.order-detail-container {\n\tpadding: 30rpx;\n\tbackground-color: #f5f7fa;\n\tmin-height: 100vh;\n\t\n\t.detail-card {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n\t\t\n\t\t.card-header {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 30rpx;\n\t\t\tpadding-bottom: 20rpx;\n\t\t\tborder-bottom: 1px solid #ebeef5;\n\t\t\t\n\t\t\t.card-title {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #2c3e50;\n\t\t\t}\n\t\t\t\n\t\t\t.order-status {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tpadding: 6rpx 20rpx;\n\t\t\t\tborder-radius: 6rpx;\n\t\t\t\t\n\t\t\t\t&.status-0 {\n\t\t\t\t\tbackground-color: #f4f4f5;\n\t\t\t\t\tcolor: #909399;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.status-1 {\n\t\t\t\t\tbackground-color: #ecf5ff;\n\t\t\t\t\tcolor: #409eff;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.status-2 {\n\t\t\t\t\tbackground-color: #f0f9eb;\n\t\t\t\t\tcolor: #67c23a;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.info-list {\n\t\t\t.info-item {\n\t\t\t\tdisplay: flex;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\n\t\t\t\t.label {\n\t\t\t\t\twidth: 200rpx;\n\t\t\t\t\tcolor: #606266;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.value {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\tcolor: #2c3e50;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.image-list {\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t\tmargin: 0 -10rpx;\n\t\t\t\n\t\t\t.image-item {\n\t\t\t\twidth: calc(33.33% - 20rpx);\n\t\t\t\theight: 200rpx;\n\t\t\t\tmargin: 10rpx;\n\t\t\t\tborder-radius: 8rpx;\n\t\t\t\tbackground-color: #f8f9fa;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.task-list {\n\t\t\t.task-item {\n\t\t\t\tbackground-color: #f8f9fa;\n\t\t\t\tborder-radius: 12rpx;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\toverflow: hidden;\n\t\t\t\t\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.task-main {\n\t\t\t\t\tpadding: 24rpx;\n\t\t\t\t\tcursor: pointer;\n\t\t\t\t\tbackground-color: #f8f9fa;\n\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\t\n\t\t\t\t\t&:active {\n\t\t\t\t\t\tbackground-color: #f0f2f5;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.task-header {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\t\t\n\t\t\t\t\t\t.task-header-left {\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t.expand-icon {\n\t\t\t\t\t\t\t\tmargin-right: 12rpx;\n\t\t\t\t\t\t\t\ttransition: transform 0.3s;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t.task-name {\n\t\t\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\t\t\t\tcolor: #1e3a8a;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.task-status {\n\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\tpadding: 6rpx 20rpx;\n\t\t\t\t\t\t\tborder-radius: 6rpx;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t&.status-0 {\n\t\t\t\t\t\t\t\tbackground-color: #f4f4f5;\n\t\t\t\t\t\t\t\tcolor: #909399;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t&.status-1 {\n\t\t\t\t\t\t\t\tbackground-color: #ecf5ff;\n\t\t\t\t\t\t\t\tcolor: #409eff;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t&.status-2 {\n\t\t\t\t\t\t\t\tbackground-color: #f0f9eb;\n\t\t\t\t\t\t\t\tcolor: #67c23a;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.task-info {\n\t\t\t\t\t\t.info-row {\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t&:last-child {\n\t\t\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t.info-label {\n\t\t\t\t\t\t\t\twidth: 140rpx;\n\t\t\t\t\t\t\t\tcolor: #606266;\n\t\t\t\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t.info-value {\n\t\t\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\t\t\tcolor: #2c3e50;\n\t\t\t\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.task-details {\n\t\t\t\t\tpadding: 20rpx 24rpx;\n\t\t\t\t\tbackground-color: #ffffff;\n\t\t\t\t\tborder-top: 1px solid #ebeef5;\n\t\t\t\t\tanimation: slideDown 0.3s ease-out;\n\t\t\t\t\t\n\t\t\t\t\t@keyframes slideDown {\n\t\t\t\t\t\tfrom {\n\t\t\t\t\t\t\topacity: 0;\n\t\t\t\t\t\t\ttransform: translateY(-10px);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tto {\n\t\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\t\ttransform: translateY(0);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.assignment-info {\n\t\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\t\tpadding: 20rpx;\n\t\t\t\t\t\tbackground-color: #f8f9fa;\n\t\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\t\t\n\t\t\t\t\t\t.assignment-title {\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\tcolor: #606266;\n\t\t\t\t\t\t\tmargin-bottom: 16rpx;\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t&::before {\n\t\t\t\t\t\t\t\tcontent: '';\n\t\t\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\t\t\twidth: 4rpx;\n\t\t\t\t\t\t\t\theight: 24rpx;\n\t\t\t\t\t\t\t\tbackground-color: #1e3a8a;\n\t\t\t\t\t\t\t\tmargin-right: 12rpx;\n\t\t\t\t\t\t\t\tborder-radius: 2rpx;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.assignment-list {\n\t\t\t\t\t\t\t.assignment-item {\n\t\t\t\t\t\t\t\tbackground-color: #ffffff;\n\t\t\t\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\t\t\t\tpadding: 20rpx;\n\t\t\t\t\t\t\t\tmargin-bottom: 16rpx;\n\t\t\t\t\t\t\t\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\n\t\t\t\t\t\t\t\tcursor: pointer;\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\t\t\topacity: 0.8;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t&:last-child {\n\t\t\t\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t.assignment-header {\n\t\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\t\tmargin-bottom: 16rpx;\n\t\t\t\t\t\t\t\t\tpadding-bottom: 12rpx;\n\t\t\t\t\t\t\t\t\tborder-bottom: 1px dashed #ebeef5;\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t.assignee-name {\n\t\t\t\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\t\t\t\tcolor: #1e3a8a;\n\t\t\t\t\t\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t.assignment-actions {\n\t\t\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\t\t\tgap: 20rpx;\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t.assignment-status {\n\t\t\t\t\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\t\t\t\t\tpadding: 4rpx 16rpx;\n\t\t\t\t\t\t\t\t\t\t\tborder-radius: 4rpx;\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t&.status-1 {\n\t\t\t\t\t\t\t\t\t\t\t\tbackground-color: #ecf5ff;\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: #409eff;\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t&.status-2 {\n\t\t\t\t\t\t\t\t\t\t\t\tbackground-color: #f0f9eb;\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: #67c23a;\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t.delete-btn {\n\t\t\t\t\t\t\t\t\t\t\tpadding: 8rpx;\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\t\t\t\t\t\topacity: 0.7;\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t.assignment-details {\n\t\t\t\t\t\t\t\t\t.detail-row {\n\t\t\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t&:last-child {\n\t\t\t\t\t\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t.detail-label {\n\t\t\t\t\t\t\t\t\t\t\twidth: 140rpx;\n\t\t\t\t\t\t\t\t\t\t\tcolor: #909399;\n\t\t\t\t\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t.detail-value {\n\t\t\t\t\t\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\t\t\t\t\t\tcolor: #606266;\n\t\t\t\t\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t.no-assignment {\n\t\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\t\tpadding: 30rpx 0;\n\t\t\t\t\t\t\t\tcolor: #909399;\n\t\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\t\tbackground-color: #ffffff;\n\t\t\t\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\t\t\t\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.task-footer {\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tpadding-top: 20rpx;\n\t\t\t\t\tborder-top: 1px solid #ebeef5;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: flex-end;\n\t\t\t\t\t\n\t\t\t\t\t.assign-btn {\n\t\t\t\t\t\twidth: 200rpx;\n\t\t\t\t\t\theight: 60rpx;\n\t\t\t\t\t\tline-height: 60rpx;\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tcolor: #ffffff;\n\t\t\t\t\t\tbackground: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);\n\t\t\t\t\t\tborder: none;\n\t\t\t\t\t\tborder-radius: 6rpx;\n\t\t\t\t\t\t\n\t\t\t\t\t\t&::after {\n\t\t\t\t\t\t\tborder: none;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\topacity: 0.8;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.user-picker-container {\n\t\tpadding: 30rpx;\n\t\tmax-height: 70vh;\n\t\tbackground-color: #ffffff;\n\t\t\n\t\t.user-picker-header {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 30rpx;\n\t\t\tpadding-bottom: 20rpx;\n\t\t\tborder-bottom: 1px solid #ebeef5;\n\t\t\t\n\t\t\t.user-picker-title {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #2c3e50;\n\t\t\t}\n\t\t\t\n\t\t\t.user-picker-actions {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.user-list {\n\t\t\tmax-height: 60vh;\n\t\t\toverflow-y: auto;\n\t\t\tpadding: 10rpx 0;\n\t\t\t\n\t\t\t.user-item {\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding: 24rpx 20rpx;\n\t\t\t\tmargin-bottom: 16rpx;\n\t\t\t\tborder-radius: 8rpx;\n\t\t\t\tbackground-color: #f8f9fa;\n\t\t\t\ttransition: all 0.3s;\n\t\t\t\t\n\t\t\t\t&:active {\n\t\t\t\t\topacity: 0.8;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.user-item-selected {\n\t\t\t\t\tbackground-color: rgba(30, 58, 138, 0.05);\n\t\t\t\t\tborder-left: 4rpx solid #1e3a8a;\n\t\t\t\t\t\n\t\t\t\t\t.user-name {\n\t\t\t\t\t\tcolor: #1e3a8a;\n\t\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.user-name {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #2c3e50;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.quantity-input {\n\t\t\tpadding: 20rpx 24rpx;\n\t\t\tborder-top: 1px solid #ebeef5;\n\t\t\t\n\t\t\t.input-label {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #2c3e50;\n\t\t\t\tmargin-bottom: 16rpx;\n\t\t\t}\n\t\t\t\n\t\t\t.input-wrapper {\n\t\t\t\t.quantity-input-field {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\theight: 80rpx;\n\t\t\t\t\tbackground-color: #f8f9fa;\n\t\t\t\t\tborder: 1px solid #ebeef5;\n\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\tpadding: 0 24rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #2c3e50;\n\t\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.quantity-tip {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #909399;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.assignment-detail-container {\n\t\tpadding: 30rpx;\n\t\tbackground-color: #ffffff;\n\t\tmax-height: 80vh;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\t\n\t\t.detail-header {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 30rpx;\n\t\t\tpadding-bottom: 20rpx;\n\t\t\tborder-bottom: 1px solid #ebeef5;\n\t\t\t\n\t\t\t.detail-title {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #2c3e50;\n\t\t\t}\n\t\t\t\n\t\t\t.detail-actions {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.detail-content {\n\t\t\tflex: 1;\n\t\t\toverflow-y: auto;\n\t\t\t\n\t\t\t.detail-item {\n\t\t\t\tmargin-bottom: 24rpx;\n\t\t\t\t\n\t\t\t\t.detail-label {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #606266;\n\t\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.detail-value {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tcolor: #2c3e50;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\n\t\t\t\t\t&.description-text {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tline-height: 1.6;\n\t\t\t\t\t\tcolor: #606266;\n\t\t\t\t\t\tfont-weight: normal;\n\t\t\t\t\t\tbackground-color: #f8f9fa;\n\t\t\t\t\t\tpadding: 20rpx;\n\t\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\t\tborder-left: 4rpx solid #1e3a8a;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.image-list {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-wrap: wrap;\n\t\t\t\t\tmargin: 0 -10rpx;\n\t\t\t\t\t\n\t\t\t\t\t.image-item {\n\t\t\t\t\t\twidth: calc(33.33% - 20rpx);\n\t\t\t\t\t\theight: 200rpx;\n\t\t\t\t\t\tmargin: 10rpx;\n\t\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\t\tbackground-color: #f8f9fa;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.assignment-image {\n\t\t\t\t\t\twidth: calc(25% - 20rpx);\n\t\t\t\t\t\theight: 120rpx;\n\t\t\t\t\t\tmargin: 10rpx;\n\t\t\t\t\t\tborder-radius: 6rpx;\n\t\t\t\t\t\tbackground-color: #f8f9fa;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.repair-button-container {\n\t\tmargin-bottom: 30rpx;\n\t\ttext-align: right;\n\t\t\n\t\t.repair-btn {\n\t\t\tpadding: 12rpx 24rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #ffffff;\n\t\t\tbackground: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);\n\t\t\tborder: none;\n\t\t\tborder-radius: 6rpx;\n\t\t\t\n\t\t\t&:active {\n\t\t\t\topacity: 0.8;\n\t\t\t}\n\t\t}\n\t}\n\n\t.maintenance-form-container {\n\t\tpadding: 30rpx;\n\t\tbackground-color: #ffffff;\n\t\tmax-height: 80vh;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\t\n\t\t.form-header {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 30rpx;\n\t\t\tpadding-bottom: 20rpx;\n\t\t\tborder-bottom: 1px solid #ebeef5;\n\t\t\t\n\t\t\t.form-title {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #2c3e50;\n\t\t\t}\n\t\t\t\n\t\t\t.form-actions {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.form-content {\n\t\t\tflex: 1;\n\t\t\toverflow-y: auto;\n\t\t\t\n\t\t\t.form-item {\n\t\t\t\tmargin-bottom: 24rpx;\n\t\t\t\t\n\t\t\t\t.form-label {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #606266;\n\t\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.form-textarea {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tmin-height: 160rpx;\n\t\t\t\t\tbackground-color: #f8f9fa;\n\t\t\t\t\tborder: 1px solid #ebeef5;\n\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\tpadding: 24rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #2c3e50;\n\t\t\t\t\tline-height: 1.6;\n\t\t\t\t\tbox-sizing: border-box;\n\t\t\t\t\tresize: none;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.char-count {\n\t\t\t\t\ttext-align: right;\n\t\t\t\t\tmargin-top: 8rpx;\n\t\t\t\t\t\n\t\t\t\t\ttext {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: #909399;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.upload-container {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\t\n\t\t\t\t\t.upload-progress {\n\t\t\t\t\t\tmargin-top: 16rpx;\n\t\t\t\t\t\tpadding: 16rpx;\n\t\t\t\t\t\tbackground-color: rgba(30, 58, 138, 0.05);\n\t\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\t\tborder: 1px solid rgba(30, 58, 138, 0.1);\n\t\t\t\t\t\t\n\t\t\t\t\t\t.progress-bar {\n\t\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\t\theight: 8rpx;\n\t\t\t\t\t\t\tbackground-color: #ebeef5;\n\t\t\t\t\t\t\tborder-radius: 4rpx;\n\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t.progress-fill {\n\t\t\t\t\t\t\t\theight: 100%;\n\t\t\t\t\t\t\t\tbackground: linear-gradient(90deg, #1e3a8a 0%, #3b82f6 100%);\n\t\t\t\t\t\t\t\tborder-radius: 4rpx;\n\t\t\t\t\t\t\t\ttransition: width 0.3s ease;\n\t\t\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t&::after {\n\t\t\t\t\t\t\t\t\tcontent: '';\n\t\t\t\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\t\t\t\ttop: 0;\n\t\t\t\t\t\t\t\t\tleft: 0;\n\t\t\t\t\t\t\t\t\tright: 0;\n\t\t\t\t\t\t\t\t\tbottom: 0;\n\t\t\t\t\t\t\t\t\tbackground: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n\t\t\t\t\t\t\t\t\tanimation: shimmer 1.5s infinite;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.progress-text {\n\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\tcolor: #1e3a8a;\n\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.form-item:last-child {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t\t\n\t\t\t@keyframes shimmer {\n\t\t\t\t0% {\n\t\t\t\t\ttransform: translateX(-100%);\n\t\t\t\t}\n\t\t\t\t100% {\n\t\t\t\t\ttransform: translateX(100%);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDetail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderDetail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752425864466\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}