<view class="u-upload data-v-69e2a36e" style="{{$root.s0}}"><view class="u-upload__wrap data-v-69e2a36e"><block wx:if="{{previewImage}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-upload__wrap__preview data-v-69e2a36e"><block wx:if="{{item.$orig.isImage||item.$orig.type&&item.$orig.type==='image'}}"><image class="u-upload__wrap__preview__image data-v-69e2a36e" style="{{'width:'+(item.g0)+';'+('height:'+(item.g1)+';')}}" src="{{item.$orig.thumb||item.$orig.url}}" mode="{{imageMode}}" data-event-opts="{{[['tap',[['onPreviewImage',['$0'],[[['lists','',index]]]]]]]}}" bindtap="__e"></image></block><block wx:else><view class="u-upload__wrap__preview__other data-v-69e2a36e"><u-icon vue-id="{{'d270c846-1-'+index}}" color="#80CBF9" size="26" name="{{item.$orig.isVideo||item.$orig.type&&item.$orig.type==='video'?'movie':'folder'}}" class="data-v-69e2a36e" bind:__l="__l"></u-icon><text class="u-upload__wrap__preview__other__text data-v-69e2a36e">{{item.$orig.isVideo||item.$orig.type&&item.$orig.type==='video'?'视频':'文件'}}</text></view></block><block wx:if="{{item.$orig.status==='uploading'||item.$orig.status==='failed'}}"><view class="u-upload__status data-v-69e2a36e"><view class="u-upload__status__icon data-v-69e2a36e"><block wx:if="{{item.$orig.status==='failed'}}"><u-icon vue-id="{{'d270c846-2-'+index}}" name="close-circle" color="#ffffff" size="25" class="data-v-69e2a36e" bind:__l="__l"></u-icon></block><block wx:else><u-loading-icon vue-id="{{'d270c846-3-'+index}}" size="22" mode="circle" color="#ffffff" class="data-v-69e2a36e" bind:__l="__l"></u-loading-icon></block></view><block wx:if="{{item.$orig.message}}"><text class="u-upload__status__message data-v-69e2a36e">{{item.$orig.message}}</text></block></view></block><block wx:if="{{item.$orig.status!=='uploading'&&(deletable||item.$orig.deletable)}}"><view data-event-opts="{{[['tap',[['deleteItem',[index]]]]]}}" class="u-upload__deletable data-v-69e2a36e" catchtap="__e"><view class="u-upload__deletable__icon data-v-69e2a36e"><u-icon vue-id="{{'d270c846-4-'+index}}" name="close" color="#ffffff" size="10" class="data-v-69e2a36e" bind:__l="__l"></u-icon></view></view></block><block wx:if="{{item.$orig.status==='success'}}"><view class="u-upload__success data-v-69e2a36e"><view class="u-upload__success__icon data-v-69e2a36e"><u-icon vue-id="{{'d270c846-5-'+index}}" name="checkmark" color="#ffffff" size="12" class="data-v-69e2a36e" bind:__l="__l"></u-icon></view></view></block></view></block></block><block wx:if="{{isInCount}}"><block wx:if="{{$slots.default||$slots.$default}}"><view data-event-opts="{{[['tap',[['chooseFile',['$event']]]]]}}" bindtap="__e" class="data-v-69e2a36e"><slot></slot></view></block><block wx:else><view class="{{['u-upload__button','data-v-69e2a36e',disabled&&'u-upload__button--disabled']}}" style="{{'width:'+($root.g2)+';'+('height:'+($root.g3)+';')}}" hover-class="{{!disabled?'u-upload__button--hover':''}}" hover-stay-time="150" data-event-opts="{{[['tap',[['chooseFile',['$event']]]]]}}" bindtap="__e"><u-icon vue-id="d270c846-6" name="{{uploadIcon}}" size="26" color="{{uploadIconColor}}" class="data-v-69e2a36e" bind:__l="__l"></u-icon><block wx:if="{{uploadText}}"><text class="u-upload__button__text data-v-69e2a36e">{{uploadText}}</text></block></view></block></block></view></view>