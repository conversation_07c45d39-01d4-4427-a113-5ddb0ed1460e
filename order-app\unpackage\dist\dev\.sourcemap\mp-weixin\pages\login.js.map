{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/login.vue?e923", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/login.vue?fb61", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/login.vue?ffb5", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/login.vue?2434", "uni-app:///pages/login.vue", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/login.vue?4acd", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/login.vue?c13f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "codeUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "register", "globalConfig", "loginForm", "username", "password", "code", "uuid", "created", "methods", "handleUserRegister", "handlePrivacy", "handleUserAgrement", "getCode", "WeChatLogin", "params", "uni", "success", "complete", "handleLogin", "pwd<PERSON><PERSON><PERSON>", "loginSuccess"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAinB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC+DroB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;UACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACAC;MACAC;QACAC;UACAF;UACAA;QACA;QACAG;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBACA;sBACA;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;gBACA;kBACA;gBACA;kBACA;gBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACA;gBACA;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7JA;AAAA;AAAA;AAAA;AAAwqC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACA5rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=18804380&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=18804380&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"business-login-container\">\n    <view class=\"header-banner\">\n      <view class=\"banner-content\">\n        <view class=\"system-name\">工单管理系统</view>\n        <view class=\"welcome-text\">高效处理，智能管理</view>\n      </view>\n      <view class=\"logo-container\">\n        <image class=\"logo-image\" :src=\"globalConfig.appInfo.logo\" mode=\"widthFix\"></image>\n      </view>\n    </view>\n    \n    <view class=\"login-form-content\">\n      <view class=\"form-title\">账号登录</view>\n      \n      <view class=\"input-item flex align-center\">\n        <view class=\"iconfont icon-user icon\"></view>\n        <input v-model=\"loginForm.username\" class=\"input\" type=\"text\" placeholder=\"请输入账号\" maxlength=\"30\" />\n      </view>\n      \n      <view class=\"input-item flex align-center\">\n        <view class=\"iconfont icon-password icon\"></view>\n        <input v-model=\"loginForm.password\" password class=\"input\" placeholder=\"请输入密码\" maxlength=\"20\" />\n      </view>\n      \n      <view class=\"captcha-container flex\" v-if=\"captchaEnabled\">\n        <view class=\"input-item captcha-input flex align-center\">\n          <view class=\"iconfont icon-code icon\"></view>\n          <input v-model=\"loginForm.code\" type=\"number\" class=\"input\" placeholder=\"请输入验证码\" maxlength=\"4\" />\n        </view>\n        <view class=\"login-code\" @click=\"getCode\"> \n          <image :src=\"codeUrl\" class=\"login-code-img\"></image>\n        </view>\n      </view>\n      \n      <view class=\"action-btn\">\n        <button @click=\"handleLogin\" class=\"login-btn\">登录系统</button>\n      </view>\n      \n      <!-- <view class=\"divider\">\n        <text class=\"divider-text\">或</text>\n      </view>\n      \n      <view class=\"wechat-login-btn\">\n        <button type=\"default\" class=\"wechat-btn\" open-type=\"getPhoneNumber\" @getphonenumber=\"WeChatLogin\">\n          <text class=\"iconfont icon-wechat\"></text>\n          <text>手机号快捷登录</text>\n        </button>\n      </view> -->\n      \n      <view class=\"reg text-center\" v-if=\"register\">\n        <text class=\"text-grey1\">没有账号？</text>\n        <text @click=\"handleUserRegister\" class=\"text-blue\">立即注册</text>\n      </view>\n    </view>\n    \n    <view class=\"footer\">\n      <text class=\"footer-text\">© 2023 工单管理系统 版权所有</text>\n    </view>\n  </view>\n</template>\n\n<script>\n  import { getCodeImg } from '@/api/login'\n\n  export default {\n    data() {\n      return {\n        codeUrl: \"\",\n        captchaEnabled: true,\n        // 用户注册开关\n        register: true,\n        globalConfig: getApp().globalData.config,\n        loginForm: {\n          username: \"\",\n          password: \"\",\n          code: \"\",\n          uuid: ''\n        }\n      }\n    },\n    created() {\n      this.getCode()\n    },\n    methods: {\n      // 用户注册\n      handleUserRegister() {\n        this.$tab.redirectTo(`/pages/register`)\n      },\n      // 隐私协议\n      handlePrivacy() {\n        let site = this.globalConfig.appInfo.agreements[0]\n        this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)\n      },\n      // 用户协议\n      handleUserAgrement() {\n        let site = this.globalConfig.appInfo.agreements[1]\n        this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)\n      },\n      // 获取图形验证码\n      getCode() {\n        getCodeImg().then(res => {\n          this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled\n          if (this.captchaEnabled) {\n            this.codeUrl = 'data:image/gif;base64,' + res.img\n            this.loginForm.uuid = res.uuid\n          }\n        })\n      },\n\t   WeChatLogin(e){\n\t  \tthis.$modal.loading(\"登录中，请耐心等待...\")\n\t  \tlet params = {}\n\t  \tparams.code = e.detail.code\n\t  \tuni.getUserInfo({\n\t  \t\tsuccess: (res) => {\n\t  \t\t\tparams.avatarUrl = res.userInfo.avatarUrl\n\t  \t\t\tparams.nickName = res.userInfo.nickName\n\t  \t\t},\n\t  \t\tcomplete: async () => {\n\t  \t\t\tthis.$store.dispatch('WxLogin', params).then(res=>{\n\t  \t\t\t\tthis.loginSuccess()\n\t  \t\t\t})\n\t  \t\t}\n\t  \t})\n\t  },\n      // 登录方法\n      async handleLogin() {\n        if (this.loginForm.username === \"\") {\n          this.$modal.msgError(\"请输入您的账号\")\n        } else if (this.loginForm.password === \"\") {\n          this.$modal.msgError(\"请输入您的密码\")\n        } else if (this.loginForm.code === \"\" && this.captchaEnabled) {\n          this.$modal.msgError(\"请输入验证码\")\n        } else {\n          this.$modal.loading(\"登录中，请耐心等待...\")\n          this.pwdLogin()\n        }\n      },\n      // 密码登录\n      async pwdLogin() {\n        this.$store.dispatch('Login', this.loginForm).then(() => {\n          this.$modal.closeLoading()\n          this.loginSuccess()\n        }).catch(() => {\n          if (this.captchaEnabled) {\n            this.getCode()\n          }\n        })\n      },\n      // 登录成功后，处理函数\n      loginSuccess(result) {\n        // 设置用户信息\n        this.$store.dispatch('GetInfo').then(res => {\n          this.$tab.reLaunch('/pages/index')\n        })\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\">\n  page {\n    background-color: #f8f9fa;\n  }\n\n  .business-login-container {\n    width: 100%;\n    min-height: 100vh;\n    display: flex;\n    flex-direction: column;\n    \n    .header-banner {\n      width: 100%;\n      height: 280rpx;\n      background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);\n      position: relative;\n      overflow: hidden;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n      \n      &:before {\n        content: '';\n        position: absolute;\n        top: -10%;\n        right: -10%;\n        width: 300rpx;\n        height: 300rpx;\n        border-radius: 50%;\n        background: rgba(255, 255, 255, 0.1);\n      }\n      \n      &:after {\n        content: '';\n        position: absolute;\n        bottom: -15%;\n        left: -5%;\n        width: 250rpx;\n        height: 250rpx;\n        border-radius: 50%;\n        background: rgba(255, 255, 255, 0.08);\n      }\n      \n      .banner-content {\n        z-index: 2;\n        text-align: center;\n        padding: 0 40rpx;\n        \n        .system-name {\n          font-size: 48rpx;\n          font-weight: 600;\n          color: #ffffff;\n          letter-spacing: 2rpx;\n          margin-bottom: 20rpx;\n          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n        }\n        \n        .welcome-text {\n          font-size: 28rpx;\n          color: rgba(255, 255, 255, 0.9);\n          letter-spacing: 4rpx;\n        }\n      }\n      \n      .logo-container {\n        position: absolute;\n        top: 30rpx;\n        left: 30rpx;\n        z-index: 3;\n        \n        .logo-image {\n          width: 80rpx;\n          height: 80rpx;\n          border-radius: 8rpx;\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\n        }\n      }\n    }\n\n    .login-form-content {\n      width: 85%;\n      margin: 40rpx auto;\n      padding: 40rpx;\n      background-color: #ffffff;\n      border-radius: 8rpx;\n      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n      \n      .form-title {\n        font-size: 32rpx;\n        font-weight: 500;\n        color: #2c3e50;\n        text-align: left;\n        margin-bottom: 40rpx;\n        padding-bottom: 20rpx;\n        border-bottom: 1px solid #ebeef5;\n      }\n\n      .input-item {\n        margin: 30rpx auto;\n        height: 90rpx;\n        border: 1px solid #dcdfe6;\n        border-radius: 4rpx;\n        background-color: #ffffff;\n        \n        .icon {\n          font-size: 40rpx;\n          margin-left: 20rpx;\n          color: #606266;\n        }\n\n        .input {\n          width: 100%;\n          font-size: 28rpx;\n          line-height: 90rpx;\n          text-align: left;\n          padding-left: 20rpx;\n          color: #2c3e50;\n        }\n      }\n      \n      .captcha-container {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin: 30rpx auto;\n        \n        .captcha-input {\n          width: 60%;\n          margin: 0;\n        }\n        \n        .login-code {\n          width: 35%;\n          height: 90rpx;\n          border: 1px solid #dcdfe6;\n          border-radius: 4rpx;\n          overflow: hidden;\n          \n          .login-code-img {\n            width: 100%;\n            height: 100%;\n          }\n        }\n      }\n\n      .action-btn {\n        margin-top: 50rpx;\n        \n        .login-btn {\n          height: 90rpx;\n          line-height: 90rpx;\n          background-color: #1e3a8a;\n          color: #ffffff;\n          font-size: 32rpx;\n          border-radius: 4rpx;\n          font-weight: 500;\n          letter-spacing: 2rpx;\n        }\n      }\n      \n      .divider {\n        position: relative;\n        text-align: center;\n        margin: 40rpx 0;\n        height: 20rpx;\n        \n        &:before {\n          content: '';\n          position: absolute;\n          top: 50%;\n          left: 0;\n          right: 0;\n          height: 1px;\n          background-color: #ebeef5;\n          z-index: 1;\n        }\n        \n        .divider-text {\n          position: relative;\n          display: inline-block;\n          padding: 0 20rpx;\n          background-color: #ffffff;\n          color: #909399;\n          font-size: 24rpx;\n          z-index: 2;\n        }\n      }\n      \n      .wechat-login-btn {\n        margin: 30rpx 0;\n        \n        .wechat-btn {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          height: 90rpx;\n          line-height: 90rpx;\n          background-color: #ffffff;\n          color: #07c160;\n          font-size: 32rpx;\n          border: 1px solid #07c160;\n          border-radius: 4rpx;\n          \n          .icon-wechat {\n            margin-right: 10rpx;\n            font-size: 36rpx;\n          }\n        }\n      }\n      \n      .reg {\n        margin-top: 30rpx;\n        font-size: 26rpx;\n        \n        .text-grey1 {\n          color: #606266;\n        }\n        \n        .text-blue {\n          color: #1e3a8a;\n          margin-left: 10rpx;\n        }\n      }\n    }\n    \n    .footer {\n      margin-top: auto;\n      padding: 40rpx 0;\n      text-align: center;\n      \n      .footer-text {\n        font-size: 24rpx;\n        color: #909399;\n      }\n    }\n  }\n</style>", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752425864364\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}