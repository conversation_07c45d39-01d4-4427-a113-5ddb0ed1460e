<view class="add-order-container"><u-form class="vue-ref" vue-id="61e246c0-1" model="{{formData}}" error-type="{{['message']}}" data-ref="uForm" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('61e246c0-2')+','+('61e246c0-1')}}" label="产品选择" prop="productId" required="{{true}}" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['showProductPicker',['$event']]]]]}}" class="form-input-wrapper" bindtap="__e"><text class="{{['selected-text',(!selectedProduct)?'placeholder':'']}}">{{''+(selectedProduct?selectedProduct.name:'请选择产品')+''}}</text><u-icon vue-id="{{('61e246c0-3')+','+('61e246c0-2')}}" name="arrow-right" color="#8c9fba" size="32rpx" bind:__l="__l"></u-icon></view></u-form-item><u-form-item vue-id="{{('61e246c0-4')+','+('61e246c0-1')}}" label="生产任务" prop="tasks" required="{{true}}" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['showTaskPicker',['$event']]]]]}}" class="form-input-wrapper" bindtap="__e"><text class="{{['selected-text',(!selectedTasksText)?'placeholder':'']}}">{{''+(selectedTasksText||'请选择生产任务')+''}}</text><u-icon vue-id="{{('61e246c0-5')+','+('61e246c0-4')}}" name="arrow-right" color="#8c9fba" size="32rpx" bind:__l="__l"></u-icon></view></u-form-item><u-form-item vue-id="{{('61e246c0-6')+','+('61e246c0-1')}}" label="下单客户" prop="customerName" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('61e246c0-7')+','+('61e246c0-6')}}" placeholder="请输入客户名称" value="{{formData.customerName}}" data-event-opts="{{[['^input',[['__set_model',['$0','customerName','$event',[]],['formData']]]]]}}" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('61e246c0-8')+','+('61e246c0-1')}}" label="型号" prop="modelType" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('61e246c0-9')+','+('61e246c0-8')}}" placeholder="请输入型号" value="{{formData.modelType}}" data-event-opts="{{[['^input',[['__set_model',['$0','modelType','$event',[]],['formData']]]]]}}" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('61e246c0-10')+','+('61e246c0-1')}}" label="装箱单图片" prop="packingListImagePath" bind:__l="__l" vue-slots="{{['default']}}"><view class="upload-container"><u-upload vue-id="{{('61e246c0-11')+','+('61e246c0-10')}}" file-list="{{fileList}}" name="packingListImagePath" multiple="{{true}}" max-count="{{5}}" custom-btn="{{customBtn}}" disabled="{{isUploading}}" data-event-opts="{{[['^afterRead',[['afterRead']]],['^delete',[['deletePic']]]]}}" bind:afterRead="__e" bind:delete="__e" bind:__l="__l"></u-upload><block wx:if="{{isUploading}}"><view class="upload-progress"><view class="progress-bar"><view class="progress-fill" style="{{'width:'+(uploadProgress+'%')+';'}}"></view></view><text class="progress-text">{{"上传中 "+uploadProgress+"%"}}</text></view></block></view></u-form-item><u-form-item vue-id="{{('61e246c0-12')+','+('61e246c0-1')}}" label="计划数量" prop="totalPlannedQuantity" required="{{true}}" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('61e246c0-13')+','+('61e246c0-12')}}" type="number" placeholder="请输入计划数量" value="{{formData.totalPlannedQuantity}}" data-event-opts="{{[['^input',[['__set_model',['$0','totalPlannedQuantity','$event',[]],['formData']]]]]}}" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('61e246c0-14')+','+('61e246c0-1')}}" label="计划开始时间" prop="overallPlannedStartTime" bind:__l="__l" vue-slots="{{['default']}}"><uni-datetime-picker class="date-picker" vue-id="{{('61e246c0-15')+','+('61e246c0-14')}}" type="date" value="{{formData.overallPlannedStartTime}}" data-event-opts="{{[['^change',[['startDateChange']]]]}}" bind:change="__e" bind:__l="__l"></uni-datetime-picker></u-form-item><u-form-item vue-id="{{('61e246c0-16')+','+('61e246c0-1')}}" label="计划结束时间" prop="overallPlannedEndTime" bind:__l="__l" vue-slots="{{['default']}}"><uni-datetime-picker class="date-picker" vue-id="{{('61e246c0-17')+','+('61e246c0-16')}}" type="date" value="{{formData.overallPlannedEndTime}}" data-event-opts="{{[['^change',[['endDateChange']]]]}}" bind:change="__e" bind:__l="__l"></uni-datetime-picker></u-form-item><u-form-item vue-id="{{('61e246c0-18')+','+('61e246c0-1')}}" label="备注" prop="remark" bind:__l="__l" vue-slots="{{['default']}}"><u-textarea bind:input="__e" vue-id="{{('61e246c0-19')+','+('61e246c0-18')}}" placeholder="请输入备注信息" height="100" count="{{true}}" maxlength="500" value="{{formData.remark}}" data-event-opts="{{[['^input',[['__set_model',['$0','remark','$event',[]],['formData']]]]]}}" bind:__l="__l"></u-textarea></u-form-item></u-form><view class="submit-btn-wrapper"><u-button vue-id="61e246c0-20" type="primary" loading="{{submitting}}" disabled="{{isUploading||submitting}}" data-event-opts="{{[['^click',[['submitForm']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">{{''+(isUploading?'图片上传中...':submitting?editMode?'修改中...':'提交中...':editMode?'修改工单':'提交工单')+''}}</u-button></view><u-picker vue-id="61e246c0-21" show="{{showProductPickerFlag}}" columns="{{[productList]}}" keyName="name" value-key="id" data-event-opts="{{[['^confirm',[['confirmProduct']]],['^cancel',[['cancelProductPicker']]]]}}" bind:confirm="__e" bind:cancel="__e" bind:__l="__l"></u-picker><u-popup vue-id="61e246c0-22" mode="bottom" show="{{showTaskPickerFlag}}" border-radius="24" safe-area-inset-bottom="{{true}}" data-event-opts="{{[['^close',[['cancelTaskPicker']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="task-picker-container"><view class="task-picker-header"><text class="task-picker-title">选择生产任务</text><view class="task-picker-actions"><u-button style="margin-right:20rpx;" vue-id="{{('61e246c0-23')+','+('61e246c0-22')}}" plain="{{true}}" size="mini" data-event-opts="{{[['^click',[['cancelTaskPicker']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">取消</u-button><u-button vue-id="{{('61e246c0-24')+','+('61e246c0-22')}}" type="primary" size="mini" data-event-opts="{{[['^click',[['confirmTask']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">确认</u-button></view></view><view class="task-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toggleTaskSelection',['$0'],[[['taskList','',index]]]]]]]}}" class="{{['task-item',(item.g0)?'task-item-selected':'']}}" bindtap="__e"><view class="task-info"><text class="task-name">{{item.$orig.taskName}}</text><text class="task-desc">{{item.$orig.taskDesc||'无描述'}}</text></view><u-icon vue-id="{{('61e246c0-25-'+index)+','+('61e246c0-22')}}" name="{{item.g1?'checkmark-circle':''}}" color="{{item.g2?'#1e3a8a':'#8c9fba'}}" size="40" bind:__l="__l"></u-icon></view></block></view><view class="task-picker-footer"><text class="selected-count">{{"已选择 "+$root.g3+" 项任务"}}</text></view></view></u-popup></view>