{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/register.vue?a402", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/register.vue?5d9a", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/register.vue?8a0b", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/register.vue?c394", "uni-app:///pages/register.vue", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/register.vue?6b3a", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/register.vue?e805"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "codeUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "globalConfig", "registerForm", "username", "password", "confirmPassword", "code", "uuid", "created", "methods", "handleUserLogin", "getCode", "handleRegister", "isValidPhoneNumber", "register", "uni", "title", "content", "success", "url", "registerSuccess"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAonB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACyDxoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;gBACA;kBACA;gBACA;kBACA;gBACA;kBACA;gBACA;kBACA;gBACA;kBACA;gBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACAC;oBACAC;oBACAC;oBACAC;sBACA;wBACAH;0BAAAI;wBAAA;sBACA;oBACA;kBACA;gBACA;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/IA;AAAA;AAAA;AAAA;AAA2qC,CAAgB,imCAAG,EAAC,C;;;;;;;;;;;ACA/rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/register.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/register.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./register.vue?vue&type=template&id=2339929c&\"\nvar renderjs\nimport script from \"./register.vue?vue&type=script&lang=js&\"\nexport * from \"./register.vue?vue&type=script&lang=js&\"\nimport style0 from \"./register.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/register.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=template&id=2339929c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"business-register-container\">\n    <view class=\"header-banner\">\n      <view class=\"banner-content\">\n        <view class=\"system-name\">工单管理系统</view>\n        <view class=\"welcome-text\">创建您的账号</view>\n      </view>\n      <view class=\"logo-container\">\n        <image class=\"logo-image\" :src=\"globalConfig.appInfo.logo\" mode=\"widthFix\"></image>\n      </view>\n    </view>\n    \n    <view class=\"register-form-content\">\n      <view class=\"form-title\">账号注册</view>\n      \n      <view class=\"input-item flex align-center\">\n        <view class=\"iconfont icon-user icon\"></view>\n        <input v-model=\"registerForm.username\" class=\"input\" type=\"tel\" placeholder=\"请输入手机号作为账号\" maxlength=\"11\" />\n      </view>\n      \n      <view class=\"input-item flex align-center\">\n        <view class=\"iconfont icon-password icon\"></view>\n        <input v-model=\"registerForm.password\" type=\"password\" class=\"input\" placeholder=\"请输入密码\" maxlength=\"20\" />\n      </view>\n      \n      <view class=\"input-item flex align-center\">\n        <view class=\"iconfont icon-password icon\"></view>\n        <input v-model=\"registerForm.confirmPassword\" type=\"password\" class=\"input\" placeholder=\"请输入确认密码\" maxlength=\"20\" />\n      </view>\n      \n      <view class=\"captcha-container flex\" v-if=\"captchaEnabled\">\n        <view class=\"input-item captcha-input flex align-center\">\n          <view class=\"iconfont icon-code icon\"></view>\n          <input v-model=\"registerForm.code\" type=\"number\" class=\"input\" placeholder=\"请输入验证码\" maxlength=\"4\" />\n        </view>\n        <view class=\"register-code\" @click=\"getCode\"> \n          <image :src=\"codeUrl\" class=\"register-code-img\"></image>\n        </view>\n      </view>\n      \n      <view class=\"action-btn\">\n        <button @click=\"handleRegister()\" class=\"register-btn\">注册账号</button>\n      </view>\n      \n      <view class=\"login-link text-center\">\n        <text class=\"text-grey1\">已有账号？</text>\n        <text @click=\"handleUserLogin\" class=\"text-blue\">立即登录</text>\n      </view>\n    </view>\n    \n    <view class=\"footer\">\n      <text class=\"footer-text\">© 2023 工单管理系统 版权所有</text>\n    </view>\n  </view>\n</template>\n\n<script>\n  import { getCodeImg, register } from '@/api/login'\n\n  export default {\n    data() {\n      return {\n        codeUrl: \"\",\n        captchaEnabled: true,\n        globalConfig: getApp().globalData.config,\n        registerForm: {\n          username: \"\",\n          password: \"\",\n          confirmPassword: \"\",\n          code: \"\",\n          uuid: ''\n        }\n      }\n    },\n    created() {\n      this.getCode()\n    },\n    methods: {\n      // 用户登录\n      handleUserLogin() {\n        this.$tab.navigateTo(`/pages/login`)\n      },\n      // 获取图形验证码\n      getCode() {\n        getCodeImg().then(res => {\n          this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled\n          if (this.captchaEnabled) {\n            this.codeUrl = 'data:image/gif;base64,' + res.img\n            this.registerForm.uuid = res.uuid\n          }\n        })\n      },\n      // 注册方法\n      async handleRegister() {\n        if (this.registerForm.username === \"\") {\n          this.$modal.msgError(\"请输入您的账号\")\n        } else if (!this.isValidPhoneNumber(this.registerForm.username)) {\n          this.$modal.msgError(\"账号必须是有效的手机号码\")\n        } else if (this.registerForm.password === \"\") {\n          this.$modal.msgError(\"请输入您的密码\")\n        } else if (this.registerForm.confirmPassword === \"\") {\n          this.$modal.msgError(\"请再次输入您的密码\")\n        } else if (this.registerForm.password !== this.registerForm.confirmPassword) {\n          this.$modal.msgError(\"两次输入的密码不一致\")\n        } else if (this.registerForm.code === \"\" && this.captchaEnabled) {\n          this.$modal.msgError(\"请输入验证码\")\n        } else {\n          this.$modal.loading(\"注册中，请耐心等待...\")\n          this.register()\n        }\n      },\n      // 验证手机号格式\n      isValidPhoneNumber(phone) {\n        const phoneRegex = /^1[3-9]\\d{9}$/;\n        return phoneRegex.test(phone);\n      },\n      // 用户注册\n      async register() {\n        register(this.registerForm).then(res => {\n          this.$modal.closeLoading()\n          uni.showModal({\n          \ttitle: \"系统提示\",\n          \tcontent: \"恭喜你，您的账号 \" + this.registerForm.username + \" 注册成功！\",\n          \tsuccess: function (res) {\n          \t\tif (res.confirm) {\n                uni.redirectTo({ url: `/pages/login` });\n          \t\t}\n          \t}\n          })\n        }).catch(() => {\n          if (this.captchaEnabled) {\n            this.getCode()\n          }\n        })\n      },\n      // 注册成功后，处理函数\n      registerSuccess(result) {\n        // 设置用户信息\n        this.$store.dispatch('GetInfo').then(res => {\n          this.$tab.reLaunch('/pages/index')\n        })\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\">\n  page {\n    background-color: #f8f9fa;\n  }\n\n  .business-register-container {\n    width: 100%;\n    min-height: 100vh;\n    display: flex;\n    flex-direction: column;\n    \n    .header-banner {\n      width: 100%;\n      height: 280rpx;\n      background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);\n      position: relative;\n      overflow: hidden;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n      \n      &:before {\n        content: '';\n        position: absolute;\n        top: -10%;\n        right: -10%;\n        width: 300rpx;\n        height: 300rpx;\n        border-radius: 50%;\n        background: rgba(255, 255, 255, 0.1);\n      }\n      \n      &:after {\n        content: '';\n        position: absolute;\n        bottom: -15%;\n        left: -5%;\n        width: 250rpx;\n        height: 250rpx;\n        border-radius: 50%;\n        background: rgba(255, 255, 255, 0.08);\n      }\n      \n      .banner-content {\n        z-index: 2;\n        text-align: center;\n        padding: 0 40rpx;\n        \n        .system-name {\n          font-size: 48rpx;\n          font-weight: 600;\n          color: #ffffff;\n          letter-spacing: 2rpx;\n          margin-bottom: 20rpx;\n          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n        }\n        \n        .welcome-text {\n          font-size: 28rpx;\n          color: rgba(255, 255, 255, 0.9);\n          letter-spacing: 4rpx;\n        }\n      }\n      \n      .logo-container {\n        position: absolute;\n        top: 30rpx;\n        left: 30rpx;\n        z-index: 3;\n        \n        .logo-image {\n          width: 80rpx;\n          height: 80rpx;\n          border-radius: 8rpx;\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\n        }\n      }\n    }\n\n    .register-form-content {\n      width: 85%;\n      margin: 40rpx auto;\n      padding: 40rpx;\n      background-color: #ffffff;\n      border-radius: 8rpx;\n      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n      \n      .form-title {\n        font-size: 32rpx;\n        font-weight: 500;\n        color: #2c3e50;\n        text-align: left;\n        margin-bottom: 40rpx;\n        padding-bottom: 20rpx;\n        border-bottom: 1px solid #ebeef5;\n      }\n\n      .input-item {\n        margin: 30rpx auto;\n        height: 90rpx;\n        border: 1px solid #dcdfe6;\n        border-radius: 4rpx;\n        background-color: #ffffff;\n        \n        .icon {\n          font-size: 40rpx;\n          margin-left: 20rpx;\n          color: #606266;\n        }\n\n        .input {\n          width: 100%;\n          font-size: 28rpx;\n          line-height: 90rpx;\n          text-align: left;\n          padding-left: 20rpx;\n          color: #2c3e50;\n        }\n      }\n      \n      .captcha-container {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin: 30rpx auto;\n        \n        .captcha-input {\n          width: 60%;\n          margin: 0;\n        }\n        \n        .register-code {\n          width: 35%;\n          height: 90rpx;\n          border: 1px solid #dcdfe6;\n          border-radius: 4rpx;\n          overflow: hidden;\n          \n          .register-code-img {\n            width: 100%;\n            height: 100%;\n          }\n        }\n      }\n\n      .action-btn {\n        margin-top: 50rpx;\n        \n        .register-btn {\n          height: 90rpx;\n          line-height: 90rpx;\n          background-color: #1e3a8a;\n          color: #ffffff;\n          font-size: 32rpx;\n          border-radius: 4rpx;\n          font-weight: 500;\n          letter-spacing: 2rpx;\n          width: 100%;\n          border: none;\n          \n          &::after {\n            border: none;\n          }\n        }\n      }\n      \n      .login-link {\n        margin-top: 30rpx;\n        font-size: 26rpx;\n        \n        .text-grey1 {\n          color: #606266;\n        }\n        \n        .text-blue {\n          color: #1e3a8a;\n          margin-left: 10rpx;\n        }\n      }\n    }\n    \n    .footer {\n      margin-top: auto;\n      padding: 40rpx 0;\n      text-align: center;\n      \n      .footer-text {\n        font-size: 24rpx;\n        color: #909399;\n      }\n    }\n  }\n</style>\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752425864310\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}