@charset "UTF-8";
/**
 * uni-app内置的常用样式变量
 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.add-order-container {
  padding: 30rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
}
.add-order-container .u-form {
  background-color: #ffffff;
  padding: 30rpx 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.add-order-container .u-form  .u-form-item {
  margin-bottom: 30rpx;
}
.add-order-container .u-form  .u-form-item .u-form-item__body {
  flex-direction: column;
  align-items: flex-start;
}
.add-order-container .u-form  .u-form-item .u-form-item__body .u-form-item__body__left {
  width: 100%;
  margin-bottom: 16rpx;
}
.add-order-container .u-form  .u-form-item .u-form-item__body .u-form-item__body__left .u-form-item__body__left__content {
  color: #2c3e50;
  font-weight: 500;
  font-size: 28rpx;
}
.add-order-container .u-form  .u-form-item .u-form-item__body .u-form-item__body__left .u-form-item__body__left__content .u-form-item__body__left__content__label {
  font-size: 28rpx;
}
.add-order-container .u-form  .u-form-item .u-form-item__body .u-form-item__body__right {
  width: 100%;
}
.add-order-container .u-form  .u-form-item .u-form-item__body .u-form-item__body__right .u-form-item__body__right__content {
  width: 100%;
}
.add-order-container .u-form  .u-form-item.required ::v-deep .u-form-item__body__left__content:before {
  color: #ff5252;
}
.add-order-container .form-input-wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  padding: 0 24rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1px solid #ebeef5;
  transition: all 0.3s;
}
.add-order-container .form-input-wrapper:active {
  border-color: #1e3a8a;
}
.add-order-container .form-input-wrapper .selected-text {
  font-size: 28rpx;
  color: #2c3e50;
}
.add-order-container .form-input-wrapper .selected-text.placeholder {
  color: #8c9fba;
}
.add-order-container  .u-input {
  width: 100%;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1px solid #ebeef5;
  padding: 0 24rpx;
}
.add-order-container  .u-input .u-input__input {
  color: #2c3e50;
  font-size: 28rpx;
}
.add-order-container .upload-container {
  width: 100%;
  padding: 20rpx 0;
  position: relative;
}
.add-order-container .upload-container  .u-upload .u-upload__button {
  border-radius: 8rpx;
  border: 1px dashed #dcdfe6;
}
.add-order-container .upload-container  .u-upload .u-upload__button:active {
  opacity: 0.8;
}
.add-order-container .upload-container  .u-upload .u-upload__preview {
  border-radius: 8rpx;
  overflow: hidden;
}
.add-order-container .upload-container  .u-upload .u-upload__preview .u-upload__preview__image {
  border-radius: 8rpx;
}
.add-order-container .upload-container .upload-progress {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: rgba(30, 58, 138, 0.05);
  border-radius: 12rpx;
  border: 1px solid rgba(30, 58, 138, 0.1);
}
.add-order-container .upload-container .upload-progress .progress-bar {
  width: 100%;
  height: 10rpx;
  background-color: #ebeef5;
  border-radius: 5rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}
.add-order-container .upload-container .upload-progress .progress-bar .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1e3a8a 0%, #3b82f6 100%);
  border-radius: 5rpx;
  transition: width 0.3s ease;
  position: relative;
}
.add-order-container .upload-container .upload-progress .progress-bar .progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  -webkit-animation: shimmer 1.5s infinite;
          animation: shimmer 1.5s infinite;
}
.add-order-container .upload-container .upload-progress .progress-text {
  font-size: 26rpx;
  color: #1e3a8a;
  text-align: center;
  display: block;
  font-weight: 600;
  letter-spacing: 1rpx;
}
.add-order-container .date-picker {
  width: 100%;
}
.add-order-container .date-picker :deep(.uni-date) {
  border: 1px solid #ebeef5;
  border-radius: 8rpx;
  background-color: #f8f9fa;
}
.add-order-container .date-picker :deep(.uni-date) .uni-date__icon-clear {
  display: none;
}
.add-order-container .date-picker :deep(.uni-date) .uni-date__input {
  color: #2c3e50;
  font-size: 28rpx;
}
.add-order-container .submit-btn-wrapper {
  margin-top: 80rpx;
  padding: 0 40rpx;
}
.add-order-container .submit-btn-wrapper  .u-button {
  height: 90rpx;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(30, 58, 138, 0.2);
}
.add-order-container .submit-btn-wrapper  .u-button.u-button--primary {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
}
.add-order-container .submit-btn-wrapper  .u-button .u-button__text {
  font-size: 32rpx;
  letter-spacing: 4rpx;
}
.add-order-container .task-picker-container {
  padding: 30rpx;
  max-height: 70vh;
  background-color: #ffffff;
}
.add-order-container .task-picker-container .task-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #ebeef5;
}
.add-order-container .task-picker-container .task-picker-header .task-picker-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #2c3e50;
}
.add-order-container .task-picker-container .task-picker-header .task-picker-actions {
  display: flex;
  align-items: center;
}
.add-order-container .task-picker-container .task-list {
  max-height: 60vh;
  overflow-y: auto;
  padding: 10rpx 0;
}
.add-order-container .task-picker-container .task-list .task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 20rpx;
  margin-bottom: 16rpx;
  border-radius: 8rpx;
  background-color: #f8f9fa;
  transition: all 0.3s;
}
.add-order-container .task-picker-container .task-list .task-item:active {
  opacity: 0.8;
}
.add-order-container .task-picker-container .task-list .task-item.task-item-selected {
  background-color: rgba(30, 58, 138, 0.05);
  border-left: 4rpx solid #1e3a8a;
}
.add-order-container .task-picker-container .task-list .task-item.task-item-selected .task-name {
  color: #1e3a8a;
  font-weight: 600;
}
.add-order-container .task-picker-container .task-list .task-item .task-info {
  flex: 1;
  padding-right: 20rpx;
}
.add-order-container .task-picker-container .task-list .task-item .task-info .task-name {
  font-size: 28rpx;
  color: #2c3e50;
  margin-bottom: 10rpx;
  display: block;
  font-weight: 500;
}
.add-order-container .task-picker-container .task-list .task-item .task-info .task-desc {
  font-size: 24rpx;
  color: #8c9fba;
  line-height: 1.4;
}
.add-order-container .task-picker-container .task-picker-footer {
  padding-top: 20rpx;
  margin-top: 20rpx;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: flex-end;
}
.add-order-container .task-picker-container .task-picker-footer .selected-count {
  font-size: 26rpx;
  color: #606266;
}
@-webkit-keyframes shimmer {
0% {
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
}
100% {
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
}
}
@keyframes shimmer {
0% {
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
}
100% {
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
}
}
