<view class="container"><uni-list vue-id="9afdb7fc-1" bind:__l="__l" vue-slots="{{['default']}}"><uni-list-item vue-id="{{('9afdb7fc-2')+','+('9afdb7fc-1')}}" showExtraIcon="true" extraIcon="{{({type:'person-filled'})}}" title="昵称" rightText="{{user.nickName}}" bind:__l="__l"></uni-list-item><uni-list-item vue-id="{{('9afdb7fc-3')+','+('9afdb7fc-1')}}" showExtraIcon="true" extraIcon="{{({type:'phone-filled'})}}" title="手机号码" rightText="{{user.phonenumber}}" bind:__l="__l"></uni-list-item><uni-list-item vue-id="{{('9afdb7fc-4')+','+('9afdb7fc-1')}}" showExtraIcon="true" extraIcon="{{({type:'email-filled'})}}" title="邮箱" rightText="{{user.email}}" bind:__l="__l"></uni-list-item><uni-list-item vue-id="{{('9afdb7fc-5')+','+('9afdb7fc-1')}}" showExtraIcon="true" extraIcon="{{({type:'auth-filled'})}}" title="岗位" rightText="{{postGroup}}" bind:__l="__l"></uni-list-item><uni-list-item vue-id="{{('9afdb7fc-6')+','+('9afdb7fc-1')}}" showExtraIcon="true" extraIcon="{{({type:'staff-filled'})}}" title="角色" rightText="{{roleGroup}}" bind:__l="__l"></uni-list-item><uni-list-item vue-id="{{('9afdb7fc-7')+','+('9afdb7fc-1')}}" showExtraIcon="true" extraIcon="{{({type:'calendar-filled'})}}" title="创建日期" rightText="{{user.createTime}}" bind:__l="__l"></uni-list-item></uni-list></view>