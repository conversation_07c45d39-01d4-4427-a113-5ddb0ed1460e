<template>
  <view class="add-order-container">
    <u-form :model="formData" ref="uForm" :error-type="['message']">
      <!-- 产品选择 -->
      <u-form-item label="产品选择" prop="productId" required>
        <view class="form-input-wrapper" @click="showProductPicker">
          <text class="selected-text" :class="{'placeholder': !selectedProduct}">
            {{ selectedProduct ? selectedProduct.name : '请选择产品' }}
          </text>
          <u-icon name="arrow-right" color="#8c9fba" size="32rpx"></u-icon>
        </view>
      </u-form-item>
      
      <!-- 生产任务 -->
      <u-form-item label="生产任务" prop="tasks" required>
        <view class="form-input-wrapper" @click="showTaskPicker">
          <text class="selected-text" :class="{'placeholder': !selectedTasksText}">
            {{ selectedTasksText || '请选择生产任务' }}
          </text>
          <u-icon name="arrow-right" color="#8c9fba" size="32rpx"></u-icon>
        </view>
      </u-form-item>
      
      <!-- 下单客户 -->
      <u-form-item label="下单客户" prop="customerName">
        <u-input v-model="formData.customerName" placeholder="请输入客户名称" />
      </u-form-item>
      
      <!-- 型号 -->
      <u-form-item label="型号" prop="modelType">
        <u-input v-model="formData.modelType" placeholder="请输入型号" />
      </u-form-item>
      
      <!-- 装箱单图片 -->
      <u-form-item label="装箱单图片" prop="packingListImagePath">
        <view class="upload-container">
          <u-upload
            :file-list="fileList"
            @afterRead="afterRead"
            @delete="deletePic"
            name="packingListImagePath"
            multiple
            :max-count="5"
            :custom-btn="customBtn"
            :disabled="isUploading"
          ></u-upload>
          
          <!-- 上传进度指示器 -->
          <view class="upload-progress" v-if="isUploading">
            <view class="progress-bar">
              <view class="progress-fill" :style="{ width: uploadProgress + '%' }"></view>
            </view>
            <text class="progress-text">上传中 {{ uploadProgress }}%</text>
          </view>
        </view>
      </u-form-item>
      
      <!-- 订单产品编号版本 -->
      <!-- <u-form-item label="订单产品编号版本" prop="orderProductNumberVersion">
        <u-textarea
          v-model="formData.orderProductNumberVersion"
          placeholder="请输入订单产品编号版本"
          height="100"
          count
          maxlength="500"
        />
      </u-form-item> -->
      
      <!-- 计划数量 -->
      <u-form-item label="计划数量" prop="totalPlannedQuantity" required>
        <u-input
          v-model="formData.totalPlannedQuantity"
          type="number"
          placeholder="请输入计划数量"
        />
      </u-form-item>
      
      <!-- 计划开始时间 -->
      <u-form-item label="计划开始时间" prop="overallPlannedStartTime">
        <uni-datetime-picker
          type="date"
          :value="formData.overallPlannedStartTime"
          @change="startDateChange"
          class="date-picker"
        />
      </u-form-item>
      
      <!-- 计划结束时间 -->
      <u-form-item label="计划结束时间" prop="overallPlannedEndTime">
        <uni-datetime-picker
          type="date"
          :value="formData.overallPlannedEndTime"
          @change="endDateChange"
          class="date-picker"
        />
      </u-form-item>
      
      <!-- 备注 -->
      <u-form-item label="备注" prop="remark">
        <u-textarea
          v-model="formData.remark"
          placeholder="请输入备注信息"
          height="100"
          count
          maxlength="500"
        />
      </u-form-item>
    </u-form>
    
    <!-- 提交按钮 -->
    <view class="submit-btn-wrapper">
      <u-button 
        type="primary" 
        @click="submitForm" 
        :loading="submitting"
        :disabled="isUploading || submitting"
      >
        {{ isUploading ? '图片上传中...' : (submitting ? (editMode ? '修改中...' : '提交中...') : (editMode ? '修改工单' : '提交工单')) }}
      </u-button>
    </view>
    
    <!-- 产品选择弹窗 -->
    <u-picker
      :show="showProductPickerFlag"
      :columns="[productList]"
      @confirm="confirmProduct"
      @cancel="cancelProductPicker"
      keyName="name"
      value-key="id"
    ></u-picker>
    
    <!-- 任务选择弹窗 -->
    <u-popup
      mode="bottom"
      :show="showTaskPickerFlag"
      @close="cancelTaskPicker"
      border-radius="24"
      safe-area-inset-bottom
    >
      <view class="task-picker-container">
        <view class="task-picker-header">
          <text class="task-picker-title">选择生产任务</text>
          <view class="task-picker-actions">
            <u-button plain size="mini" @click="cancelTaskPicker" style="margin-right: 20rpx;">取消</u-button>
            <u-button type="primary" size="mini" @click="confirmTask">确认</u-button>
          </view>
        </view>
        <view class="task-list">
          <view
            class="task-item"
            v-for="(item, index) in taskList"
            :key="index"
            @click="toggleTaskSelection(item)"
            :class="{'task-item-selected': tempSelectedTasks.some(t => t.taskId === item.taskId)}"
          >
            <view class="task-info">
              <text class="task-name">{{ item.taskName }}</text>
              <text class="task-desc">{{ item.taskDesc || '无描述' }}</text>
            </view>
            <u-icon
              :name="tempSelectedTasks.some(t => t.taskId === item.taskId) ? 'checkmark-circle' : ''"
              :color="tempSelectedTasks.some(t => t.taskId === item.taskId) ? '#1e3a8a' : '#8c9fba'"
              size="40"
            ></u-icon>
          </view>
        </view>
        <view class="task-picker-footer">
          <text class="selected-count">已选择 {{ tempSelectedTasks.length }} 项任务</text>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { addOrders,updateOrders,getOrders, } from "@/api/work/orders.js"
import { listGoods } from "@/api/work/goods.js"
import { baseUrl } from "../../config"
import { listTasks } from "@/api/work/tasks.js"

export default {
  data() {
    return {
      baseUrl,
      editMode: false, // 编辑模式标识
      workOrderId: null, // 工单ID
      formData: {
        productId: '',
        customerName: '',
        modelType: '',
        packingListImagePath: '',
        // orderProductNumberVersion: '',
        totalPlannedQuantity: '',
        overallPlannedStartTime: '',
        overallPlannedEndTime: '',
        remark: '',
        status: '0' // 添加状态字段
      },
      rules: {
        productId: [
          { required: true, message: '请选择产品', trigger: 'change' }
        ],
        tasks: [
          { required: true, message: '请选择生产任务', trigger: 'change' }
        ],
        totalPlannedQuantity: [
          { required: true, message: '请输入计划数量', trigger: 'blur' }
        ]
      },
      fileList: [],
      productList: [],
      taskList: [],
      selectedTasks: [],
      tempSelectedTasks: [],
      selectedProduct: null,
      showProductPickerFlag: false,
      showTaskPickerFlag: false,
      submitting: false,
      isUploading: false,
      uploadProgress: 0
    }
  },
  computed: {
    selectedTasksText() {
      if (this.selectedTasks.length === 0) return '';
      return this.selectedTasks.map(task => task.taskName).join('，');
    },
    customBtn() {
      return {
        slot: 'addBtn',
        icon: 'camera-fill',
        backgroundColor: '#f2f3f5',
        color: '#8c9fba',
        size: 140
      };
    }
  },
  onLoad(options) {
    this.$refs.uForm.setRules(this.rules);
    
    // 先获取产品和任务列表，再检查是否为编辑模式
    this.getProductList().then(() => {
      this.getTaskList().then(() => {
        // 检查是否为编辑模式
        if (options.id) {
          this.editMode = true;
          this.workOrderId = options.id;
          this.getOrderDetail(options.id);
        }
      });
    });
  },
  methods: {
    // 获取工单详情（编辑模式使用）
    async getOrderDetail(workOrderId) {
      try {
        const res = await getOrders(workOrderId);
        if (res && res.data) {
          const orderData = res.data;
          
          // 填充表单数据
          this.formData = {
            productId: orderData.productId || '',
            customerName: orderData.customerName || '',
            modelType: orderData.modelType || '',
            packingListImagePath: orderData.packingListImagePath || '',
            totalPlannedQuantity: orderData.totalPlannedQuantity || '',
            overallPlannedStartTime: orderData.overallPlannedStartTime || '',
            overallPlannedEndTime: orderData.overallPlannedEndTime || '',
            remark: orderData.remark || '',
            status: orderData.status || '0'
          };
          
          // 设置选中的产品
          if (orderData.productId) {
            const product = this.productList.find(p => p.id == orderData.productId);
            if (product) {
              this.selectedProduct = product;
            }
          }
          
          // 设置图片列表
          if (orderData.packingListImagePath) {
            this.fileList = orderData.packingListImagePath.split(',').filter(path => path).map(path => ({
              url: this.baseUrl + path,
              status: 'success',
              message: '上传成功',
              fileName: path
            }));
          }
          
          // 获取并设置选中的任务（需要从工单项目中获取）
          if (orderData.workOrderItems && orderData.workOrderItems.length > 0) {
            this.selectedTasks = orderData.workOrderItems.map(item => {
              const task = this.taskList.find(t => t.taskId == item.productionTaskId);
              return task || { taskId: item.productionTaskId, taskName: '未知任务' };
            }).filter(task => task);
            this.tempSelectedTasks = [...this.selectedTasks];
          }
        }
      } catch (error) {
        console.error('获取工单详情失败', error);
        uni.showToast({
          title: '获取工单详情失败',
          icon: 'none'
        });
      }
    },
    
    // 获取产品列表
    async getProductList() {
      try {
        const res = await listGoods();
        if (res && res.rows) {
          this.productList = res.rows
        }
      } catch (error) {
        console.error('获取产品列表失败', error);
        uni.showToast({
          title: '获取产品列表失败',
          icon: 'none'
        });
      }
    },
    
    // 获取任务列表
    async getTaskList() {
      try {
        const res = await listTasks();
        if (res && res.rows) {
          this.taskList = res.rows.map(item => ({
            ...item,
            selected: false
          }));
        }
      } catch (error) {
        console.error('获取任务列表失败', error);
        uni.showToast({
          title: '获取任务列表失败',
          icon: 'none'
        });
      }
    },
    
    // 显示产品选择器
    showProductPicker() {
      this.showProductPickerFlag = true;
    },
    
    // 确认产品选择
    confirmProduct(e) {
      console.log(e)
      const selected = e.value[0];
      this.selectedProduct = selected;
      this.formData.productId = this.selectedProduct.id;
      console.log(this.formData.productId)
      this.showProductPickerFlag = false;
    },
    
    // 取消产品选择
    cancelProductPicker() {
      this.showProductPickerFlag = false;
    },
    
    // 显示任务选择器
    showTaskPicker() {
      this.showTaskPickerFlag = true;
    },
    
    // 切换任务选择状态
    toggleTaskSelection(task) {
      const index = this.tempSelectedTasks.findIndex(t => t.taskId === task.taskId);
      if (index > -1) {
        this.tempSelectedTasks.splice(index, 1);
      } else {
        this.tempSelectedTasks.push(task);
      }
    },
    
    // 确认任务选择
    confirmTask() {
      this.selectedTasks = [...this.tempSelectedTasks];
      this.showTaskPickerFlag = false;
    },
    
    // 取消任务选择
    cancelTaskPicker() {
      this.tempSelectedTasks = [...this.selectedTasks];
      this.showTaskPickerFlag = false;
    },
    
    // 开始日期变化
    startDateChange(value) {
      this.formData.overallPlannedStartTime = value;
    },
    
    // 结束日期变化
    endDateChange(value) {
      this.formData.overallPlannedEndTime = value;
    },
    
    // 上传图片后的回调
    async afterRead(event) {
      const { file } = event;
      
      // 设置上传状态
      this.isUploading = true;
      this.uploadProgress = 0;
      
      // 显示上传中
      uni.showLoading({
        title: '图片上传中...',
        mask: true
      });
      
      try {
        // 模拟上传进度
        const progressTimer = setInterval(() => {
          if (this.uploadProgress < 80) {
            this.uploadProgress += 10;
          }
        }, 150);
        
        const uploadPromises = file.map(item => {
          return new Promise((resolve, reject) => {
            uni.uploadFile({
              url: this.baseUrl + '/common/upload',
              filePath: item.url,
              name: 'file',
              success: (res) => {
                const data = JSON.parse(res.data);
                if (data.code === 200) {
                  resolve(data.fileName);
                } else {
                  reject(new Error(data.msg || '上传失败'));
                }
              },
              fail: (err) => {
                reject(err);
              }
            });
          });
        });
        
        const uploadedFiles = await Promise.all(uploadPromises);
        
        // 清除进度定时器，设置完成状态
        clearInterval(progressTimer);
        this.uploadProgress = 100;
        
        // 更新文件列表
        uploadedFiles.forEach(fileName => {
          this.fileList.push({
            url: this.baseUrl + fileName,
            status: 'success',
            message: '上传成功',
            fileName: fileName
          });
        });
        
        // 更新表单数据
        this.formData.packingListImagePath = this.fileList
          .map(file => file.fileName)
          .join(',');
        
        // 延迟一下再隐藏加载，让用户看到100%的进度
        setTimeout(() => {
          uni.hideLoading();
          uni.showToast({
            title: `成功上传${uploadedFiles.length}张图片`,
            icon: 'success',
            duration: 1500
          });
          
          // 重置上传状态
          this.isUploading = false;
          this.uploadProgress = 0;
        }, 300);
        
      } catch (error) {
        // 重置状态
        this.isUploading = false;
        this.uploadProgress = 0;
        
        uni.hideLoading();
        uni.showToast({
          title: '上传失败: ' + error.message,
          icon: 'error',
          duration: 2000
        });
      }
    },
    
    // 删除图片
    deletePic(event) {
      const index = event.index;
      this.fileList.splice(index, 1);
      
      // 更新表单数据
      this.formData.packingListImagePath = this.fileList
        .map(file => file.fileName)
        .join(',');
      
      // 重置上传状态（如果没有正在上传的话）
      if (!this.isUploading) {
        this.uploadProgress = 0;
      }
    },
    
    // 提交表单
    submitForm() {
      this.submitData();
    },
    
    // 提交数据
    async submitData() {
      if (this.formData.productId == null) {
        uni.showToast({
          title: '请选择产品',
          icon: 'none'
        });
        return;
      }
      console.log(this.selectedTasks)
      if (!this.selectedTasks || this.selectedTasks.length === 0) {
        uni.showToast({
          title: '请选择生产任务',
          icon: 'none'
        });
        return;
      }
      
      if (!this.formData.totalPlannedQuantity) {
        uni.showToast({
          title: '请输入计划数量',
          icon: 'none'
        });
        return;
      }
      
      this.submitting = true;
      
      try {
        // 构建任务数据数组
        const workOrderItems = this.selectedTasks.map(task => ({
          productionTaskId: task.taskId,
          plannedQuantity: this.formData.totalPlannedQuantity
        }));
        
        // 构建提交数据
        const orderData = {
          productId: this.formData.productId,
          customerName: this.formData.customerName,
          modelType: this.formData.modelType,
          packingListImagePath: this.formData.packingListImagePath,
          // orderProductNumberVersion: this.formData.orderProductNumberVersion,
          totalPlannedQuantity: this.formData.totalPlannedQuantity,
          overallPlannedStartTime: this.formData.overallPlannedStartTime,
          overallPlannedEndTime: this.formData.overallPlannedEndTime,
          remark: this.formData.remark,
          status: this.formData.status,
          workOrderItems: workOrderItems
        };
        
        let orderRes;
        if (this.editMode) {
          // 编辑模式：调用修改接口
          orderData.workOrderId = this.workOrderId;
          orderRes = await updateOrders(orderData);
        } else {
          // 新增模式：调用新增接口
          orderRes = await addOrders(orderData);
        }
        
        if (orderRes && orderRes.data) {
          uni.showToast({
            title: this.editMode ? '工单修改成功' : '工单创建成功',
            icon: 'success'
          });
          
          // 返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          throw new Error(this.editMode ? '工单修改失败' : '工单创建失败');
        }
      } catch (error) {
        console.error('提交数据失败', error);
        uni.showToast({
          title: '提交失败: ' + (error.message || '未知错误'),
          icon: 'none'
        });
      } finally {
        this.submitting = false;
      }
    }
  }
}
</script>

<style lang="scss">
.add-order-container {
  padding: 30rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
  
  .u-form {
    background-color: #ffffff;
    padding: 30rpx 20rpx;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    
    ::v-deep .u-form-item {
      margin-bottom: 30rpx;
      
      .u-form-item__body {
        flex-direction: column;
        align-items: flex-start;
        
        .u-form-item__body__left {
          width: 100%;
          margin-bottom: 16rpx;
          
          .u-form-item__body__left__content {
            color: #2c3e50;
            font-weight: 500;
            font-size: 28rpx;
            
            .u-form-item__body__left__content__label {
              font-size: 28rpx;
            }
          }
        }
        
        .u-form-item__body__right {
          width: 100%;
          
          .u-form-item__body__right__content {
            width: 100%;
          }
        }
      }
      
      &.required {
        ::v-deep .u-form-item__body__left__content:before {
          color: #ff5252;
        }
      }
    }
  }
  
  .form-input-wrapper {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80rpx;
    padding: 0 24rpx;
    background-color: #f8f9fa;
    border-radius: 8rpx;
    border: 1px solid #ebeef5;
    transition: all 0.3s;
    
    &:active {
      border-color: #1e3a8a;
    }
    
    .selected-text {
      font-size: 28rpx;
      color: #2c3e50;
      
      &.placeholder {
        color: #8c9fba;
      }
    }
  }
  
  ::v-deep .u-input {
    width: 100%;
    background-color: #f8f9fa;
    border-radius: 8rpx;
    border: 1px solid #ebeef5;
    padding: 0 24rpx;
    
    .u-input__input {
      color: #2c3e50;
      font-size: 28rpx;
    }
  }
  
  .upload-container {
    width: 100%;
    padding: 20rpx 0;
    position: relative;
    
    ::v-deep .u-upload {
      .u-upload__button {
        border-radius: 8rpx;
        border: 1px dashed #dcdfe6;
        
        &:active {
          opacity: 0.8;
        }
      }
      
      .u-upload__preview {
        border-radius: 8rpx;
        overflow: hidden;
        
        .u-upload__preview__image {
          border-radius: 8rpx;
        }
      }
    }
    
    .upload-progress {
      margin-top: 20rpx;
      padding: 20rpx;
      background-color: rgba(30, 58, 138, 0.05);
      border-radius: 12rpx;
      border: 1px solid rgba(30, 58, 138, 0.1);
      
      .progress-bar {
        width: 100%;
        height: 10rpx;
        background-color: #ebeef5;
        border-radius: 5rpx;
        overflow: hidden;
        margin-bottom: 16rpx;
        
        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #1e3a8a 0%, #3b82f6 100%);
          border-radius: 5rpx;
          transition: width 0.3s ease;
          position: relative;
          
          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shimmer 1.5s infinite;
          }
        }
      }
      
      .progress-text {
        font-size: 26rpx;
        color: #1e3a8a;
        text-align: center;
        display: block;
        font-weight: 600;
        letter-spacing: 1rpx;
      }
    }
  }
  
  .date-picker {
    width: 100%;
    
    :deep(.uni-date) {
      border: 1px solid #ebeef5;
      border-radius: 8rpx;
      background-color: #f8f9fa;
      
      .uni-date__icon-clear {
        display: none;
      }
      
      .uni-date__input {
        color: #2c3e50;
        font-size: 28rpx;
      }
    }
  }
  
  .submit-btn-wrapper {
    margin-top: 80rpx;
    padding: 0 40rpx;
    
    ::v-deep .u-button {
      height: 90rpx;
      border-radius: 8rpx;
      box-shadow: 0 4rpx 12rpx rgba(30, 58, 138, 0.2);
      
      &.u-button--primary {
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
      }
      
      .u-button__text {
        font-size: 32rpx;
        letter-spacing: 4rpx;
      }
    }
  }
  
  .task-picker-container {
    padding: 30rpx;
    max-height: 70vh;
    background-color: #ffffff;
    
    .task-picker-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;
      padding-bottom: 20rpx;
      border-bottom: 1px solid #ebeef5;
      
      .task-picker-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #2c3e50;
      }
      
      .task-picker-actions {
        display: flex;
        align-items: center;
      }
    }
    
    .task-list {
      max-height: 60vh;
      overflow-y: auto;
      padding: 10rpx 0;
      
      .task-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 20rpx;
        margin-bottom: 16rpx;
        border-radius: 8rpx;
        background-color: #f8f9fa;
        transition: all 0.3s;
        
        &:active {
          opacity: 0.8;
        }
        
        &.task-item-selected {
          background-color: rgba(30, 58, 138, 0.05);
          border-left: 4rpx solid #1e3a8a;
          
          .task-name {
            color: #1e3a8a;
            font-weight: 600;
          }
        }
        
        .task-info {
          flex: 1;
          padding-right: 20rpx;
          
          .task-name {
            font-size: 28rpx;
            color: #2c3e50;
            margin-bottom: 10rpx;
            display: block;
            font-weight: 500;
          }
          
          .task-desc {
            font-size: 24rpx;
            color: #8c9fba;
            line-height: 1.4;
          }
        }
      }
    }
    
    .task-picker-footer {
      padding-top: 20rpx;
      margin-top: 20rpx;
      border-top: 1px solid #ebeef5;
      display: flex;
      justify-content: flex-end;
      
      .selected-count {
        font-size: 26rpx;
        color: #606266;
      }
    }
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
</style>
