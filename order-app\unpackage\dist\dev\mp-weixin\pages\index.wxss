@charset "UTF-8";
/**
 * uni-app内置的常用样式变量
 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.order-list-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}
/* 无权限页面样式 */
.no-permission-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f5f7fa;
}
.no-permission-container .no-permission-content {
  text-align: center;
  padding: 60rpx 40rpx;
}
.no-permission-container .no-permission-content .permission-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40rpx;
}
.no-permission-container .no-permission-content .permission-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20rpx;
}
.no-permission-container .no-permission-content .permission-description {
  font-size: 28rpx;
  color: #606266;
  line-height: 1.6;
}
/* 搜索区域样式 */
.search-section {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 10;
}
.search-section .search-box {
  display: flex;
  align-items: center;
  height: 80rpx;
  background-color: #f5f7fa;
  border-radius: 40rpx;
  padding: 0 30rpx;
}
.search-section .search-box .u-icon {
  margin-right: 20rpx;
}
.search-section .search-box input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
}
.search-section .search-box .search-btn {
  padding: 0 20rpx;
  height: 60rpx;
  line-height: 60rpx;
  background-color: #1e3a8a;
  color: #ffffff;
  font-size: 26rpx;
  border-radius: 30rpx;
}
/* 列表区域样式 */
.order-list-scroll {
  flex: 1;
  width: 100%;
  height: 75vh;
}
.order-list {
  padding: 20rpx;
}
/* 工单卡片样式 */
.order-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.order-card .order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #f0f2f5;
}
.order-card .order-header .order-no {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.order-card .order-header .order-status {
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}
.order-card .order-header .order-status.status-pending {
  background-color: #e6f7ff;
  color: #1890ff;
}
.order-card .order-header .order-status.status-processing {
  background-color: #fff7e6;
  color: #fa8c16;
}
.order-card .order-header .order-status.status-completed {
  background-color: #f6ffed;
  color: #52c41a;
}
.order-card .order-content {
  padding: 20rpx 30rpx;
  /* 操作按钮区域 */
}
.order-card .order-content .info-row {
  display: flex;
  margin-bottom: 16rpx;
}
.order-card .order-content .info-row:last-child {
  margin-bottom: 0;
}
.order-card .order-content .info-row .info-label {
  width: 160rpx;
  font-size: 26rpx;
  color: #666;
}
.order-card .order-content .info-row .info-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}
.order-card .order-content .info-row .remark-text {
  color: #8c9fba;
}
.order-card .order-content .action-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 20rpx;
  margin-top: 24rpx;
  padding-top: 20rpx;
  border-top: 1px solid #f0f2f5;
}
.order-card .order-content .action-buttons .edit-btn, .order-card .order-content .action-buttons .delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 24rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 26rpx;
  font-weight: 500;
  min-width: 100rpx;
  height: 60rpx;
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.order-card .order-content .action-buttons .edit-btn::before, .order-card .order-content .action-buttons .delete-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}
.order-card .order-content .action-buttons .edit-btn:active, .order-card .order-content .action-buttons .delete-btn:active {
  -webkit-transform: translateY(1rpx);
          transform: translateY(1rpx);
}
.order-card .order-content .action-buttons .edit-btn:active::before, .order-card .order-content .action-buttons .delete-btn:active::before {
  left: 100%;
}
.order-card .order-content .action-buttons .edit-btn {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
}
.order-card .order-content .action-buttons .edit-btn:active {
  box-shadow: 0 1rpx 4rpx rgba(30, 58, 138, 0.3);
}
.order-card .order-content .action-buttons .delete-btn {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
}
.order-card .order-content .action-buttons .delete-btn:active {
  box-shadow: 0 1rpx 4rpx rgba(220, 38, 38, 0.3);
}
/* 空数据样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-container .empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-container .empty-text {
  font-size: 28rpx;
  color: #8c9fba;
}
/* 加载更多和无更多数据样式 */
.loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #8c9fba;
}
/* 悬浮按钮样式 */
.fab-button {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #1e3a8a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(30, 58, 138, 0.4);
  z-index: 100;
  transition: all 0.3s;
  /* 使用u-icon组件，不需要额外的图标样式 */
}
.fab-button:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
/* 统计信息区域样式 */
.stats-section {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.stats-section .stats-item {
  display: flex;
  align-items: center;
}
.stats-section .stats-item .stats-label {
  font-size: 26rpx;
  color: #606266;
  margin-right: 8rpx;
}
.stats-section .stats-item .stats-value {
  font-size: 28rpx;
  color: #1e3a8a;
  font-weight: 600;
}
