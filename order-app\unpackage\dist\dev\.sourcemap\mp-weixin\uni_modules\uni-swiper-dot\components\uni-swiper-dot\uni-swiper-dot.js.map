{"version": 3, "sources": ["webpack:///D:/项目/工单小程序/order-app/uni_modules/uni-swiper-dot/components/uni-swiper-dot/uni-swiper-dot.vue?8dec", "webpack:///D:/项目/工单小程序/order-app/uni_modules/uni-swiper-dot/components/uni-swiper-dot/uni-swiper-dot.vue?db49", "webpack:///D:/项目/工单小程序/order-app/uni_modules/uni-swiper-dot/components/uni-swiper-dot/uni-swiper-dot.vue?43fa", "webpack:///D:/项目/工单小程序/order-app/uni_modules/uni-swiper-dot/components/uni-swiper-dot/uni-swiper-dot.vue?b62d", "uni-app:///uni_modules/uni-swiper-dot/components/uni-swiper-dot/uni-swiper-dot.vue", "webpack:///D:/项目/工单小程序/order-app/uni_modules/uni-swiper-dot/components/uni-swiper-dot/uni-swiper-dot.vue?3b17", "webpack:///D:/项目/工单小程序/order-app/uni_modules/uni-swiper-dot/components/uni-swiper-dot/uni-swiper-dot.vue?8e68"], "names": ["name", "emits", "props", "info", "type", "default", "current", "dotsStyles", "mode", "field", "data", "dots", "width", "height", "bottom", "color", "backgroundColor", "border", "selectedBackgroundColor", "selectedB<PERSON>er", "watch", "created", "methods", "clickItem"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACc;;;AAG3E;AAC4K;AAC5K,gBAAgB,6KAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAwpB,CAAgB,wnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+B5qB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,gBAgBA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;EACA;EACAK;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACAb;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;EAEA;EACAa;IACA;MACA;MACA;IACA;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACvHA;AAAA;AAAA;AAAA;AAAuuC,CAAgB,umCAAG,EAAC,C;;;;;;;;;;;ACA3vC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-swiper-dot/components/uni-swiper-dot/uni-swiper-dot.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-swiper-dot.vue?vue&type=template&id=77b53eff&\"\nvar renderjs\nimport script from \"./uni-swiper-dot.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-swiper-dot.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-swiper-dot.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-swiper-dot/components/uni-swiper-dot/uni-swiper-dot.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-swiper-dot.vue?vue&type=template&id=77b53eff&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.mode === \"nav\" ? _vm.info.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-swiper-dot.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-swiper-dot.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-swiper__warp\">\r\n\t\t<slot />\r\n\t\t<view v-if=\"mode === 'default'\" :style=\"{'bottom':dots.bottom + 'px'}\" class=\"uni-swiper__dots-box\" key='default'>\r\n\t\t\t<view v-for=\"(item,index) in info\" @click=\"clickItem(index)\" :style=\"{\r\n        'width': (index === current? dots.width*2:dots.width ) + 'px','height':dots.width/2 +'px' ,'background-color':index !== current?dots.backgroundColor:dots.selectedBackgroundColor,'border-radius':'0px'}\"\r\n\t\t\t :key=\"index\" class=\"uni-swiper__dots-item uni-swiper__dots-bar\" />\r\n\t\t</view>\r\n\t\t<view v-if=\"mode === 'dot'\" :style=\"{'bottom':dots.bottom + 'px'}\" class=\"uni-swiper__dots-box\" key='dot'>\r\n\t\t\t<view v-for=\"(item,index) in info\" @click=\"clickItem(index)\" :style=\"{\r\n        'width': dots.width + 'px','height':dots.height +'px' ,'background-color':index !== current?dots.backgroundColor:dots.selectedBackgroundColor,'border':index !==current ? dots.border:dots.selectedBorder}\"\r\n\t\t\t :key=\"index\" class=\"uni-swiper__dots-item\" />\r\n\t\t</view>\r\n\t\t<view v-if=\"mode === 'round'\" :style=\"{'bottom':dots.bottom + 'px'}\" class=\"uni-swiper__dots-box\" key='round'>\r\n\t\t\t<view v-for=\"(item,index) in info\" @click=\"clickItem(index)\" :class=\"[index === current&&'uni-swiper__dots-long']\" :style=\"{\r\n\t\t    'width':(index === current? dots.width*3:dots.width ) + 'px','height':dots.height +'px' ,'background-color':index !== current?dots.backgroundColor:dots.selectedBackgroundColor,'border':index !==current ? dots.border:dots.selectedBorder}\"\r\n\t\t\t :key=\"index\" class=\"uni-swiper__dots-item \" />\r\n\t\t</view>\r\n\t\t<view v-if=\"mode === 'nav'\" key='nav' :style=\"{'background-color':dotsStyles.backgroundColor,'bottom':'0'}\" class=\"uni-swiper__dots-box uni-swiper__dots-nav\">\r\n\t\t\t<text :style=\"{'color':dotsStyles.color}\" class=\"uni-swiper__dots-nav-item\">{{ (current+1)+\"/\"+info.length +' ' +info[current][field] }}</text>\r\n\t\t</view>\r\n\t\t<view v-if=\"mode === 'indexes'\" key='indexes' :style=\"{'bottom':dots.bottom + 'px'}\" class=\"uni-swiper__dots-box\">\r\n\t\t\t<view v-for=\"(item,index) in info\" @click=\"clickItem(index)\" :style=\"{\r\n        'width':dots.width + 'px','height':dots.height +'px' ,'color':index === current?dots.selectedColor:dots.color,'background-color':index !== current?dots.backgroundColor:dots.selectedBackgroundColor,'border':index !==current ? dots.border:dots.selectedBorder}\"\r\n\t\t\t :key=\"index\" class=\"uni-swiper__dots-item uni-swiper__dots-indexes\"><text class=\"uni-swiper__dots-indexes-text\">{{ index+1 }}</text></view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\n\n\t/**\n\t * SwiperDod 轮播图指示点\n\t * @description 自定义轮播图指示点\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=284\n\t * @property {Number} current 当前指示点索引，必须是通过 `swiper` 的 `change` 事件获取到的 `e.detail.current`\n\t * @property {String} mode = [default|round|nav|indexes] 指示点的类型\n\t * \t@value defualt 默认指示点\n\t * \t@value round 圆形指示点\n\t * \t@value nav 条形指示点\n\t * \t@value indexes 索引指示点\n\t * @property {String} field mode 为 nav 时，显示的内容字段（mode = nav 时必填）\n\t * @property {String} info 轮播图的数据，通过数组长度决定指示点个数\n\t * @property {Object} dotsStyles 指示点样式\n\t * @event {Function} clickItem 组件触发点击事件时触发，e={currentIndex}\n\t */\n\r\n\texport default {\r\n\t\tname: 'UniSwiperDot',\r\n\t\temits:['clickItem'],\r\n\t\tprops: {\r\n\t\t\tinfo: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcurrent: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tdotsStyles: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 类型 ：default(默认) indexes long nav\r\n\t\t\tmode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'default'\r\n\t\t\t},\r\n\t\t\t// 只在 nav 模式下生效，变量名称\r\n\t\t\tfield: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tdots: {\r\n\t\t\t\t\twidth: 6,\r\n\t\t\t\t\theight: 6,\r\n\t\t\t\t\tbottom: 10,\r\n\t\t\t\t\tcolor: '#fff',\r\n\t\t\t\t\tbackgroundColor: 'rgba(0, 0, 0, .3)',\r\n\t\t\t\t\tborder: '1px rgba(0, 0, 0, .3) solid',\r\n\t\t\t\t\tselectedBackgroundColor: '#333',\r\n\t\t\t\t\tselectedBorder: '1px rgba(0, 0, 0, .9) solid'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tdotsStyles(newVal) {\r\n\t\t\t\tthis.dots = Object.assign(this.dots, this.dotsStyles)\r\n\t\t\t},\r\n\t\t\tmode(newVal) {\r\n\t\t\t\tif (newVal === 'indexes') {\r\n\t\t\t\t\tthis.dots.width = 14\r\n\t\t\t\t\tthis.dots.height = 14\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.dots.width = 6\r\n\t\t\t\t\tthis.dots.height = 6\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tif (this.mode === 'indexes') {\r\n\t\t\t\tthis.dots.width = 12\r\n\t\t\t\tthis.dots.height = 12\r\n\t\t\t}\r\n\t\t\tthis.dots = Object.assign(this.dots, this.dotsStyles)\r\n\t\t},\n\t\tmethods: {\n\t\t\tclickItem(index) {\n\t\t\t\tthis.$emit('clickItem', index)\n\t\t\t}\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.uni-swiper__warp {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex: 1;\r\n\t\tflex-direction: column;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.uni-swiper__dots-box {\r\n\t\tposition: absolute;\r\n\t\tbottom: 10px;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex: 1;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.uni-swiper__dots-item {\r\n\t\twidth: 8px;\r\n\t\tborder-radius: 100px;\r\n\t\tmargin-left: 6px;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.4);\n\t\t/* #ifndef APP-NVUE */\n\t\tcursor: pointer;\n\t\t/* #endif */\n\t\t/* #ifdef H5 */\n\t\t// border-width: 5px 0;\n\t\t// border-style: solid;\n\t\t// border-color: transparent;\n\t\t// background-clip: padding-box;\n\t\t/* #endif */\r\n\t\t// transition: width 0.2s linear;  不要取消注释，不然会不能变色\r\n\t}\r\n\r\n\t.uni-swiper__dots-item:first-child {\r\n\t\tmargin: 0;\r\n\t}\r\n\r\n\t.uni-swiper__dots-default {\r\n\t\tborder-radius: 100px;\r\n\t}\r\n\r\n\t.uni-swiper__dots-long {\r\n\t\tborder-radius: 50px;\r\n\t}\r\n\r\n\t.uni-swiper__dots-bar {\r\n\t\tborder-radius: 50px;\r\n\t}\r\n\r\n\t.uni-swiper__dots-nav {\r\n\t\tbottom: 0px;\r\n\t\t// height: 26px;\n\t\tpadding: 8px 0;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex: 1;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: flex-start;\r\n\t\talign-items: center;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.2);\r\n\t}\r\n\r\n\t.uni-swiper__dots-nav-item {\r\n\t\t/* overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap; */\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #fff;\r\n\t\tmargin: 0 15px;\r\n\t}\r\n\r\n\t.uni-swiper__dots-indexes {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\t// flex: 1;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.uni-swiper__dots-indexes-text {\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 12px;\n\t\tline-height: 14px;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-swiper-dot.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-swiper-dot.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1748504379732\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}