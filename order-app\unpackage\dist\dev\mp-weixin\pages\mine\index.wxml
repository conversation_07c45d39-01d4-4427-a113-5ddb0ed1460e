<view class="mine-container" style="{{'height:'+(windowHeight+'px')+';'}}"><view class="header-section"><view class="flex padding justify-between"><view class="flex align-center"><block wx:if="{{!avatar}}"><view class="cu-avatar xl round bg-white"><view class="iconfont icon-people text-gray icon"></view></view></block><block wx:if="{{avatar}}"><image class="cu-avatar xl round" src="{{avatar}}" mode="widthFix" data-event-opts="{{[['tap',[['handleToAvatar',['$event']]]]]}}" bindtap="__e"></image></block><block wx:if="{{!name}}"><view data-event-opts="{{[['tap',[['handleToLogin',['$event']]]]]}}" class="login-tip" bindtap="__e">点击登录</view></block><block wx:if="{{name}}"><view data-event-opts="{{[['tap',[['handleToInfo',['$event']]]]]}}" class="user-info" bindtap="__e"><view class="u_title">{{'用户名：'+name+''}}</view></view></block></view><view data-event-opts="{{[['tap',[['handleToInfo',['$event']]]]]}}" class="flex align-center" bindtap="__e"><text>个人信息</text><view class="iconfont icon-right"></view></view></view></view><view class="content-section"><view class="menu-list"><view data-event-opts="{{[['tap',[['handleToEditInfo',['$event']]]]]}}" class="list-cell list-cell-arrow" bindtap="__e"><view class="menu-item-box"><view class="iconfont icon-user menu-icon"></view><view>编辑资料</view></view></view><view data-event-opts="{{[['tap',[['handleLogout',['$event']]]]]}}" class="list-cell list-cell-arrow" bindtap="__e"><view class="menu-item-box"><view class="iconfont icon-setting menu-icon"></view><view>退出登录</view></view></view></view></view></view>