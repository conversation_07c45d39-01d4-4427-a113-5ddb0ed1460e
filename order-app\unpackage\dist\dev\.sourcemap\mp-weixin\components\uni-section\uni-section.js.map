{"version": 3, "sources": ["webpack:///D:/项目/工单小程序/order-app/components/uni-section/uni-section.vue?c8fc", "webpack:///D:/项目/工单小程序/order-app/components/uni-section/uni-section.vue?add1", "webpack:///D:/项目/工单小程序/order-app/components/uni-section/uni-section.vue?b50d", "webpack:///D:/项目/工单小程序/order-app/components/uni-section/uni-section.vue?3c3c", "uni-app:///components/uni-section/uni-section.vue", "webpack:///D:/项目/工单小程序/order-app/components/uni-section/uni-section.vue?e145", "webpack:///D:/项目/工单小程序/order-app/components/uni-section/uni-section.vue?2fd9"], "names": ["name", "emits", "props", "type", "default", "title", "required", "titleFontSize", "titleColor", "subTitle", "subTitleFontSize", "subTitleColor", "padding", "computed", "_padding", "watch", "uni", "methods", "onClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAunB,CAAgB,qnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwB3oB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,eAgBA;EACAA;EACAC;EACAC;IACAC;MACAA;MACAC;IACA;IACAC;MACAF;MACAG;MACAF;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;EACA;EACAS;IACAC;MACA;QACA;MACA;MAEA;IACA;EACA;EACAC;IACAV;MACA;QACAW;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnGA;AAAA;AAAA;AAAA;AAA8qC,CAAgB,omCAAG,EAAC,C;;;;;;;;;;;ACAlsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uni-section/uni-section.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-section.vue?vue&type=template&id=5584ec96&\"\nvar renderjs\nimport script from \"./uni-section.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-section.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-section.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uni-section/uni-section.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-section.vue?vue&type=template&id=5584ec96&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-section.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-section.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"uni-section\">\n\t\t<view class=\"uni-section-header\" @click=\"onClick\">\n\t\t\t\t<view class=\"uni-section-header__decoration\" v-if=\"type\" :class=\"type\" />\n        <slot v-else name=\"decoration\"></slot>\n\n        <view class=\"uni-section-header__content\">\n          <text :style=\"{'font-size':titleFontSize,'color':titleColor}\" class=\"uni-section__content-title\" :class=\"{'distraction':!subTitle}\">{{ title }}</text>\n          <text v-if=\"subTitle\" :style=\"{'font-size':subTitleFontSize,'color':subTitleColor}\" class=\"uni-section-header__content-sub\">{{ subTitle }}</text>\n        </view>\n\n        <view class=\"uni-section-header__slot-right\">\n          <slot name=\"right\"></slot>\n        </view>\n\t\t</view>\n\n\t\t<view class=\"uni-section-content\" :style=\"{padding: _padding}\">\n\t\t\t<slot />\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\n\t/**\n\t * Section 标题栏\n\t * @description 标题栏\n\t * @property {String} type = [line|circle|square] 标题装饰类型\n\t * \t@value line 竖线\n\t * \t@value circle 圆形\n\t * \t@value square 正方形\n\t * @property {String} title 主标题\n\t * @property {String} titleFontSize 主标题字体大小\n\t * @property {String} titleColor 主标题字体颜色\n\t * @property {String} subTitle 副标题\n\t * @property {String} subTitleFontSize 副标题字体大小\n\t * @property {String} subTitleColor 副标题字体颜色\n\t * @property {String} padding 默认插槽 padding\n\t */\n\n\texport default {\n\t\tname: 'UniSection',\n    emits:['click'],\n\t\tprops: {\n\t\t\ttype: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\ttitle: {\n\t\t\t\ttype: String,\n\t\t\t\trequired: true,\n\t\t\t\tdefault: ''\n\t\t\t},\n      titleFontSize: {\n        type: String,\n        default: '14px'\n      },\n\t\t\ttitleColor:{\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '#333'\n\t\t\t},\n\t\t\tsubTitle: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n      subTitleFontSize: {\n        type: String,\n        default: '12px'\n      },\n      subTitleColor: {\n        type: String,\n        default: '#999'\n      },\n\t\t\tpadding: {\n\t\t\t\ttype: [Boolean, String],\n\t\t\t\tdefault: false\n\t\t\t}\n\t\t},\n    computed:{\n      _padding(){\n        if(typeof this.padding === 'string'){\n          return this.padding\n        }\n\n        return this.padding?'10px':''\n      }\n    },\n\t\twatch: {\n\t\t\ttitle(newVal) {\n\t\t\t\tif (uni.report && newVal !== '') {\n\t\t\t\t\tuni.report('title', newVal)\n\t\t\t\t}\n\t\t\t}\n\t\t},\n    methods: {\n\t\t\tonClick() {\n\t\t\t\tthis.$emit('click')\n\t\t\t}\n\t\t}\n\t}\n</script>\n<style lang=\"scss\" >\n\t$uni-primary: #2979ff !default;\n\n\t.uni-section {\n\t\tbackground-color: #fff;\n    .uni-section-header {\n      position: relative;\n      /* #ifndef APP-NVUE */\n      display: flex;\n      /* #endif */\n      flex-direction: row;\n      align-items: center;\n      padding: 12px 10px;\n      font-weight: normal;\n\n      &__decoration{\n        margin-right: 6px;\n        background-color: $uni-primary;\n        &.line {\n          width: 4px;\n          height: 12px;\n          border-radius: 10px;\n        }\n\n        &.circle {\n          width: 8px;\n          height: 8px;\n          border-top-right-radius: 50px;\n          border-top-left-radius: 50px;\n          border-bottom-left-radius: 50px;\n          border-bottom-right-radius: 50px;\n        }\n\n        &.square {\n          width: 8px;\n          height: 8px;\n        }\n      }\n\n      &__content {\n        /* #ifndef APP-NVUE */\n        display: flex;\n        /* #endif */\n        flex-direction: column;\n        flex: 1;\n        color: #333;\n\n        .distraction {\n          flex-direction: row;\n          align-items: center;\n        }\n        &-sub {\n          margin-top: 2px;\n        }\n      }\n\n      &__slot-right{\n        font-size: 14px;\n      }\n    }\n\n    .uni-section-content{\n      font-size: 14px;\n    }\n\t}\n</style>\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-section.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-section.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1748504379699\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}