<template>
  <view class="order-list-container">
    <!-- 权限检查：有权限的用户显示正常页面 -->
    <template v-if="hasPermission">
      <!-- 悬浮按钮 -->
      <view class="fab-button" @click="goToAddOrder">
        <u-icon name="plus" size="50rpx" color="#ffffff"></u-icon>
      </view>
      <!-- 搜索区域 -->
      <view class="search-section">
        <view class="search-box">
          <u-icon name="search" size="36rpx" color="#8c9fba"></u-icon>
          <input 
            type="text" 
            v-model="queryParams.orderProductNumberVersion" 
            placeholder="请输入产品编号版本搜索" 
            confirm-type="search"
            @input="onSearchInput"
            @confirm="handleSearch"
          />
          <text class="search-btn" @click="handleSearch">搜索</text>
        </view>
      </view>
      
      <!-- 统计信息区域 -->
      <view class="stats-section" v-if="hasPermission">
        <view class="stats-item">
          <text class="stats-label">工单总量：</text>
          <text class="stats-value">{{ total || 0 }}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">当前显示：</text>
          <text class="stats-value">{{ orderList.length }}</text>
        </view>
      </view>
      
      <!-- 列表区域 -->
      <scroll-view 
        class="order-list-scroll" 
        :scroll-y="true" 
        @scrolltolower="loadMore" 
        :enable-back-to-top="true"
        :refresher-enabled="true"
        :refresher-triggered="isRefreshing"
        @refresherrefresh="onRefresh"
      >
        <!-- 列表为空时显示 -->
        <view class="empty-container" v-if="orderList.length === 0 && !loading">
          <u-empty mode="data" text="暂无工单数据"></u-empty>
        </view>
        
        <!-- 工单列表 -->
        <view class="order-list" v-else>
          <view class="order-card" v-for="(item, index) in orderList" :key="index" @click="toOrderDetail(item)">
            <view class="order-header">
              <view class="order-no">工单编号：{{ item.workOrderNo }}</view>
              <view class="order-status" 
                    :class="{
                      'status-pending': item.status === '0',
                      'status-processing': item.status === '1',
                      'status-completed': item.status === '2'
                    }">
                {{ getStatusText(item.status) }}
              </view>
            </view>
            
            <view class="order-content">
              <view class="info-row">
                <text class="info-label">产品名称：</text>
                <text class="info-value">{{ item.productName }}</text>
              </view>
              <view class="info-row" v-if="item.orderProductNumberVersion">
                <text class="info-label">产品编号：</text>
                <text class="info-value">{{ item.orderProductNumberVersion }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">计划数量：</text>
                <text class="info-value">{{ item.totalPlannedQuantity }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">计划开始：</text>
                <text class="info-value">{{ formatDate(item.overallPlannedStartTime) }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">计划结束：</text>
                <text class="info-value">{{ formatDate(item.overallPlannedEndTime) }}</text>
              </view>
              <view class="info-row" v-if="item.remark">
                <text class="info-label">备注：</text>
                <text class="info-value remark-text">{{ item.remark }}</text>
              </view>
              
              <!-- 操作按钮区域 -->
              <view class="action-buttons">
                <button class="delete-btn" @click.stop="deleteOrder(item)">
                  删除
                </button>
                <button class="edit-btn" @click.stop="goToEditOrder(item)">
                  修改
                </button>
              </view>
            </view>
          </view>
          
          <!-- 加载更多 -->
          <view class="loading-more" v-if="loading">
            <u-loading mode="circle" size="24"></u-loading>
            <text class="loading-text">加载中...</text>
          </view>
          
          <!-- 没有更多数据 -->
          <view class="no-more" v-if="!hasMore && orderList.length > 0">
            <text>没有更多数据了</text>
          </view>
        </view>
      </scroll-view>
    </template>
    
    <!-- 无权限时显示 -->
    <template v-else>
      <view class="no-permission-container">
        <view class="no-permission-content">
          <view class="permission-icon">
            <u-icon name="lock" size="120rpx" color="#909399"></u-icon>
          </view>
          <view class="permission-title">访问受限</view>
          <view class="permission-description">
            抱歉，您暂无权限访问工单管理页面
          </view>
        </view>
      </view>
    </template>
  </view>
</template>

<script>
import { listOrders,delOrders } from "@/api/work/orders.js"

export default {
      data() {
        return {
          queryParams: {
            pageSize: 5,
            pageNum: 1,
            orderProductNumberVersion: null
          },
          orderList: [],
          total: 0,
          loading: false,
          isRefreshing: false,
          hasMore: true,
          searchTimer: null, // 用于防抖处理
          searchDelay: 500, // 搜索延迟时间（毫秒）
          roles: this.$store.state.user.roles
        }
      },
  computed: {
    // 是否有更多数据
    hasMoreData() {
      return this.orderList.length < this.total;
    },
    
    // 权限检查：是否包含admin、dispatcher或repairman角色
    hasPermission() {
      if (!this.roles || !Array.isArray(this.roles)) {
        return false;
      }
      const allowedRoles = ['admin', 'dispatcher', 'repairman'];
      return this.roles.some(role => allowedRoles.includes(role));
    }
  },
  onLoad() {
	  console.log(this.roles);
    // 只有有权限的用户才获取数据
    if (this.hasPermission) {
      this.getOrderList();
    }
  },
  methods: {
    // 获取工单列表
    async getOrderList(isRefresh = false) {
      // 权限检查
      if (!this.hasPermission) {
        return;
      }
      
      if (this.loading) return;
      
      this.loading = true;
      
      try {
        const res = await listOrders(this.queryParams);
        
        if (res && res.rows) {
          if (isRefresh) {
            this.orderList = res.rows;
            
            // 如果是刷新操作，显示成功提示
            if (this.isRefreshing) {
              uni.showToast({
                title: '刷新成功',
                icon: 'success',
                duration: 1500
              });
            }
          } else {
            this.orderList = [...this.orderList, ...res.rows];
          }
          
          this.total = res.total;
          this.hasMore = this.orderList.length < this.total;
          
          // 如果没有数据，显示提示
          if (isRefresh && this.orderList.length === 0) {
            uni.showToast({
              title: '暂无工单数据',
              icon: 'none',
              duration: 2000
            });
          }
        } else {
          // 处理响应格式不符合预期的情况
          uni.showToast({
            title: '数据格式异常',
            icon: 'none',
            duration: 2000
          });
        }
      } catch (error) {
        console.error('获取工单列表失败', error);
        
        // 提供更具体的错误信息
        let errorMsg = '获取数据失败';
        if (error.message) {
          errorMsg = error.message.includes('timeout') 
            ? '请求超时，请检查网络' 
            : '获取数据失败: ' + error.message.substring(0, 20);
        }
        
        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        });
      } finally {
        this.loading = false;
        if (this.isRefreshing) {
          this.isRefreshing = false;
        }
      }
    },
    
    // 处理搜索输入（防抖）
    onSearchInput() {
      // 权限检查
      if (!this.hasPermission) {
        return;
      }
      
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
      
      // 设置新的定时器
      this.searchTimer = setTimeout(() => {
        this.handleSearch();
      }, this.searchDelay);
    },
    
    // 搜索
    handleSearch() {
      // 权限检查
      if (!this.hasPermission) {
        return;
      }
      
      // 清除可能存在的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
        this.searchTimer = null;
      }
      
      this.queryParams.pageNum = 1;
      this.orderList = [];
      this.getOrderList(true);
    },
    
    // 加载更多
    loadMore() {
      if (!this.hasPermission || !this.hasMore || this.loading) return;
      
      this.queryParams.pageNum += 1;
      this.getOrderList();
    },
    
    // 下拉刷新
    onRefresh() {
      if (!this.hasPermission) return;
      
      this.isRefreshing = true;
      this.queryParams.pageNum = 1;
      this.getOrderList(true);
    },
    
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '--';
      
      try {
        // 处理可能的日期格式
        let date;
        if (dateString.includes('T')) {
          // 处理ISO格式的日期
          date = new Date(dateString);
        } else {
          // 处理普通日期字符串
          date = new Date(dateString.replace(/-/g, '/'));
        }
        
        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return '--';
        }
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        
        return `${year}-${month}-${day}`;
      } catch (error) {
        console.error('日期格式化错误:', error);
        return '--';
      }
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        '0': '未开始',
        '1': '处理中',
        '2': '已完成'
      };
      return statusMap[status] || '未知状态';
    },
    
    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        '0': 'status-pending',
        '1': 'status-processing',
        '2': 'status-completed'
      };
      return classMap[status] || '';
    },
    
    // 跳转到新增工单页面
    goToAddOrder() {
      if (!this.hasPermission) {
        uni.showToast({
          title: '无权限执行此操作',
          icon: 'none'
        });
        return;
      }
      
      uni.navigateTo({
        url:"/pages/addOrder/addOrder"
      });
    },
	
	//跳转详情页
	toOrderDetail(item){
      if (!this.hasPermission) {
        uni.showToast({
          title: '无权限执行此操作',
          icon: 'none'
        });
        return;
      }
      
		uni.navigateTo({
			url:"/pages/orderDetail/orderDetail?id="+item.workOrderId
		})
	},
	
	//跳转到编辑工单页面
	goToEditOrder(item) {
      if (!this.hasPermission) {
        uni.showToast({
          title: '无权限执行此操作',
          icon: 'none'
        });
        return;
      }
      
		uni.navigateTo({
			url:"/pages/addOrder/addOrder?id="+item.workOrderId
		})
	},
	
	//删除工单
	async deleteOrder(item) {
      if (!this.hasPermission) {
        uni.showToast({
          title: '无权限执行此操作',
          icon: 'none'
        });
        return;
      }
      
      try {
        const result = await new Promise((resolve) => {
          uni.showModal({
            title: '确认删除',
            content: `确定要删除工单 "${item.workOrderNo}" 吗？删除后无法恢复。`,
            confirmText: '删除',
            confirmColor: '#ff5252',
            cancelText: '取消',
            success: (res) => {
              resolve(res.confirm);
            }
          });
        });
        
        if (result) {
          uni.showLoading({
            title: '删除中...',
            mask: true
          });
          
          await delOrders(item.workOrderId);
          
          uni.hideLoading();
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });
          
          // 刷新列表
          this.onRefresh();
        }
      } catch (error) {
        uni.hideLoading();
        console.error('删除工单失败：', error);
        uni.showToast({
          title: '删除失败: ' + (error.message || '未知错误'),
          icon: 'none',
          duration: 3000
        });
      }
    }
  }
}
</script>

<style lang="scss">
.order-list-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

/* 无权限页面样式 */
.no-permission-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f5f7fa;
  
  .no-permission-content {
    text-align: center;
    padding: 60rpx 40rpx;
    
    .permission-icon {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 40rpx;
    }
    
    .permission-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 20rpx;
    }
    
    .permission-description {
      font-size: 28rpx;
      color: #606266;
      line-height: 1.6;
    }
  }
}

/* 搜索区域样式 */
.search-section {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 10;
  
  .search-box {
    display: flex;
    align-items: center;
    height: 80rpx;
    background-color: #f5f7fa;
    border-radius: 40rpx;
    padding: 0 30rpx;
    
    .u-icon {
      margin-right: 20rpx;
    }
    
    input {
      flex: 1;
      height: 80rpx;
      font-size: 28rpx;
      color: #333;
    }
    
    .search-btn {
      padding: 0 20rpx;
      height: 60rpx;
      line-height: 60rpx;
      background-color: #1e3a8a;
      color: #ffffff;
      font-size: 26rpx;
      border-radius: 30rpx;
    }
  }
}

/* 列表区域样式 */
.order-list-scroll {
  flex: 1;
  width: 100%;
  height: 75vh;
}

.order-list {
  padding: 20rpx;
}

/* 工单卡片样式 */
.order-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 30rpx;
    border-bottom: 2rpx solid #f0f2f5;
    
    .order-no {
      font-size: 28rpx;
      font-weight: 500;
      color: #333;
    }
    
    .order-status {
      padding: 6rpx 20rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      
      &.status-pending {
        background-color: #e6f7ff;
        color: #1890ff;
      }
      
      &.status-processing {
        background-color: #fff7e6;
        color: #fa8c16;
      }
      
      &.status-completed {
        background-color: #f6ffed;
        color: #52c41a;
      }
    }
  }
  
  .order-content {
    padding: 20rpx 30rpx;
    
    .info-row {
      display: flex;
      margin-bottom: 16rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .info-label {
        width: 160rpx;
        font-size: 26rpx;
        color: #666;
      }
      
      .info-value {
        flex: 1;
        font-size: 26rpx;
        color: #333;
      }
      
      .remark-text {
        color: #8c9fba;
      }
    }
    
    /* 操作按钮区域 */
    .action-buttons {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 20rpx;
      margin-top: 24rpx;
      padding-top: 20rpx;
      border-top: 1px solid #f0f2f5;
      
      .edit-btn, .delete-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 12rpx 24rpx;
        border: none;
        border-radius: 8rpx;
        font-size: 26rpx;
        font-weight: 500;
        min-width: 100rpx;
        height: 60rpx;
        color: #ffffff;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s ease;
        }
        
        &:active {
          transform: translateY(1rpx);
          
          &::before {
            left: 100%;
          }
        }
      }
      
      .edit-btn {
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        
        &:active {
          box-shadow: 0 1rpx 4rpx rgba(30, 58, 138, 0.3);
        }
      }
      
      .delete-btn {
        background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        
        &:active {
          box-shadow: 0 1rpx 4rpx rgba(220, 38, 38, 0.3);
        }
      }
    }
  }
}

/* 空数据样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #8c9fba;
  }
}

/* 加载更多和无更多数据样式 */
.loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #8c9fba;
}

/* 悬浮按钮样式 */
.fab-button {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #1e3a8a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(30, 58, 138, 0.4);
  z-index: 100;
  transition: all 0.3s;
  
  &:active {
    transform: scale(0.95);
  }
  
  /* 使用u-icon组件，不需要额外的图标样式 */
}

/* 统计信息区域样式 */
.stats-section {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .stats-item {
    display: flex;
    align-items: center;
    
    .stats-label {
      font-size: 26rpx;
      color: #606266;
      margin-right: 8rpx;
    }
    
    .stats-value {
      font-size: 28rpx;
      color: #1e3a8a;
      font-weight: 600;
    }
  }
}
</style>