@charset "UTF-8";
/**
 * uni-app内置的常用样式变量
 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-00369c57 {
  background-color: #f8f8f8;
}
.help-container.data-v-00369c57 {
  margin-bottom: 100rpx;
  padding: 30rpx;
}
.list-title.data-v-00369c57 {
  margin-bottom: 30rpx;
}
.childList.data-v-00369c57 {
  background: #ffffff;
  box-shadow: 0px 0px 10rpx rgba(193, 193, 193, 0.2);
  border-radius: 16rpx;
  margin-top: 10rpx;
}
.line.data-v-00369c57 {
  width: 100%;
  height: 1rpx;
  background-color: #F5F5F5;
}
.text-title.data-v-00369c57 {
  color: #303133;
  font-size: 32rpx;
  font-weight: bold;
  margin-left: 10rpx;
}
.text-title .iconfont.data-v-00369c57 {
  font-size: 16px;
  margin-right: 10rpx;
}
.text-item.data-v-00369c57 {
  font-size: 28rpx;
  padding: 24rpx;
}
.question.data-v-00369c57 {
  color: #606266;
  font-size: 28rpx;
}
