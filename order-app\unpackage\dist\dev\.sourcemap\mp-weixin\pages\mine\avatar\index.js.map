{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/avatar/index.vue?1caa", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/avatar/index.vue?2db4", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/avatar/index.vue?4f3a", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/avatar/index.vue?1e70", "uni-app:///pages/mine/avatar/index.vue", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/avatar/index.vue?6ef6", "webpack:///D:/项目/工单小程序/work_order/order-app/pages/mine/avatar/index.vue?f07c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "PAGE_Y", "PR", "T_PAGE_X", "T_PAGE_Y", "CUT_L", "CUT_T", "CUT_R", "CUT_B", "CUT_W", "CUT_H", "IMG_RATIO", "IMG_REAL_W", "IMG_REAL_H", "DRAFG_MOVE_RATIO", "INIT_DRAG_POSITION", "DRAW_IMAGE_W", "data", "imageSrc", "isShowImg", "cropperInitW", "cropperInitH", "cropperW", "cropperH", "cropperL", "cropperT", "transL", "transT", "scaleP", "imageW", "imageH", "cutL", "cutT", "cutB", "cutR", "qualityWidth", "innerAspectRadio", "onReady", "methods", "setData", "Object", "that", "getImage", "uni", "success", "_this", "loadImage", "src", "contentStartMove", "PAGE_X", "contentMoveing", "contentTouchEnd", "getImageInfo", "title", "ctx", "x", "y", "width", "height", "destWidth", "destHeight", "quality", "canvasId", "name", "filePath", "store", "icon", "dragStart", "dragMove"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC4K;AAC5K,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+oB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACuCnqB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;EAAA;EACAC;EAAA;EACAC;EAAA;EACAC;EAAA;EACAC;EAAA;EACAC;EAAA;EACAC;EAAA;EACAC;EAAA;EACAC;EAAA;EACAC;EAAA;EACAC;EAAA;EACAC;EAAA;EACAC;EAAA;EACAC;EAAA;EACAC;EAAA;EACAC;EAAA;EACAC;AAAA,eAEA;EACA;AACA;AACA;EACAC;IACA;MACAC;MACAC;MACA;MACAC;MACAC;MACA;MACAC;MACAC;MACA;MACAC;MACAC;MAEAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACA;AACA;AACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;QACAC;MACA;IACA;IACAC;MACA;MACAC;QACAC;UACAC;YACA3B;UACA;UACA2B;QACA;MACA;IACA;IACAC;MACA;MAEAH;QACAI;QACAH;UACAjC;UACA;YACAC;YACAC;UACA;YACAD;YACAC;UACA;UACA;UACAE;UACA;UACA;YACA;YACA;YACA;YACA;YACA8B;cACAvB;cACAC;cACA;cACAC;cACAC;cACAM;cACAC;cACAE;cACAD;cACA;cACAJ;cACAC;cACAF;cACAO;cACAC;YACA;UACA;YACA;YACA;YACA;YACA;YACAS;cACAvB;cACAC;cACA;cACAC;cACAC;cAEAM;cACAC;cACAE;cACAD;cACA;cACAJ;cACAC;cACAF;cACAO;cACAC;YACA;UACA;UACAS;YACA1B;UACA;UACAwB;QACA;MACA;IACA;IACA;IACAK;MACAC;MACAhD;IACA;IAEA;IACAiD;MACA;MACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;MAEA;QACA;MACA;QACA;MACA;MACA;QACAnB;QACAC;QACAE;QACAD;MACA;MAEAgB;MACAhD;IACA;IAEAkD,6CAEA;IAEA;IACAC;MACA;MACAT;QACAU;MACA;MACA;MACA;MACAC;MACAA;QACA;QACA;QACA;QACA;QACA;QACAX;UACAY;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAlB;YACAD;YACA;cAAAoB;cAAAC;YAAA;YACA;cACAC;cACAtB;gBAAAU;gBAAAa;cAAA;cACAvB;YACA;UACA;QACA;MACA;IACA;IACA;IACAwB;MACAhE;MACAC;MACAC;MACAE;MACAC;MACAF;IACA;IAEA;IACA8D;MACA;MACA;MACA;QACA;UACA;UACA;UACA;YACAlC;UACA;UACA;QACA;UACA;UACA;UACA;UACA;YACAH;UACA;UACA;QACA;UACA;UACA;UACA;UACA;YACAC;UACA;UACA;QACA;UACA;UACA;UACA;YACAC;UACA;UACA;QACA;UACA;UACA;UAEA;UACA;UACA;UACA;UAEA;YACAA;YACAC;UACA;UACA;QACA;UACA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3UA;AAAA;AAAA;AAAA;AAAo7B,CAAgB,o2BAAG,EAAC,C;;;;;;;;;;;ACAx8B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/avatar/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/avatar/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=05deedaf&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/avatar/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=05deedaf&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"page-body uni-content-info\">\r\n\t\t\t<view class='cropper-content'>\r\n\t\t\t\t<view v-if=\"isShowImg\" class=\"uni-corpper\" :style=\"'width:'+cropperInitW+'px;height:'+cropperInitH+'px;background:#000'\">\r\n\t\t\t\t\t<view class=\"uni-corpper-content\" :style=\"'width:'+cropperW+'px;height:'+cropperH+'px;left:'+cropperL+'px;top:'+cropperT+'px'\">\r\n\t\t\t\t\t\t<image :src=\"imageSrc\" :style=\"'width:'+cropperW+'px;height:'+cropperH+'px'\"></image>\r\n\t\t\t\t\t\t<view class=\"uni-corpper-crop-box\" @touchstart.stop=\"contentStartMove\" @touchmove.stop=\"contentMoveing\" @touchend.stop=\"contentTouchEnd\"\r\n\t\t\t\t\t\t    :style=\"'left:'+cutL+'px;top:'+cutT+'px;right:'+cutR+'px;bottom:'+cutB+'px'\">\r\n\t\t\t\t\t\t\t<view class=\"uni-cropper-view-box\">\r\n\t\t\t\t\t\t\t\t<view class=\"uni-cropper-dashed-h\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"uni-cropper-dashed-v\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"uni-cropper-line-t\" data-drag=\"top\" @touchstart.stop=\"dragStart\" @touchmove.stop=\"dragMove\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"uni-cropper-line-r\" data-drag=\"right\" @touchstart.stop=\"dragStart\" @touchmove.stop=\"dragMove\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"uni-cropper-line-b\" data-drag=\"bottom\" @touchstart.stop=\"dragStart\" @touchmove.stop=\"dragMove\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"uni-cropper-line-l\" data-drag=\"left\" @touchstart.stop=\"dragStart\" @touchmove.stop=\"dragMove\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"uni-cropper-point point-t\" data-drag=\"top\" @touchstart.stop=\"dragStart\" @touchmove.stop=\"dragMove\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"uni-cropper-point point-tr\" data-drag=\"topTight\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"uni-cropper-point point-r\" data-drag=\"right\" @touchstart.stop=\"dragStart\" @touchmove.stop=\"dragMove\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"uni-cropper-point point-rb\" data-drag=\"rightBottom\" @touchstart.stop=\"dragStart\" @touchmove.stop=\"dragMove\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"uni-cropper-point point-b\" data-drag=\"bottom\" @touchstart.stop=\"dragStart\" @touchmove.stop=\"dragMove\" @touchend.stop=\"dragEnd\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"uni-cropper-point point-bl\" data-drag=\"bottomLeft\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"uni-cropper-point point-l\" data-drag=\"left\" @touchstart.stop=\"dragStart\" @touchmove.stop=\"dragMove\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"uni-cropper-point point-lt\" data-drag=\"leftTop\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class='cropper-config'>\r\n\t\t\t\t<button type=\"primary reverse\" @click=\"getImage\" style='margin-top: 30rpx;'> 选择头像 </button>\r\n\t\t\t\t<button type=\"warn\" @click=\"getImageInfo\" style='margin-top: 30rpx;'> 提交 </button>\r\n\t\t\t</view>\r\n\t\t\t<canvas canvas-id=\"myCanvas\" :style=\"'position:absolute;border: 1px solid red; width:'+imageW+'px;height:'+imageH+'px;top:-9999px;left:-9999px;'\"></canvas>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n  import config from '@/config'\r\n  import store from \"@/store\"\r\n  import { uploadAvatar } from \"@/api/system/user\"\r\n  \r\n  const baseUrl = config.baseUrl\r\n\tlet sysInfo = uni.getSystemInfoSync()\r\n\tlet SCREEN_WIDTH = sysInfo.screenWidth\r\n\tlet PAGE_X, // 手按下的x位置\r\n\t\tPAGE_Y, // 手按下y的位置 \r\n\t\tPR = sysInfo.pixelRatio, // dpi\r\n\t\tT_PAGE_X, // 手移动的时候x的位置\r\n\t\tT_PAGE_Y, // 手移动的时候Y的位置\r\n\t\tCUT_L, // 初始化拖拽元素的left值\r\n\t\tCUT_T, // 初始化拖拽元素的top值\r\n\t\tCUT_R, // 初始化拖拽元素的\r\n\t\tCUT_B, // 初始化拖拽元素的\r\n\t\tCUT_W, // 初始化拖拽元素的宽度\r\n\t\tCUT_H, //  初始化拖拽元素的高度\r\n\t\tIMG_RATIO, // 图片比例\r\n\t\tIMG_REAL_W, // 图片实际的宽度\r\n\t\tIMG_REAL_H, // 图片实际的高度\r\n\t\tDRAFG_MOVE_RATIO = 1, //移动时候的比例,\r\n\t\tINIT_DRAG_POSITION = 100, // 初始化屏幕宽度和裁剪区域的宽度之差，用于设置初始化裁剪的宽度\r\n\t\tDRAW_IMAGE_W = sysInfo.screenWidth // 设置生成的图片宽度\r\n\r\n\texport default {\r\n\t\t/**\r\n\t\t * 页面的初始数据\r\n\t\t */\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\timageSrc: store.getters.avatar,\r\n\t\t\t\tisShowImg: false,\r\n\t\t\t\t// 初始化的宽高\r\n\t\t\t\tcropperInitW: SCREEN_WIDTH,\r\n\t\t\t\tcropperInitH: SCREEN_WIDTH,\r\n\t\t\t\t// 动态的宽高\r\n\t\t\t\tcropperW: SCREEN_WIDTH,\r\n\t\t\t\tcropperH: SCREEN_WIDTH,\r\n\t\t\t\t// 动态的left top值\r\n\t\t\t\tcropperL: 0,\r\n\t\t\t\tcropperT: 0,\r\n\r\n\t\t\t\ttransL: 0,\r\n\t\t\t\ttransT: 0,\r\n\r\n\t\t\t\t// 图片缩放值\r\n\t\t\t\tscaleP: 0,\r\n\t\t\t\timageW: 0,\r\n\t\t\t\timageH: 0,\r\n\r\n\t\t\t\t// 裁剪框 宽高\r\n\t\t\t\tcutL: 0,\r\n\t\t\t\tcutT: 0,\r\n\t\t\t\tcutB: SCREEN_WIDTH,\r\n\t\t\t\tcutR: '100%',\r\n\t\t\t\tqualityWidth: DRAW_IMAGE_W,\r\n\t\t\t\tinnerAspectRadio: DRAFG_MOVE_RATIO\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 生命周期函数--监听页面初次渲染完成\r\n\t\t */\r\n\t\tonReady: function () {\r\n\t\t\tthis.loadImage()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsetData: function (obj) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tObject.keys(obj).forEach(function (key) {\r\n\t\t\t\t\tthat.$set(that.$data, key, obj[key])\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetImage: function () {\r\n\t\t\t\tvar _this = this\r\n\t\t\t\tuni.chooseImage({\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\t_this.setData({\r\n\t\t\t\t\t\t\timageSrc: res.tempFilePaths[0],\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t_this.loadImage()\r\n\t\t\t\t\t},\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tloadImage: function () {\r\n\t\t\t\tvar _this = this\r\n\r\n\t\t\t\tuni.getImageInfo({\r\n\t\t\t\t\tsrc: _this.imageSrc,\r\n\t\t\t\t\tsuccess: function success(res) {\r\n\t\t\t\t\t\tIMG_RATIO = 1 / 1\r\n\t\t\t\t\t\tif (IMG_RATIO >= 1) {\r\n\t\t\t\t\t\t\tIMG_REAL_W = SCREEN_WIDTH\r\n\t\t\t\t\t\t\tIMG_REAL_H = SCREEN_WIDTH / IMG_RATIO\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tIMG_REAL_W = SCREEN_WIDTH * IMG_RATIO\r\n\t\t\t\t\t\t\tIMG_REAL_H = SCREEN_WIDTH\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tlet minRange = IMG_REAL_W > IMG_REAL_H ? IMG_REAL_W : IMG_REAL_H\r\n\t\t\t\t\t\tINIT_DRAG_POSITION = minRange > INIT_DRAG_POSITION ? INIT_DRAG_POSITION : minRange\r\n\t\t\t\t\t\t// 根据图片的宽高显示不同的效果   保证图片可以正常显示\r\n\t\t\t\t\t\tif (IMG_RATIO >= 1) {\r\n\t\t\t\t\t\t\tlet cutT = Math.ceil((SCREEN_WIDTH / IMG_RATIO - (SCREEN_WIDTH / IMG_RATIO - INIT_DRAG_POSITION)) / 2)\r\n\t\t\t\t\t\t\tlet cutB = cutT\r\n\t\t\t\t\t\t\tlet cutL = Math.ceil((SCREEN_WIDTH - SCREEN_WIDTH + INIT_DRAG_POSITION) / 2)\r\n\t\t\t\t\t\t\tlet cutR = cutL\r\n\t\t\t\t\t\t\t_this.setData({\r\n\t\t\t\t\t\t\t\tcropperW: SCREEN_WIDTH,\r\n\t\t\t\t\t\t\t\tcropperH: SCREEN_WIDTH / IMG_RATIO,\r\n\t\t\t\t\t\t\t\t// 初始化left right\r\n\t\t\t\t\t\t\t\tcropperL: Math.ceil((SCREEN_WIDTH - SCREEN_WIDTH) / 2),\r\n\t\t\t\t\t\t\t\tcropperT: Math.ceil((SCREEN_WIDTH - SCREEN_WIDTH / IMG_RATIO) / 2),\r\n\t\t\t\t\t\t\t\tcutL: cutL,\r\n\t\t\t\t\t\t\t\tcutT: cutT,\r\n\t\t\t\t\t\t\t\tcutR: cutR,\r\n\t\t\t\t\t\t\t\tcutB: cutB,\r\n\t\t\t\t\t\t\t\t// 图片缩放值\r\n\t\t\t\t\t\t\t\timageW: IMG_REAL_W,\r\n\t\t\t\t\t\t\t\timageH: IMG_REAL_H,\r\n\t\t\t\t\t\t\t\tscaleP: IMG_REAL_W / SCREEN_WIDTH,\r\n\t\t\t\t\t\t\t\tqualityWidth: DRAW_IMAGE_W,\r\n\t\t\t\t\t\t\t\tinnerAspectRadio: IMG_RATIO\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tlet cutL = Math.ceil((SCREEN_WIDTH * IMG_RATIO - (SCREEN_WIDTH * IMG_RATIO)) / 2)\r\n\t\t\t\t\t\t\tlet cutR = cutL\r\n\t\t\t\t\t\t\tlet cutT = Math.ceil((SCREEN_WIDTH - INIT_DRAG_POSITION) / 2)\r\n\t\t\t\t\t\t\tlet cutB = cutT\r\n\t\t\t\t\t\t\t_this.setData({\r\n\t\t\t\t\t\t\t\tcropperW: SCREEN_WIDTH * IMG_RATIO,\r\n\t\t\t\t\t\t\t\tcropperH: SCREEN_WIDTH,\r\n\t\t\t\t\t\t\t\t// 初始化left right\r\n\t\t\t\t\t\t\t\tcropperL: Math.ceil((SCREEN_WIDTH - SCREEN_WIDTH * IMG_RATIO) / 2),\r\n\t\t\t\t\t\t\t\tcropperT: Math.ceil((SCREEN_WIDTH - SCREEN_WIDTH) / 2),\r\n\r\n\t\t\t\t\t\t\t\tcutL: cutL,\r\n\t\t\t\t\t\t\t\tcutT: cutT,\r\n\t\t\t\t\t\t\t\tcutR: cutR,\r\n\t\t\t\t\t\t\t\tcutB: cutB,\r\n\t\t\t\t\t\t\t\t// 图片缩放值\r\n\t\t\t\t\t\t\t\timageW: IMG_REAL_W,\r\n\t\t\t\t\t\t\t\timageH: IMG_REAL_H,\r\n\t\t\t\t\t\t\t\tscaleP: IMG_REAL_W / SCREEN_WIDTH,\r\n\t\t\t\t\t\t\t\tqualityWidth: DRAW_IMAGE_W,\r\n\t\t\t\t\t\t\t\tinnerAspectRadio: IMG_RATIO\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t_this.setData({\r\n\t\t\t\t\t\t\tisShowImg: true\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 拖动时候触发的touchStart事件\r\n\t\t\tcontentStartMove(e) {\r\n\t\t\t\tPAGE_X = e.touches[0].pageX\r\n\t\t\t\tPAGE_Y = e.touches[0].pageY\r\n\t\t\t},\r\n\r\n\t\t\t// 拖动时候触发的touchMove事件\r\n\t\t\tcontentMoveing(e) {\r\n\t\t\t\tvar _this = this\r\n\t\t\t\tvar dragLengthX = (PAGE_X - e.touches[0].pageX) * DRAFG_MOVE_RATIO\r\n\t\t\t\tvar dragLengthY = (PAGE_Y - e.touches[0].pageY) * DRAFG_MOVE_RATIO\r\n\t\t\t\t// 左移\r\n\t\t\t\tif (dragLengthX > 0) {\r\n\t\t\t\t\tif (this.cutL - dragLengthX < 0) dragLengthX = this.cutL\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (this.cutR + dragLengthX < 0) dragLengthX = -this.cutR\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (dragLengthY > 0) {\r\n\t\t\t\t\tif (this.cutT - dragLengthY < 0) dragLengthY = this.cutT\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (this.cutB + dragLengthY < 0) dragLengthY = -this.cutB\r\n\t\t\t\t}\r\n\t\t\t\tthis.setData({\r\n\t\t\t\t\tcutL: this.cutL - dragLengthX,\r\n\t\t\t\t\tcutT: this.cutT - dragLengthY,\r\n\t\t\t\t\tcutR: this.cutR + dragLengthX,\r\n\t\t\t\t\tcutB: this.cutB + dragLengthY\r\n\t\t\t\t})\r\n\r\n\t\t\t\tPAGE_X = e.touches[0].pageX\r\n\t\t\t\tPAGE_Y = e.touches[0].pageY\r\n\t\t\t},\r\n\r\n\t\t\tcontentTouchEnd() {\r\n\r\n\t\t\t},\r\n\r\n\t\t\t// 获取图片\r\n\t\t\tgetImageInfo() {\r\n\t\t\t\tvar _this = this\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '图片生成中...',\r\n\t\t\t\t})\r\n\t\t\t\t// 将图片写入画布\r\n\t\t\t\tconst ctx = uni.createCanvasContext('myCanvas')\r\n\t\t\t\tctx.drawImage(_this.imageSrc, 0, 0, IMG_REAL_W, IMG_REAL_H)\r\n\t\t\t\tctx.draw(true, () => {\r\n\t\t\t\t\t// 获取画布要裁剪的位置和宽度   均为百分比 * 画布中图片的宽度    保证了在微信小程序中裁剪的图片模糊  位置不对的问题 canvasT = (_this.cutT / _this.cropperH) * (_this.imageH / pixelRatio)\r\n\t\t\t\t\tvar canvasW = ((_this.cropperW - _this.cutL - _this.cutR) / _this.cropperW) * IMG_REAL_W\r\n\t\t\t\t\tvar canvasH = ((_this.cropperH - _this.cutT - _this.cutB) / _this.cropperH) * IMG_REAL_H\r\n\t\t\t\t\tvar canvasL = (_this.cutL / _this.cropperW) * IMG_REAL_W\r\n\t\t\t\t\tvar canvasT = (_this.cutT / _this.cropperH) * IMG_REAL_H\r\n\t\t\t\t\tuni.canvasToTempFilePath({\r\n\t\t\t\t\t\tx: canvasL,\r\n\t\t\t\t\t\ty: canvasT,\r\n\t\t\t\t\t\twidth: canvasW,\r\n\t\t\t\t\t\theight: canvasH,\r\n\t\t\t\t\t\tdestWidth: canvasW,\r\n\t\t\t\t\t\tdestHeight: canvasH,\r\n\t\t\t\t\t\tquality: 0.5,\r\n\t\t\t\t\t\tcanvasId: 'myCanvas',\r\n\t\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\tlet data = {name: 'avatarfile', filePath: res.tempFilePath}\r\n\t\t\t\t\t\t\tuploadAvatar(data).then(response => {\r\n\t\t\t\t\t\t\t\tstore.commit('SET_AVATAR', baseUrl + response.imgUrl)\r\n\t\t\t\t\t\t\t\tuni.showToast({ title: \"修改成功\", icon: 'success' })\r\n\t\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 设置大小的时候触发的touchStart事件\r\n\t\t\tdragStart(e) {\r\n\t\t\t\tT_PAGE_X = e.touches[0].pageX\r\n\t\t\t\tT_PAGE_Y = e.touches[0].pageY\r\n\t\t\t\tCUT_L = this.cutL\r\n\t\t\t\tCUT_R = this.cutR\r\n\t\t\t\tCUT_B = this.cutB\r\n\t\t\t\tCUT_T = this.cutT\r\n\t\t\t},\r\n\r\n\t\t\t// 设置大小的时候触发的touchMove事件\r\n\t\t\tdragMove(e) {\r\n\t\t\t\tvar _this = this\r\n\t\t\t\tvar dragType = e.target.dataset.drag\r\n\t\t\t\tswitch (dragType) {\r\n\t\t\t\t\tcase 'right':\r\n\t\t\t\t\t\tvar dragLength = (T_PAGE_X - e.touches[0].pageX) * DRAFG_MOVE_RATIO\r\n\t\t\t\t\t\tif (CUT_R + dragLength < 0) dragLength = -CUT_R\r\n\t\t\t\t\t\tthis.setData({\r\n\t\t\t\t\t\t\tcutR: CUT_R + dragLength\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\tcase 'left':\r\n\t\t\t\t\t\tvar dragLength = (T_PAGE_X - e.touches[0].pageX) * DRAFG_MOVE_RATIO\r\n\t\t\t\t\t\tif (CUT_L - dragLength < 0) dragLength = CUT_L\r\n\t\t\t\t\t\tif ((CUT_L - dragLength) > (this.cropperW - this.cutR)) dragLength = CUT_L - (this.cropperW - this.cutR)\r\n\t\t\t\t\t\tthis.setData({\r\n\t\t\t\t\t\t\tcutL: CUT_L - dragLength\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\tcase 'top':\r\n\t\t\t\t\t\tvar dragLength = (T_PAGE_Y - e.touches[0].pageY) * DRAFG_MOVE_RATIO\r\n\t\t\t\t\t\tif (CUT_T - dragLength < 0) dragLength = CUT_T\r\n\t\t\t\t\t\tif ((CUT_T - dragLength) > (this.cropperH - this.cutB)) dragLength = CUT_T - (this.cropperH - this.cutB)\r\n\t\t\t\t\t\tthis.setData({\r\n\t\t\t\t\t\t\tcutT: CUT_T - dragLength\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\tcase 'bottom':\r\n\t\t\t\t\t\tvar dragLength = (T_PAGE_Y - e.touches[0].pageY) * DRAFG_MOVE_RATIO\r\n\t\t\t\t\t\tif (CUT_B + dragLength < 0) dragLength = -CUT_B\r\n\t\t\t\t\t\tthis.setData({\r\n\t\t\t\t\t\t\tcutB: CUT_B + dragLength\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\tcase 'rightBottom':\r\n\t\t\t\t\t\tvar dragLengthX = (T_PAGE_X - e.touches[0].pageX) * DRAFG_MOVE_RATIO\r\n\t\t\t\t\t\tvar dragLengthY = (T_PAGE_Y - e.touches[0].pageY) * DRAFG_MOVE_RATIO\r\n\r\n\t\t\t\t\t\tif (CUT_B + dragLengthY < 0) dragLengthY = -CUT_B\r\n\t\t\t\t\t\tif (CUT_R + dragLengthX < 0) dragLengthX = -CUT_R\r\n\t\t\t\t\t\tlet cutB = CUT_B + dragLengthY\r\n\t\t\t\t\t\tlet cutR = CUT_R + dragLengthX\r\n\r\n\t\t\t\t\t\tthis.setData({\r\n\t\t\t\t\t\t\tcutB: cutB,\r\n\t\t\t\t\t\t\tcutR: cutR\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* pages/uni-cropper/index.wxss */\r\n\r\n\t.uni-content-info {\r\n\t\t/* position: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tdisplay: block;\r\n\t\talign-items: center;\r\n\t\tflex-direction: column; */\r\n\t}\r\n\r\n\t.cropper-config {\r\n\t\tpadding: 20rpx 40rpx;\r\n\t}\r\n\r\n\t.cropper-content {\r\n\t\tmin-height: 750rpx;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.uni-corpper {\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t\t-webkit-user-select: none;\r\n\t\t-moz-user-select: none;\r\n\t\t-ms-user-select: none;\r\n\t\tuser-select: none;\r\n\t\t-webkit-tap-highlight-color: transparent;\r\n\t\t-webkit-touch-callout: none;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.uni-corpper-content {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.uni-corpper-content image {\r\n\t\tdisplay: block;\r\n\t\twidth: 100%;\r\n\t\tmin-width: 0 !important;\r\n\t\tmax-width: none !important;\r\n\t\theight: 100%;\r\n\t\tmin-height: 0 !important;\r\n\t\tmax-height: none !important;\r\n\t\timage-orientation: 0deg !important;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\t/* 移动图片效果 */\r\n\r\n\t.uni-cropper-drag-box {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tcursor: move;\r\n\t\tbackground: rgba(0, 0, 0, 0.6);\r\n\t\tz-index: 1;\r\n\t}\r\n\t/* 内部的信息 */\r\n\r\n\t.uni-corpper-crop-box {\r\n\t\tposition: absolute;\r\n\t\tbackground: rgba(255, 255, 255, 0.3);\r\n\t\tz-index: 2;\r\n\t}\r\n\r\n\t.uni-corpper-crop-box .uni-cropper-view-box {\r\n\t\tposition: relative;\r\n\t\tdisplay: block;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\toverflow: visible;\r\n\t\toutline: 1rpx solid #69f;\r\n\t\toutline-color: rgba(102, 153, 255, .75)\r\n\t}\r\n\t/* 横向虚线 */\r\n\r\n\t.uni-cropper-dashed-h {\r\n\t\tposition: absolute;\r\n\t\ttop: 33.33333333%;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 33.33333333%;\r\n\t\tborder-top: 1rpx dashed rgba(255, 255, 255, 0.5);\r\n\t\tborder-bottom: 1rpx dashed rgba(255, 255, 255, 0.5);\r\n\t}\r\n\t/* 纵向虚线 */\r\n\r\n\t.uni-cropper-dashed-v {\r\n\t\tposition: absolute;\r\n\t\tleft: 33.33333333%;\r\n\t\ttop: 0;\r\n\t\twidth: 33.33333333%;\r\n\t\theight: 100%;\r\n\t\tborder-left: 1rpx dashed rgba(255, 255, 255, 0.5);\r\n\t\tborder-right: 1rpx dashed rgba(255, 255, 255, 0.5);\r\n\t}\r\n\t/* 四个方向的线  为了之后的拖动事件*/\r\n\r\n\t.uni-cropper-line-t {\r\n\t\tposition: absolute;\r\n\t\tdisplay: block;\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #69f;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\theight: 1rpx;\r\n\t\topacity: 0.1;\r\n\t\tcursor: n-resize;\r\n\t}\r\n\r\n\t.uni-cropper-line-t::before {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 50%;\r\n\t\tright: 0rpx;\r\n\t\twidth: 100%;\r\n\t\t-webkit-transform: translate3d(0, -50%, 0);\r\n\t\ttransform: translate3d(0, -50%, 0);\r\n\t\tbottom: 0;\r\n\t\theight: 41rpx;\r\n\t\tbackground: transparent;\r\n\t\tz-index: 11;\r\n\t}\r\n\r\n\t.uni-cropper-line-r {\r\n\t\tposition: absolute;\r\n\t\tdisplay: block;\r\n\t\tbackground-color: #69f;\r\n\t\ttop: 0;\r\n\t\tright: 0rpx;\r\n\t\twidth: 1rpx;\r\n\t\topacity: 0.1;\r\n\t\theight: 100%;\r\n\t\tcursor: e-resize;\r\n\t}\r\n\r\n\t.uni-cropper-line-r::before {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 50%;\r\n\t\twidth: 41rpx;\r\n\t\t-webkit-transform: translate3d(-50%, 0, 0);\r\n\t\ttransform: translate3d(-50%, 0, 0);\r\n\t\tbottom: 0;\r\n\t\theight: 100%;\r\n\t\tbackground: transparent;\r\n\t\tz-index: 11;\r\n\t}\r\n\r\n\t.uni-cropper-line-b {\r\n\t\tposition: absolute;\r\n\t\tdisplay: block;\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #69f;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\theight: 1rpx;\r\n\t\topacity: 0.1;\r\n\t\tcursor: s-resize;\r\n\t}\r\n\r\n\t.uni-cropper-line-b::before {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 50%;\r\n\t\tright: 0rpx;\r\n\t\twidth: 100%;\r\n\t\t-webkit-transform: translate3d(0, -50%, 0);\r\n\t\ttransform: translate3d(0, -50%, 0);\r\n\t\tbottom: 0;\r\n\t\theight: 41rpx;\r\n\t\tbackground: transparent;\r\n\t\tz-index: 11;\r\n\t}\r\n\r\n\t.uni-cropper-line-l {\r\n\t\tposition: absolute;\r\n\t\tdisplay: block;\r\n\t\tbackground-color: #69f;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 1rpx;\r\n\t\topacity: 0.1;\r\n\t\theight: 100%;\r\n\t\tcursor: w-resize;\r\n\t}\r\n\r\n\t.uni-cropper-line-l::before {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 50%;\r\n\t\twidth: 41rpx;\r\n\t\t-webkit-transform: translate3d(-50%, 0, 0);\r\n\t\ttransform: translate3d(-50%, 0, 0);\r\n\t\tbottom: 0;\r\n\t\theight: 100%;\r\n\t\tbackground: transparent;\r\n\t\tz-index: 11;\r\n\t}\r\n\r\n\t.uni-cropper-point {\r\n\t\twidth: 5rpx;\r\n\t\theight: 5rpx;\r\n\t\tbackground-color: #69f;\r\n\t\topacity: .75;\r\n\t\tposition: absolute;\r\n\t\tz-index: 3;\r\n\t}\r\n\r\n\t.point-t {\r\n\t\ttop: -3rpx;\r\n\t\tleft: 50%;\r\n\t\tmargin-left: -3rpx;\r\n\t\tcursor: n-resize;\r\n\t}\r\n\r\n\t.point-tr {\r\n\t\ttop: -3rpx;\r\n\t\tleft: 100%;\r\n\t\tmargin-left: -3rpx;\r\n\t\tcursor: n-resize;\r\n\t}\r\n\r\n\t.point-r {\r\n\t\ttop: 50%;\r\n\t\tleft: 100%;\r\n\t\tmargin-left: -3rpx;\r\n\t\tmargin-top: -3rpx;\r\n\t\tcursor: n-resize;\r\n\t}\r\n\r\n\t.point-rb {\r\n\t\tleft: 100%;\r\n\t\ttop: 100%;\r\n\t\t-webkit-transform: translate3d(-50%, -50%, 0);\r\n\t\ttransform: translate3d(-50%, -50%, 0);\r\n\t\tcursor: n-resize;\r\n\t\twidth: 36rpx;\r\n\t\theight: 36rpx;\r\n\t\tbackground-color: #69f;\r\n\t\tposition: absolute;\r\n\t\tz-index: 1112;\r\n\t\topacity: 1;\r\n\t}\r\n\r\n\t.point-b {\r\n\t\tleft: 50%;\r\n\t\ttop: 100%;\r\n\t\tmargin-left: -3rpx;\r\n\t\tmargin-top: -3rpx;\r\n\t\tcursor: n-resize;\r\n\t}\r\n\r\n\t.point-bl {\r\n\t\tleft: 0%;\r\n\t\ttop: 100%;\r\n\t\tmargin-left: -3rpx;\r\n\t\tmargin-top: -3rpx;\r\n\t\tcursor: n-resize;\r\n\t}\r\n\r\n\t.point-l {\r\n\t\tleft: 0%;\r\n\t\ttop: 50%;\r\n\t\tmargin-left: -3rpx;\r\n\t\tmargin-top: -3rpx;\r\n\t\tcursor: n-resize;\r\n\t}\r\n\r\n\t.point-lt {\r\n\t\tleft: 0%;\r\n\t\ttop: 0%;\r\n\t\tmargin-left: -3rpx;\r\n\t\tmargin-top: -3rpx;\r\n\t\tcursor: n-resize;\r\n\t}\r\n\t/* 裁剪框预览内容 */\r\n\r\n\t.uni-cropper-viewer {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.uni-cropper-viewer image {\r\n\t\tposition: absolute;\r\n\t\tz-index: 2;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752425857662\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}