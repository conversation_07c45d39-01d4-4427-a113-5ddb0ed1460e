<template>
	<view class="order-detail-container">
		<!-- 维修记录按钮 -->
		<view class="repair-button-container" v-if="isRepairman">
			<button class="repair-btn" @click="showMaintenanceForm">
				添加维修记录
			</button>
		</view>
		
		<!-- 工单基本信息卡片 -->
		<view class="detail-card">
			<view class="card-header">
				<text class="card-title">工单信息</text>
				<text class="order-status" :class="'status-' + (orderDetail && orderDetail.status)">
					{{ getStatusText(orderDetail && orderDetail.status) }}
				</text>
			</view>
			
			<view class="info-list">
				<view class="info-item">
					<text class="label">工单编号</text>
					<text class="value">{{ orderDetail && orderDetail.workOrderNo }}</text>
				</view>
				<view class="info-item">
					<text class="label">产品名称</text>
					<text class="value">{{ orderDetail && orderDetail.productName }}</text>
				</view>
				<view class="info-item">
					<text class="label">下单客户</text>
					<text class="value">{{ orderDetail && orderDetail.customerName }}</text>
				</view>
				<view class="info-item">
					<text class="label">型号</text>
					<text class="value">{{ orderDetail && orderDetail.modelType }}</text>
				</view>
				<view class="info-item">
					<text class="label">产品编号版本</text>
					<text class="value">{{ orderDetail && orderDetail.orderProductNumberVersion }}</text>
				</view>
				<view class="info-item">
					<text class="label">计划数量</text>
					<text class="value">{{ orderDetail && orderDetail.totalPlannedQuantity }}</text>
				</view>
				<view class="info-item">
					<text class="label">计划开始时间</text>
					<text class="value">{{ orderDetail && orderDetail.overallPlannedStartTime }}</text>
				</view>
				<view class="info-item">
					<text class="label">计划结束时间</text>
					<text class="value">{{ orderDetail && orderDetail.overallPlannedEndTime }}</text>
				</view>
			</view>
		</view>
		
		<!-- 装箱单图片卡片 -->
		<view class="detail-card" v-if="orderDetail && orderDetail.packingListImagePath">
			<view class="card-header">
				<text class="card-title">装箱单图片</text>
			</view>
			<view class="image-list">
				<image
					v-for="(image, imageIndex) in imageList"
					:key="imageIndex"
					:src="image"
					mode="aspectFill"
					class="image-item"
					@click="previewImage(imageIndex)"
				></image>
			</view>
		</view>
		
		<!-- 任务列表卡片 -->
		<view class="detail-card">
			<view class="card-header">
				<text class="card-title">任务列表</text>
			</view>
			<view class="task-list">
				<view class="task-item" v-for="(task, taskIndex) in orderItemLists" :key="taskIndex">
					<view class="task-main" @click="toggleTask(task)">
						<view class="task-header">
							<view class="task-header-left">
								<u-icon 
									:name="expandedTasks[task.workOrderItemId] ? 'arrow-up' : 'arrow-down'" 
									size="28" 
									color="#2c3e50"
									class="expand-icon"
								></u-icon>
								<text class="task-name">{{ task.taskName }}</text>
							</view>
							<text class="task-status" :class="'status-' + task.itemStatus">
								{{ getTaskStatusText(task.itemStatus) }}
							</text>
						</view>
						<view class="task-info">
							<view class="info-row">
								<text class="info-label">计划数量：</text>
								<text class="info-value">{{ task.plannedQuantity }}</text>
							</view>
							<template v-if="!isMaintenanceTask(task.taskName)">
								<view class="info-row">
									<text class="info-label">良品数：</text>
									<text class="info-value">{{ task.itemGoodQuantity }}</text>
								</view>
								<view class="info-row">
									<text class="info-label">不良品数：</text>
									<text class="info-value">{{ task.itemDefectiveQuantity }}</text>
								</view>
							</template>
						</view>
					</view>
					
					<!-- 指派信息（折叠部分） -->
					<view class="task-details" v-show="expandedTasks[task.workOrderItemId]">
						<view class="assignment-info">
							<view class="assignment-title">指派情况</view>
							<view class="assignment-list">
								<view 
									class="assignment-item" 
									v-for="(assignment, assignmentIndex) in (task.assignments || [])" 
									:key="assignmentIndex"
									@tap="() => handleAssignmentClick(assignment, task)"
								>
									<view class="assignment-header">
										<text class="assignee-name">{{ assignment.nickName || '未知用户' }}</text>
										<view class="assignment-actions">
											<text class="assignment-status" :class="'status-' + (assignment.assignmentStatus || '1')">
												{{ getAssignmentStatusText(assignment.assignmentStatus || '1') }}
											</text>
											<view 
												v-if="canDeleteAssignment"
												class="delete-btn" 
												:data-assignment-id="assignment.assignmentId"
												:data-task-id="task.workOrderItemId"
												@tap.stop="handleDeleteAssignment"
											>
												<u-icon name="trash" size="32" color="#909399"></u-icon>
											</view>
										</view>
									</view>
									<view class="assignment-details">
										<view class="detail-row">
											<text class="detail-label">指派数量：</text>
											<text class="detail-value">{{ assignment.assignedQuantity || 0 }}</text>
										</view>
										<!-- 发货任务显示地址 -->
										<view class="detail-row" v-if="isShippingTask(task.taskName)">
											<text class="detail-label">地址：</text>
											<text class="detail-value">{{ assignment.locationAddress || '未填写' }}</text>
										</view>
										<!-- 非维修任务且非发货任务显示良品数和不良品数 -->
										<template v-if="!isMaintenanceTask(task.taskName) && !isShippingTask(task.taskName)">
											<view class="detail-row">
												<text class="detail-label">良品数：</text>
												<text class="detail-value">{{ assignment.reportedGoodQuantity || 0 }}</text>
											</view>
											<view class="detail-row">
												<text class="detail-label">不良品数：</text>
												<text class="detail-value">{{ assignment.reportedDefectiveQuantity || 0 }}</text>
											</view>
										</template>
										<!-- 维修任务显示说明 -->
										<template v-if="isMaintenanceTask(task.taskName)">
											<!-- <view class="detail-row" v-if="assignment.pic">
												<text class="detail-label">图片：</text>
												<view class="image-list">
													<image
														v-for="(image, imageIndex) in getAssignmentImages(assignment.pic)"
														:key="imageIndex"
														:src="image"
														mode="aspectFill"
														class="assignment-image"
														@click="previewAssignmentImageFromList(assignment.pic, imageIndex)"
													></image>
												</view>
											</view> -->
											<view class="detail-row" v-if="assignment.description">
												<text class="detail-label">说明：</text>
												<text class="detail-value description-text">{{ assignment.description }}</text>
											</view>
										</template>
									</view>
								</view>
								<view class="no-assignment" v-if="!task.assignments || task.assignments.length === 0">
									<text>暂无指派信息</text>
								</view>
							</view>
						</view>
						
						<view class="task-footer" v-if="task.itemStatus === '0' && canAssignTask">
							<button 
								class="assign-btn"
								@click.stop="showUserPicker(task)"
							>指派</button>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 用户选择弹窗 -->
		<u-popup
			mode="bottom"
			:show="showUserPickerFlag"
			@close="closeUserPicker"
			border-radius="24"
			safe-area-inset-bottom
		>
			<view class="user-picker-container">
				<view class="user-picker-header">
					<text class="user-picker-title">选择执行人</text>
					<view class="user-picker-actions">
						<u-button plain size="mini" @click="closeUserPicker" style="margin-right: 20rpx;">取消</u-button>
						<u-button type="primary" size="mini" @click="confirmUser">确认</u-button>
					</view>
				</view>
				<view class="user-list">
					<view
						class="user-item"
						v-for="(user, userIndex) in userList"
						:key="userIndex"
						@click="selectUser(user)"
						:class="{'user-item-selected': selectedUser && selectedUser.userId === user.userId}"
					>
						<text class="user-name">{{ user.nickName }}</text>
						<u-icon
							:name="selectedUser && selectedUser.userId === user.userId ? 'checkmark-circle' : ''"
							:color="selectedUser && selectedUser.userId === user.userId ? '#1e3a8a' : '#8c9fba'"
							size="40"
						></u-icon>
					</view>
				</view>
				
				<!-- 计划数输入 -->
				<view class="quantity-input" v-if="selectedUser">
					<view class="input-label">计划数量</view>
					<view class="input-wrapper">
						<input 
							type="number" 
							v-model="assignQuantity"
							class="quantity-input-field"
							placeholder="请输入计划数量"
						/>
						<text class="quantity-tip">最大可分配数量：{{ currentTask ? currentTask.plannedQuantity : 0 }}</text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 指派详情弹窗 -->
		<u-popup
			mode="bottom"
			:show="showAssignmentDetailFlag"
			@close="closeAssignmentDetail"
			border-radius="24"
			safe-area-inset-bottom
		>
			<view class="assignment-detail-container">
				<view class="detail-header">
					<text class="detail-title">指派详情</text>
					<view class="detail-actions">
						<u-button plain size="mini" @click="closeAssignmentDetail">关闭</u-button>
					</view>
				</view>
				
				<scroll-view class="detail-content" scroll-y>
					<view class="detail-item">
						<text class="detail-label">执行人</text>
						<text class="detail-value">{{ currentAssignment.nickName || '未知用户' }}</text>
					</view>
					
					<!-- 发货任务显示地址 -->
					<view class="detail-item" v-if="isShippingAssignment">
						<text class="detail-label">地址</text>
						<text class="detail-value">{{ currentAssignment.locationAddress || '未填写' }}</text>
					</view>
					
					<!-- 非维修任务且非发货任务显示良品数和不良品数 -->
					<template v-if="!isMaintenanceAssignment && !isShippingAssignment">
						<view class="detail-item">
							<text class="detail-label">良品数</text>
							<text class="detail-value">{{ currentAssignment.goodQuantity || 0 }}</text>
						</view>
						
						<view class="detail-item">
							<text class="detail-label">不良品数</text>
							<text class="detail-value">{{ currentAssignment.defectiveQuantity || 0 }}</text>
						</view>
					</template>
					
					<view class="detail-item" v-if="currentAssignment.pic">
						<text class="detail-label">{{ isMaintenanceAssignment ? '维修图片' : '图片' }}</text>
						<view class="image-list">
							<image
								v-for="(image, detailImageIndex) in assignmentImageList"
								:key="detailImageIndex"
								:src="image"
								mode="aspectFill"
								class="image-item"
								@click="previewAssignmentImage(detailImageIndex)"
							></image>
						</view>
					</view>
					
					<view class="detail-item" v-if="isMaintenanceAssignment && currentAssignment.repairGoodNo">
						<text class="detail-label">维修产品编号</text>
						<text class="detail-value description-text">{{ currentAssignment.repairGoodNo }}</text>
					</view>
					
					<view class="detail-item" v-if="isMaintenanceAssignment && currentAssignment.createTime">
						<text class="detail-label">维修时间</text>
						<text class="detail-value">{{ currentAssignment.createTime }}</text>
					</view>
					
					<view class="detail-item" v-if="isMaintenanceAssignment && currentAssignment.description">
						<text class="detail-label">维修说明</text>
						<text class="detail-value description-text">{{ currentAssignment.description }}</text>
					</view>
				</scroll-view>
			</view>
		</u-popup>

		<!-- 维修记录表单弹窗 -->
		<u-popup
			mode="bottom"
			:show="showMaintenanceFormFlag"
			@close="closeMaintenanceForm"
			border-radius="24"
			safe-area-inset-bottom
		>
			<view class="maintenance-form-container">
				<view class="form-header">
					<text class="form-title">添加维修记录</text>
					<view class="form-actions">
						<u-button plain size="mini" @click="closeMaintenanceForm" style="margin-right: 20rpx;">取消</u-button>
						<u-button type="primary" size="mini" @click="submitMaintenanceRecord" :disabled="isUploading">
							{{ isUploading ? '上传中...' : '确认' }}
						</u-button>
					</view>
				</view>
				
				<scroll-view class="form-content" scroll-y>
					<view class="form-item">
						<text class="form-label">维修图片 *</text>
						<view class="upload-container">
							<u-upload
								:fileList="maintenanceFileList"
								@afterRead="afterMaintenanceRead"
								@delete="deleteMaintenancePic"
								:maxCount="9"
								:maxSize="5242880"
								width="200"
								height="200"
								:previewFullImage="true"
								:disabled="isUploading"
							></u-upload>
							
							<!-- 上传进度指示器 -->
							<view class="upload-progress" v-if="isUploading">
								<view class="progress-bar">
									<view class="progress-fill" :style="{ width: uploadProgress + '%' }"></view>
								</view>
								<text class="progress-text">上传中 {{ uploadProgress }}%</text>
							</view>
						</view>
					</view>
					
					<view class="form-item">
						<text class="form-label">维修产品编号</text>
						<textarea 
							v-model="maintenanceFormData.repairGoodNo"
							class="form-textarea"
							placeholder="请输入维修产品编号"
							:auto-height="true"
							:maxlength="500"
						></textarea>
						<view class="char-count">
							<text>{{ (maintenanceFormData.repairGoodNo || '').length }}/500</text>
						</view>
					</view>
					
					<view class="form-item">
						<text class="form-label">维修说明 *</text>
						<textarea 
							v-model="maintenanceFormData.description"
							class="form-textarea"
							placeholder="请输入维修说明"
							:auto-height="true"
							:maxlength="500"
						></textarea>
					</view>
				</scroll-view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import { getOrders,updateOrders } from "@/api/work/orders.js"
	import { listOrderItems,updateOrderItems,addOrderItems } from "@/api/work/orderItems.js"
	import { baseUrl } from "../../config"
	import { listUser } from "@/api/system/user.js"
	import { addTaskAssignments,listTaskAssignments,delTaskAssignments } from "@/api/work/taskAssignments.js"
	export default {
		data() {
			return {
				orderDetail:null,
				orderItemLists:[],
				baseUrl,
				userList: [],
				showUserPickerFlag: false,
				selectedUser: null,
				currentTask: null,
				assignQuantity: '',
				taskAssignmentsParams:{
					assignedToUserId:'',
					assignedQuantity:'',
					workOrderItemId:''
				},
				expandedTasks: {}, // 用于存储任务的展开状态
				showAssignmentDetailFlag: false,
				currentAssignment: null,
				userId:this.$store.state.user.userId,
				roles:this.$store.state.user.roles,
				showMaintenanceFormFlag: false,
				maintenanceFileList: [],
				maintenanceFormData: {
					pic: '',
					description: '',
					repairGoodNo: ''
				},
				isUploading: false,
				uploadProgress: 0
			}
		},
		computed: {
			imageList() {
				if (!this.orderDetail || !this.orderDetail.packingListImagePath) return [];
				return this.orderDetail.packingListImagePath.split(',').map(path => this.baseUrl + path);
			},
			assignmentImageList() {
				if (!this.currentAssignment || !this.currentAssignment.pic) return [];
				return this.currentAssignment.pic.split(',').map(path => this.baseUrl + path);
			},
			isRepairman() {
				return this.roles.includes('repairman');
			},
			maintenanceTask() {
				return this.orderItemLists.find(task => task.taskName && task.taskName.includes('维修'));
			},
			isMaintenanceAssignment() {
				return this.currentAssignment && this.currentAssignment.taskName && this.currentAssignment.taskName.includes('维修');
			},
			isShippingAssignment() {
				return this.currentAssignment && this.currentAssignment.taskName && this.currentAssignment.taskName.includes('发货');
			},
			canDeleteAssignment() {
				return this.roles.includes('admin') || this.roles.includes('dispatcher');
			},
			canAssignTask() {
				return this.roles.includes('admin') || this.roles.includes('dispatcher');
			}
		},
		onLoad({id}) {
			this.getDetail(id)
		},
		methods: {
			getDetail(id){
				getOrders(id).then(async res=>{
					this.orderDetail = res.data
					
					const itemRes = await listOrderItems({pageSize:100000,workOrderId:id})
					// 确保每个任务都有assignments数组
					this.orderItemLists = itemRes.rows.map(task => ({
						...task,
						assignments: []
					}))
					
					// 查询每个任务的指派情况
					for(let task of this.orderItemLists) {
						const assignRes = await listTaskAssignments({
							workOrderItemId: task.workOrderItemId
						})
						task.assignments = assignRes.rows || []
						console.log('任务指派信息：', task.workOrderItemId, assignRes.rows)
					}
					
					console.log('工单详情：', this.orderDetail)
					console.log('任务列表：', this.orderItemLists)
				}).catch(error => {
					console.error('获取工单详情失败：', error)
					uni.showToast({
						title: '获取工单详情失败',
						icon: 'none'
					})
				})
			},
			// 获取工单状态文本
			getStatusText(status) {
				const statusMap = {
					'0': '未开始',
					'1': '处理中',
					'2': '已完成'
				}
				return statusMap[status] || '未知状态'
			},
			// 获取任务状态文本
			getTaskStatusText(status) {
				const statusMap = {
					'0': '未开始',
					'1': '处理中',
					'2': '已完成'
				}
				return statusMap[status] || '未知状态'
			},
			// 获取指派任务状态文本
			getAssignmentStatusText(status) {
				const statusMap = {
					'1': '处理中',
					'2': '已完成'
				}
				return statusMap[status] || '未知状态'
			},
			// 预览图片
			previewImage(imageIndex) {
				uni.previewImage({
					urls: this.imageList,
					current: imageIndex
				})
			},
			// 显示用户选择器
			async showUserPicker(task) {
				this.currentTask = task;
				this.selectedUser = null;
				this.assignQuantity = task.plannedQuantity || '';
				try {
					const res = await listUser();
					if (res && res.rows) {
						this.userList = res.rows;
						this.showUserPickerFlag = true;
					}
				} catch (error) {
					console.error('获取用户列表失败', error);
					uni.showToast({
						title: '获取用户列表失败',
						icon: 'none'
					});
				}
			},
			
			// 选择用户
			selectUser(user) {
				this.selectedUser = user;
			},
			
			// 确认选择用户
			async confirmUser() {
				if (!this.selectedUser) {
					uni.showToast({
						title: '请选择执行人',
						icon: 'none'
					});
					return;
				}
				
				if (!this.assignQuantity) {
					uni.showToast({
						title: '请输入计划数量',
						icon: 'none'
					});
					return;
				}
				
				const quantity = Number(this.assignQuantity);
				if (isNaN(quantity) || quantity <= 0) {
					uni.showToast({
						title: '请输入有效的计划数量',
						icon: 'none'
					});
					return;
				}
				
				if (quantity > this.currentTask.plannedQuantity) {
					uni.showToast({
						title: '计划数量不能大于任务计划数',
						icon: 'none'
					});
					return;
				}
				
				try {
					// 设置指派参数
					this.taskAssignmentsParams = {
						assignedToUserId: this.selectedUser.userId,
						assignedQuantity: quantity,
						workOrderItemId: this.currentTask.workOrderItemId
					}
					
					// 调用指派接口
					await addTaskAssignments(this.taskAssignmentsParams)
					
					// 如果工单状态为未开始，更新工单状态为处理中
					if (this.orderDetail.status === '0') {
						await updateOrders({
							workOrderId: this.orderDetail.workOrderId,
							status: '1'
						})
					}
					
					// 更新任务状态为处理中
					await updateOrderItems({
						workOrderItemId: this.currentTask.workOrderItemId,
						itemStatus: '1'
					})
					
					uni.showToast({
						title: '指派成功',
						icon: 'success'
					})
					
					// 关闭弹窗
					this.closeUserPicker()
					
					// 刷新页面数据
					this.getDetail(this.orderDetail.workOrderId)
					
				} catch (error) {
					console.error('指派失败', error)
					uni.showToast({
						title: '指派失败',
						icon: 'none'
					})
				}
			},
			
			// 关闭用户选择器
			closeUserPicker() {
				this.showUserPickerFlag = false;
				this.selectedUser = null;
				this.currentTask = null;
				this.assignQuantity = '';
			},
			// 切换任务展开状态
			toggleTask(task) {
				this.$set(this.expandedTasks, task.workOrderItemId, !this.expandedTasks[task.workOrderItemId])
			},
			// 处理指派点击
			handleAssignmentClick(assignment, task) {
				if (!assignment) return;
				
				try {
									// 确保assignment对象包含所需的所有属性
				this.currentAssignment = {
					nickName: assignment.nickName || '未知用户',
					goodQuantity: assignment.reportedGoodQuantity || 0,
					defectiveQuantity: assignment.reportedDefectiveQuantity || 0,
					pic: assignment.pic || '',
					description: assignment.description || '',
					assignmentStatus: assignment.assignmentStatus || '1',
					taskName: task ? task.taskName : '',
					locationAddress: assignment.locationAddress || '',
					repairGoodNo: assignment.repairGoodNo || '',
					createTime: assignment.createTime || ''
				};
					
					this.showAssignmentDetailFlag = true;
				} catch (error) {
					console.error('处理指派点击失败：', error);
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					});
				}
			},
			// 显示指派详情
			showAssignmentDetail(assignment) {
				if (!assignment) return;
				this.currentAssignment = assignment;
				this.showAssignmentDetailFlag = true;
			},
			// 关闭指派详情
			closeAssignmentDetail() {
				this.showAssignmentDetailFlag = false;
				this.currentAssignment = null;
			},
			// 预览指派图片
			previewAssignmentImage(detailImageIndex) {
				if (!this.assignmentImageList || !this.assignmentImageList.length) return;
				uni.previewImage({
					urls: this.assignmentImageList,
					current: detailImageIndex
				})
			},
			// 处理删除指派
			async handleDeleteAssignment(event) {
				const assignmentId = event.currentTarget.dataset.assignmentId;
				const taskId = event.currentTarget.dataset.taskId;
				if (!assignmentId || !taskId) {
					uni.showToast({
						title: '无法获取指派信息',
						icon: 'none'
					});
					return;
				}
				
				try {
					uni.showModal({
						title: '提示',
						content: '确定要删除这条指派记录吗？',
						success: async (res) => {
							if (res.confirm) {
								await delTaskAssignments(assignmentId);
								
								// 检查任务是否还有其他指派
								const assignRes = await listTaskAssignments({
									workOrderItemId: taskId
								});
								
								// 如果没有指派了，将任务状态改为未开始
								if (!assignRes || !assignRes.rows || assignRes.rows.length === 0) {
									await updateOrderItems({
										workOrderItemId: taskId,
										itemStatus: '0' // 未开始
									});
								}
								
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								});
								
								// 刷新页面数据
								this.getDetail(this.orderDetail.workOrderId);
							}
						}
					});
				} catch (error) {
					console.error('删除指派失败：', error);
					uni.showToast({
						title: '删除失败',
						icon: 'none'
					});
				}
			},
			// 显示维修记录表单
			showMaintenanceForm() {
				this.showMaintenanceFormFlag = true;
			},
			// 关闭维修记录表单
			closeMaintenanceForm() {
				this.showMaintenanceFormFlag = false;
				this.maintenanceFileList = [];
				this.maintenanceFormData = {
					pic: '',
					description: '',
					repairGoodNo: ''
				};
				// 重置上传状态
				this.isUploading = false;
				this.uploadProgress = 0;
			},
			// 处理维修图片上传
			async afterMaintenanceRead(event) {
				console.log('维修图片上传事件：', event);
				
				// 设置上传状态
				this.isUploading = true;
				this.uploadProgress = 0;
				
				// 显示上传中提示
				uni.showLoading({
					title: '图片上传中...',
					mask: true
				});
				
				try {
					const file = event.file;
					
					// 模拟上传进度
					const progressTimer = setInterval(() => {
						if (this.uploadProgress < 90) {
							this.uploadProgress += 10;
						}
					}, 100);
					
					const [uploadErr, uploadRes] = await uni.uploadFile({
						url: this.baseUrl + '/common/upload',
						filePath: file.url,
						name: 'file'
					});
					
					// 清除进度定时器
					clearInterval(progressTimer);
					this.uploadProgress = 100;
					
					if (uploadErr) {
						throw new Error('上传图片失败');
					}
					
					const result = JSON.parse(uploadRes.data);
					if (result.code === 200) {
						// 更新图片路径，用逗号分隔多张图片
						const newPic = result.fileName;
						if (this.maintenanceFormData.pic) {
							this.maintenanceFormData.pic = this.maintenanceFormData.pic + ',' + newPic;
						} else {
							this.maintenanceFormData.pic = newPic;
						}
						
						// 更新文件列表显示
						this.maintenanceFileList = this.maintenanceFormData.pic.split(',').filter(url => url).map(url => ({
							url: this.baseUrl + url,
							status: 'success',
							message: '上传成功'
						}));
						
						// 延迟一下再隐藏加载
						setTimeout(() => {
							uni.hideLoading();
							uni.showToast({
								title: '上传成功',
								icon: 'success',
								duration: 1500
							});
							this.isUploading = false;
							this.uploadProgress = 0;
						}, 300);
						
					} else {
						throw new Error(result.msg || '上传失败');
					}
					
				} catch (error) {
					console.error('上传维修图片失败：', error);
					
					// 重置状态
					this.isUploading = false;
					this.uploadProgress = 0;
					
					uni.hideLoading();
					uni.showToast({
						title: error.message || '上传图片失败',
						icon: 'error',
						duration: 2000
					});
				}
			},
			// 删除维修图片
			deleteMaintenancePic(event) {
				console.log('删除维修图片：', event);
				const pics = this.maintenanceFormData.pic.split(',').filter(url => url);
				pics.splice(event.index, 1);
				this.maintenanceFormData.pic = pics.join(',');
				
				// 更新文件列表显示
				this.maintenanceFileList = pics.map(url => ({
					url: this.baseUrl + url,
					status: 'success',
					message: '上传成功'
				}));
			},
			// 提交维修记录
			async submitMaintenanceRecord() {
				// 表单验证
				if (!this.maintenanceFormData.pic) {
					uni.showToast({
						title: '请上传维修图片',
						icon: 'none'
					});
					return;
				}
				
				if (!this.maintenanceFormData.description) {
					uni.showToast({
						title: '请填写维修说明',
						icon: 'none'
					});
					return;
				}
				
				try {
					// 首先尝试从现有任务列表中查找维修任务
					let maintenanceTaskItem = this.orderItemLists.find(task => task.productionTaskId === 3);
					
					// 如果没有维修任务，先创建一个
					if (!maintenanceTaskItem) {
						const createTaskParams = {
							workOrderId: this.orderDetail.workOrderId,
							productionTaskId: 3, // 维修任务ID
							plannedQuantity: this.orderDetail.totalPlannedQuantity,
							itemStatus: '2', // 已完成状态
							itemGoodQuantity: null,
							itemDefectiveQuantity: null
						};
						
						console.log('创建维修任务参数：', createTaskParams);
						const createResult = await addOrderItems(createTaskParams);
						console.log('创建维修任务结果：', createResult);
						
						if (createResult && createResult.code === 200) {
							// 显示加载动画
							uni.showLoading({
								title: '正在创建维修任务...',
								mask: true
							});
							
							// 等待一下，确保数据库事务完成
							await new Promise(resolve => setTimeout(resolve, 500));
							
							// 重新获取工单详情以获取新创建的维修任务
							await this.getDetail(this.orderDetail.workOrderId);
							console.log('重新获取后的任务列表：', this.orderItemLists);
							console.log('查找维修任务，productionTaskId=3');
							
							// 再次根据productionTaskId=3查找维修任务
							maintenanceTaskItem = this.orderItemLists.find(task => {
								console.log('任务：', task.taskName, 'productionTaskId：', task.productionTaskId);
								return task.productionTaskId === 3;
							});
							console.log('找到的维修任务：', maintenanceTaskItem);
							
							// 如果还是找不到，再试一次
							if (!maintenanceTaskItem) {
								console.log('第一次查找失败，等待1秒后重试...');
								uni.showLoading({
									title: '正在重试获取任务...',
									mask: true
								});
								await new Promise(resolve => setTimeout(resolve, 1000));
								await this.getDetail(this.orderDetail.workOrderId);
								console.log('重试后的任务列表：', this.orderItemLists);
								maintenanceTaskItem = this.orderItemLists.find(task => task.productionTaskId === 3);
								console.log('重试后找到的维修任务：', maintenanceTaskItem);
							}
							
							// 隐藏加载动画
							uni.hideLoading();
						} else {
							uni.showToast({
								title: '创建维修任务失败',
								icon: 'none'
							});
							return;
						}
					}
					
					// 最终检查是否成功获取到维修任务
					if (!maintenanceTaskItem) {
						uni.showToast({
							title: '无法获取维修任务信息',
							icon: 'none'
						});
						return;
					}
					
					// 准备提交参数
					const submitParams = {
						assignedToUserId: this.userId,
						workOrderItemId: maintenanceTaskItem.workOrderItemId,
						assignedQuantity: 1, // 维修任务默认数量为1
						pic: this.maintenanceFormData.pic,
						description: this.maintenanceFormData.description,
						repairGoodNo: this.maintenanceFormData.repairGoodNo
					};
					
					console.log('提交维修记录参数：', submitParams);
					
					// 调用addTaskAssignments方法
					await addTaskAssignments(submitParams);
					
					uni.showToast({
						title: '维修记录添加成功',
						icon: 'success'
					});
					
					// 关闭弹窗
					this.closeMaintenanceForm();
					
					// 刷新页面数据
					this.getDetail(this.orderDetail.workOrderId);
					
				} catch (error) {
					console.error('提交维修记录失败：', error);
					uni.showToast({
						title: '提交失败',
						icon: 'none'
					});
				}
			},
			// 判断是否为维修任务
			isMaintenanceTask(taskName) {
				return taskName && taskName.includes('维修');
			},
			// 获取指派图片
			getAssignmentImages(pic) {
				if (!pic) return [];
				return pic.split(',').map(path => this.baseUrl + path);
			},
			// 预览指派图片
			previewAssignmentImageFromList(pic, imageIndex) {
				uni.previewImage({
					urls: this.getAssignmentImages(pic),
					current: imageIndex
				})
			},
			// 判断是否为发货任务
			isShippingTask(taskName) {
				return taskName && taskName.includes('发货');
			}
		}
	}
</script>

<style lang="scss">
.order-detail-container {
	padding: 30rpx;
	background-color: #f5f7fa;
	min-height: 100vh;
	
	.detail-card {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		
		.card-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 30rpx;
			padding-bottom: 20rpx;
			border-bottom: 1px solid #ebeef5;
			
			.card-title {
				font-size: 32rpx;
				font-weight: 500;
				color: #2c3e50;
			}
			
			.order-status {
				font-size: 28rpx;
				padding: 6rpx 20rpx;
				border-radius: 6rpx;
				
				&.status-0 {
					background-color: #f4f4f5;
					color: #909399;
				}
				
				&.status-1 {
					background-color: #ecf5ff;
					color: #409eff;
				}
				
				&.status-2 {
					background-color: #f0f9eb;
					color: #67c23a;
				}
			}
		}
		
		.info-list {
			.info-item {
				display: flex;
				margin-bottom: 20rpx;
				
				.label {
					width: 200rpx;
					color: #606266;
					font-size: 28rpx;
				}
				
				.value {
					flex: 1;
					color: #2c3e50;
					font-size: 28rpx;
				}
			}
		}
		
		.image-list {
			display: flex;
			flex-wrap: wrap;
			margin: 0 -10rpx;
			
			.image-item {
				width: calc(33.33% - 20rpx);
				height: 200rpx;
				margin: 10rpx;
				border-radius: 8rpx;
				background-color: #f8f9fa;
			}
		}
		
		.task-list {
			.task-item {
				background-color: #f8f9fa;
				border-radius: 12rpx;
				margin-bottom: 20rpx;
				overflow: hidden;
				
				&:last-child {
					margin-bottom: 0;
				}
				
				.task-main {
					padding: 24rpx;
					cursor: pointer;
					background-color: #f8f9fa;
					border-radius: 12rpx;
					
					&:active {
						background-color: #f0f2f5;
					}
					
					.task-header {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 20rpx;
						
						.task-header-left {
							display: flex;
							align-items: center;
							
							.expand-icon {
								margin-right: 12rpx;
								transition: transform 0.3s;
							}
							
							.task-name {
								font-size: 32rpx;
								font-weight: 600;
								color: #1e3a8a;
							}
						}
						
						.task-status {
							font-size: 24rpx;
							padding: 6rpx 20rpx;
							border-radius: 6rpx;
							font-weight: 500;
							
							&.status-0 {
								background-color: #f4f4f5;
								color: #909399;
							}
							
							&.status-1 {
								background-color: #ecf5ff;
								color: #409eff;
							}
							
							&.status-2 {
								background-color: #f0f9eb;
								color: #67c23a;
							}
						}
					}
					
					.task-info {
						.info-row {
							display: flex;
							margin-bottom: 12rpx;
							
							&:last-child {
								margin-bottom: 0;
							}
							
							.info-label {
								width: 140rpx;
								color: #606266;
								font-size: 26rpx;
							}
							
							.info-value {
								flex: 1;
								color: #2c3e50;
								font-size: 26rpx;
								font-weight: 500;
							}
						}
					}
				}
				
				.task-details {
					padding: 20rpx 24rpx;
					background-color: #ffffff;
					border-top: 1px solid #ebeef5;
					animation: slideDown 0.3s ease-out;
					
					@keyframes slideDown {
						from {
							opacity: 0;
							transform: translateY(-10px);
						}
						to {
							opacity: 1;
							transform: translateY(0);
						}
					}
					
					.assignment-info {
						margin-top: 20rpx;
						padding: 20rpx;
						background-color: #f8f9fa;
						border-radius: 12rpx;
						
						.assignment-title {
							font-size: 28rpx;
							color: #606266;
							margin-bottom: 16rpx;
							display: flex;
							align-items: center;
							
							&::before {
								content: '';
								display: inline-block;
								width: 4rpx;
								height: 24rpx;
								background-color: #1e3a8a;
								margin-right: 12rpx;
								border-radius: 2rpx;
							}
						}
						
						.assignment-list {
							.assignment-item {
								background-color: #ffffff;
								border-radius: 8rpx;
								padding: 20rpx;
								margin-bottom: 16rpx;
								box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
								cursor: pointer;
								
								&:active {
									opacity: 0.8;
								}
								
								&:last-child {
									margin-bottom: 0;
								}
								
								.assignment-header {
									display: flex;
									justify-content: space-between;
									align-items: center;
									margin-bottom: 16rpx;
									padding-bottom: 12rpx;
									border-bottom: 1px dashed #ebeef5;
									
									.assignee-name {
										font-size: 28rpx;
										color: #1e3a8a;
										font-weight: 600;
									}
									
									.assignment-actions {
										display: flex;
										align-items: center;
										gap: 20rpx;
										
										.assignment-status {
											font-size: 24rpx;
											padding: 4rpx 16rpx;
											border-radius: 4rpx;
											
											&.status-1 {
												background-color: #ecf5ff;
												color: #409eff;
											}
											
											&.status-2 {
												background-color: #f0f9eb;
												color: #67c23a;
											}
										}
										
										.delete-btn {
											padding: 8rpx;
											
											&:active {
												opacity: 0.7;
											}
										}
									}
								}
								
								.assignment-details {
									.detail-row {
										display: flex;
										margin-bottom: 8rpx;
										
										&:last-child {
											margin-bottom: 0;
										}
										
										.detail-label {
											width: 140rpx;
											color: #909399;
											font-size: 24rpx;
										}
										
										.detail-value {
											flex: 1;
											color: #606266;
											font-size: 24rpx;
										}
									}
								}
							}
							
							.no-assignment {
								text-align: center;
								padding: 30rpx 0;
								color: #909399;
								font-size: 28rpx;
								background-color: #ffffff;
								border-radius: 8rpx;
								box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
							}
						}
					}
				}
				
				.task-footer {
					margin-top: 20rpx;
					padding-top: 20rpx;
					border-top: 1px solid #ebeef5;
					display: flex;
					justify-content: flex-end;
					
					.assign-btn {
						width: 200rpx;
						height: 60rpx;
						line-height: 60rpx;
						font-size: 28rpx;
						color: #ffffff;
						background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
						border: none;
						border-radius: 6rpx;
						
						&::after {
							border: none;
						}
						
						&:active {
							opacity: 0.8;
						}
					}
				}
			}
		}
	}

	.user-picker-container {
		padding: 30rpx;
		max-height: 70vh;
		background-color: #ffffff;
		
		.user-picker-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 30rpx;
			padding-bottom: 20rpx;
			border-bottom: 1px solid #ebeef5;
			
			.user-picker-title {
				font-size: 32rpx;
				font-weight: 500;
				color: #2c3e50;
			}
			
			.user-picker-actions {
				display: flex;
				align-items: center;
			}
		}
		
		.user-list {
			max-height: 60vh;
			overflow-y: auto;
			padding: 10rpx 0;
			
			.user-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 24rpx 20rpx;
				margin-bottom: 16rpx;
				border-radius: 8rpx;
				background-color: #f8f9fa;
				transition: all 0.3s;
				
				&:active {
					opacity: 0.8;
				}
				
				&.user-item-selected {
					background-color: rgba(30, 58, 138, 0.05);
					border-left: 4rpx solid #1e3a8a;
					
					.user-name {
						color: #1e3a8a;
						font-weight: 600;
					}
				}
				
				.user-name {
					font-size: 28rpx;
					color: #2c3e50;
					font-weight: 500;
				}
			}
		}
		
		.quantity-input {
			padding: 20rpx 24rpx;
			border-top: 1px solid #ebeef5;
			
			.input-label {
				font-size: 28rpx;
				color: #2c3e50;
				margin-bottom: 16rpx;
			}
			
			.input-wrapper {
				.quantity-input-field {
					width: 100%;
					height: 80rpx;
					background-color: #f8f9fa;
					border: 1px solid #ebeef5;
					border-radius: 8rpx;
					padding: 0 24rpx;
					font-size: 28rpx;
					color: #2c3e50;
					margin-bottom: 12rpx;
				}
				
				.quantity-tip {
					font-size: 24rpx;
					color: #909399;
				}
			}
		}
	}

	.assignment-detail-container {
		padding: 30rpx;
		background-color: #ffffff;
		max-height: 80vh;
		display: flex;
		flex-direction: column;
		
		.detail-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 30rpx;
			padding-bottom: 20rpx;
			border-bottom: 1px solid #ebeef5;
			
			.detail-title {
				font-size: 32rpx;
				font-weight: 500;
				color: #2c3e50;
			}
			
			.detail-actions {
				display: flex;
				align-items: center;
			}
		}
		
		.detail-content {
			flex: 1;
			overflow-y: auto;
			
			.detail-item {
				margin-bottom: 24rpx;
				
				.detail-label {
					display: block;
					font-size: 28rpx;
					color: #606266;
					margin-bottom: 12rpx;
				}
				
				.detail-value {
					font-size: 32rpx;
					color: #2c3e50;
					font-weight: 500;
					
					&.description-text {
						font-size: 28rpx;
						line-height: 1.6;
						color: #606266;
						font-weight: normal;
						background-color: #f8f9fa;
						padding: 20rpx;
						border-radius: 8rpx;
						border-left: 4rpx solid #1e3a8a;
					}
				}
				
				.image-list {
					display: flex;
					flex-wrap: wrap;
					margin: 0 -10rpx;
					
					.image-item {
						width: calc(33.33% - 20rpx);
						height: 200rpx;
						margin: 10rpx;
						border-radius: 8rpx;
						background-color: #f8f9fa;
					}
					
					.assignment-image {
						width: calc(25% - 20rpx);
						height: 120rpx;
						margin: 10rpx;
						border-radius: 6rpx;
						background-color: #f8f9fa;
					}
				}
			}
		}
	}

	.repair-button-container {
		margin-bottom: 30rpx;
		text-align: right;
		
		.repair-btn {
			padding: 12rpx 24rpx;
			font-size: 28rpx;
			color: #ffffff;
			background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
			border: none;
			border-radius: 6rpx;
			
			&:active {
				opacity: 0.8;
			}
		}
	}

	.maintenance-form-container {
		padding: 30rpx;
		background-color: #ffffff;
		max-height: 80vh;
		display: flex;
		flex-direction: column;
		
		.form-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 30rpx;
			padding-bottom: 20rpx;
			border-bottom: 1px solid #ebeef5;
			
			.form-title {
				font-size: 32rpx;
				font-weight: 500;
				color: #2c3e50;
			}
			
			.form-actions {
				display: flex;
				align-items: center;
			}
		}
		
		.form-content {
			flex: 1;
			overflow-y: auto;
			
			.form-item {
				margin-bottom: 24rpx;
				
				.form-label {
					display: block;
					font-size: 28rpx;
					color: #606266;
					margin-bottom: 12rpx;
				}
				
				.form-textarea {
					width: 100%;
					min-height: 160rpx;
					background-color: #f8f9fa;
					border: 1px solid #ebeef5;
					border-radius: 8rpx;
					padding: 24rpx;
					font-size: 28rpx;
					color: #2c3e50;
					line-height: 1.6;
					box-sizing: border-box;
					resize: none;
				}
				
				.char-count {
					text-align: right;
					margin-top: 8rpx;
					
					text {
						font-size: 24rpx;
						color: #909399;
					}
				}
				
				.upload-container {
					position: relative;
					
					.upload-progress {
						margin-top: 16rpx;
						padding: 16rpx;
						background-color: rgba(30, 58, 138, 0.05);
						border-radius: 8rpx;
						border: 1px solid rgba(30, 58, 138, 0.1);
						
						.progress-bar {
							width: 100%;
							height: 8rpx;
							background-color: #ebeef5;
							border-radius: 4rpx;
							overflow: hidden;
							margin-bottom: 12rpx;
							
							.progress-fill {
								height: 100%;
								background: linear-gradient(90deg, #1e3a8a 0%, #3b82f6 100%);
								border-radius: 4rpx;
								transition: width 0.3s ease;
								position: relative;
								
								&::after {
									content: '';
									position: absolute;
									top: 0;
									left: 0;
									right: 0;
									bottom: 0;
									background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
									animation: shimmer 1.5s infinite;
								}
							}
						}
						
						.progress-text {
							font-size: 24rpx;
							color: #1e3a8a;
							text-align: center;
							display: block;
							font-weight: 500;
						}
					}
				}
			}
			
			.form-item:last-child {
				margin-bottom: 0;
			}
			
			@keyframes shimmer {
				0% {
					transform: translateX(-100%);
				}
				100% {
					transform: translateX(100%);
				}
			}
		}
	}
}
</style>
