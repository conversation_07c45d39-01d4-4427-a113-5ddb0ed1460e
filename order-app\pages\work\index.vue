<template>
	<view class="task-list-container">
		<!-- 状态筛选tabs -->
		<view class="status-tabs">
			<view 
				class="tab-item" 
				:class="{ active: queryParams.assignmentStatus === null }"
				@click="changeStatus(null)"
			>全部</view>
			<view 
				class="tab-item" 
				:class="{ active: queryParams.assignmentStatus === '1' }"
				@click="changeStatus('1')"
			>进行中</view>
			<view 
				class="tab-item" 
				:class="{ active: queryParams.assignmentStatus === '2' }"
				@click="changeStatus('2')"
			>已完成</view>
		</view>
		
		<!-- 搜索框 -->
		<view class="search-container">
			<view class="search-box">
				<u-icon name="search" size="32" color="#909399" class="search-icon"></u-icon>
				<input 
					type="text" 
					v-model="searchKeyword"
					class="search-input"
					placeholder="请输入产品编号版本搜索"
					@input="onSearchInput"
					@confirm="performSearch"
				/>
				<view class="search-clear" v-if="searchKeyword" @click="clearSearch">
					<u-icon name="close-circle-fill" size="32" color="#c0c4cc"></u-icon>
				</view>
			</view>
		</view>
		
		<!-- 任务列表 -->
		<scroll-view 
			class="task-list"
			scroll-y
			@scrolltolower="loadMore"
			refresher-enabled
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh"
		>
			<view class="task-item" v-for="(task, index) in taskList" :key="index">
				<view class="task-header">
					<text class="task-name">{{ task.taskName }}</text>
					<text class="task-status" :class="'status-' + task.assignmentStatus">
						{{ getStatusText(task.assignmentStatus) }}
					</text>
				</view>
				<view class="task-info">
					<view class="info-row">
						<text class="label">工单号：</text>
						<text class="value">{{ task.workOrderNo }}</text>
					</view>
					<view class="info-row">
						<text class="label">指派数量：</text>
						<text class="value">{{ task.assignedQuantity }}</text>
					</view>
					<view class="info-row">
						<text class="label">下单客户：</text>
						<text class="value">{{ task.orderCustomerName || '暂无' }}</text>
					</view>
					<view class="info-row">
						<text class="label">订单备注：</text>
						<text class="value">{{ task.ordersRemark || '暂无' }}</text>
					</view>
					<template v-if="task.assignmentStatus === '2'">
						<view class="info-row">
							<text class="label">良品数：</text>
							<text class="value">{{ task.reportedGoodQuantity }}</text>
						</view>
						<view class="info-row">
							<text class="label">不良品数：</text>
							<text class="value">{{ task.reportedDefectiveQuantity }}</text>
						</view>
					</template>
				</view>
				<view class="task-footer">
					<button 
						v-if="task.recordId"
						class="submit-btn secondary"
						@click="showSubmitForm(task)"
					>修改</button>
					<button 
						v-if="task.assignmentStatus === '1'"
						class="submit-btn"
						@click="showSubmitForm(task)"
					>完成任务</button>
				</view>
			</view>
			
			<!-- 加载更多提示 -->
			<view class="load-more" v-if="taskList.length > 0">
				<text v-if="hasMore">加载中...</text>
				<text v-else>没有更多数据了</text>
			</view>
			
			<!-- 空状态提示 -->
			<view class="empty-state" v-if="taskList.length === 0">
				<text>暂无任务</text>
			</view>
		</scroll-view>
		
		<!-- 提交表单弹窗 -->
		<u-popup
			mode="bottom"
			:show="showSubmitFormFlag"
			@close="closeSubmitForm"
			border-radius="24"
			safe-area-inset-bottom
		>
			<view class="submit-form-container">
				<view class="form-header">
					<text class="form-title">提交任务</text>
					<view class="form-actions">
						<u-button plain size="mini" @click="closeSubmitForm" style="margin-right: 20rpx;">取消</u-button>
						<u-button type="primary" size="mini" @click="submitForm" :disabled="isUploading">
							{{ isUploading ? '上传中...' : '确认' }}
						</u-button>
					</view>
				</view>
				
				<scroll-view class="form-content" scroll-y>
					<!-- 包装任务字段 -->
					<template v-if="currentTaskType === 'packaging'">
											<view class="form-item">
						<text class="form-label">字节号 *</text>
						<textarea 
							v-model="formData.byteNo"
							class="form-textarea"
							placeholder="请输入字节号"
							:auto-height="true"
							:maxlength="500"
						></textarea>
						<view class="char-count">
							<text>{{ (formData.byteNo || '').length }}/500</text>
						</view>
					</view>
						
						<view class="form-item">
							<text class="form-label">良品数 *</text>
							<input 
								type="number" 
								v-model="formData.goodQuantity"
								class="form-input"
								placeholder="请输入良品数"
							/>
						</view>
						
						<view class="form-item">
							<text class="form-label">不良品数 *</text>
							<input 
								type="number" 
								v-model="formData.defectiveQuantity"
								class="form-input"
								placeholder="请输入不良品数"
							/>
						</view>
						
						<view class="form-item">
							<text class="form-label">图片 *</text>
							<view class="upload-container">
								<u-upload
									:fileList="fileList"
									@afterRead="afterRead"
									@delete="deletePic"
									:maxCount="100"
									:maxSize="5242880"
									width="200"
									height="200"
									:previewFullImage="true"
									:disabled="isUploading"
								></u-upload>
								
								<!-- 上传进度指示器 -->
								<view class="upload-progress" v-if="isUploading">
									<view class="progress-bar">
										<view class="progress-fill" :style="{ width: uploadProgress + '%' }"></view>
									</view>
									<text class="progress-text">上传中 {{ uploadProgress }}%</text>
								</view>
							</view>
						</view>
					</template>
					
					<!-- 发货任务字段 -->
					<template v-else-if="currentTaskType === 'shipping'">
						<view class="form-item">
							<text class="form-label">发货地址 *</text>
							<textarea 
								v-model="formData.locationAddress"
								class="form-textarea"
								placeholder="请输入发货地址"
								:auto-height="true"
								:maxlength="500"
							></textarea>
						</view>
					</template>
					
					<!-- 维修任务字段 -->
					<template v-else-if="currentTaskType === 'maintenance'">
						<view class="form-item">
							<text class="form-label">图片 *</text>
							<view class="upload-container">
								<u-upload
									:fileList="fileList"
									@afterRead="afterRead"
									@delete="deletePic"
									:maxCount="100"
									:maxSize="5242880"
									width="200"
									height="200"
									:previewFullImage="true"
									:disabled="isUploading"
								></u-upload>

								<!-- 上传进度指示器 -->
								<view class="upload-progress" v-if="isUploading">
									<view class="progress-bar">
										<view class="progress-fill" :style="{ width: uploadProgress + '%' }"></view>
									</view>
									<text class="progress-text">上传中 {{ uploadProgress }}%</text>
								</view>
							</view>
						</view>

						<view class="form-item">
							<text class="form-label">维修产品编号</text>
							<textarea
								v-model="formData.repairGoodNo"
								class="form-textarea"
								placeholder="请输入维修产品编号"
								:auto-height="true"
								:maxlength="200"
							></textarea>
							<view class="char-count">
								<text>{{ (formData.repairGoodNo || '').length }}/200</text>
							</view>
						</view>

						<view class="form-item">
							<text class="form-label">文字说明 *</text>
							<textarea
								v-model="formData.description"
								class="form-textarea"
								placeholder="请输入文字说明"
								:auto-height="true"
								:maxlength="500"
							></textarea>
						</view>
					</template>
					
					<!-- 其他任务字段（保持原有逻辑） -->
					<template v-else>
						<view class="form-item">
							<text class="form-label">良品数 *</text>
							<input 
								type="number" 
								v-model="formData.goodQuantity"
								class="form-input"
								placeholder="请输入良品数"
							/>
						</view>
						
						<view class="form-item">
							<text class="form-label">不良品数 *</text>
							<input 
								type="number" 
								v-model="formData.defectiveQuantity"
								class="form-input"
								placeholder="请输入不良品数"
							/>
						</view>
						
						<view class="form-item">
							<text class="form-label">图片 *</text>
							<view class="upload-container">
								<u-upload
									:fileList="fileList"
									@afterRead="afterRead"
									@delete="deletePic"
									:maxCount="100"
									:maxSize="5242880"
									width="200"
									height="200"
									:previewFullImage="true"
									:disabled="isUploading"
								></u-upload>
								
								<!-- 上传进度指示器 -->
								<view class="upload-progress" v-if="isUploading">
									<view class="progress-bar">
										<view class="progress-fill" :style="{ width: uploadProgress + '%' }"></view>
									</view>
									<text class="progress-text">上传中 {{ uploadProgress }}%</text>
								</view>
							</view>
						</view>
					</template>
				</scroll-view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import { listTaskAssignments,updateTaskAssignments } from "@/api/work/taskAssignments.js"
	import { updateProductionRecords,addProductionRecords } from "@/api/work/productionRecords.js"
	import { baseUrl } from "../../config"
	export default {
		data() {
			return {
				queryParams:{
					assignedToUserId:this.$store.state.user.userId,
					pageSize:5,
					pageNum:1,
					assignmentStatus:null,
					orderProductNumberVersion: ''
				},
				taskList: [],
				total: 0,
				isRefreshing: false,
				hasMore: true,
				baseUrl,
				showSubmitFormFlag: false,
				currentTask: null,
				formData: {
					goodQuantity: '',
					defectiveQuantity: '',
					pic: '',
					byteNo: '', // 字节号（包装任务用）
					locationAddress: '', // 地址（发货任务用）
					description: '', // 文字说明（维修任务用）
					repairGoodNo: '' // 维修产品编号（维修任务用）
				},
				fileList: [], // 用于存储图片列表
				searchKeyword: '',
				searchTimer: null, // 搜索防抖定时器
				isUploading: false, // 上传状态
				uploadProgress: 0 // 上传进度
			}
		},
		computed: {
			// 获取当前任务类型
			currentTaskType() {
				if (!this.currentTask || !this.currentTask.taskName) return 'other';
				const taskName = this.currentTask.taskName.toLowerCase();
				if (taskName.includes('包装')) return 'packaging';
				if (taskName.includes('发货')) return 'shipping';
				if (taskName.includes('维修')) return 'maintenance';
				return 'other';
			}
		},
		onLoad() {
			this.getList()
		},
		methods: {
			// 获取任务列表
			async getList(isRefresh = false) {
				try {
					// 设置搜索参数
					this.queryParams.orderProductNumberVersion = this.searchKeyword
					
					const res = await listTaskAssignments(this.queryParams)
					if (isRefresh) {
						this.taskList = res.rows
					} else {
						this.taskList = [...this.taskList, ...res.rows]
					}
					this.total = res.total
					this.hasMore = this.taskList.length < this.total
				} catch (error) {
					console.error('获取任务列表失败：', error)
					uni.showToast({
						title: '获取任务列表失败',
						icon: 'none'
					})
				}
			},
			
			// 切换状态
			changeStatus(status) {
				this.queryParams.assignmentStatus = status
				this.queryParams.pageNum = 1
				this.taskList = []
				this.getList(true)
			},
			
			// 下拉刷新
			async onRefresh() {
				this.isRefreshing = true
				this.queryParams.pageNum = 1
				await this.getList(true)
				this.isRefreshing = false
			},
			
			// 加载更多
			loadMore() {
				if (!this.hasMore) return
				this.queryParams.pageNum++
				this.getList()
			},
			
			// 获取状态文本
			getStatusText(status) {
				const statusMap = {
					'1': '进行中',
					'2': '已完成'
				}
				return statusMap[status] || '未知状态'
			},
			
			// 显示提交表单
			showSubmitForm(task) {
				this.currentTask = task
				if (task.recordId) {
					// 如果是修改，填充已有数据
					this.formData = {
						goodQuantity: task.goodQuantity != '' || task.goodQuantity != null ? task.goodQuantity : '',
						defectiveQuantity: task.defectiveQuantity != '' || task.defectiveQuantity != null ? task.defectiveQuantity : '',
						pic: task.pic || '',
						byteNo: task.byteNo || '',
						locationAddress: task.locationAddress || '',
						description: task.description || '',
						repairGoodNo: task.repairGoodNo || ''
					}
					// 设置图片列表
					if (task.pic) {
						this.fileList = task.pic.split(',').filter(url => url && url.trim()).map(url => ({
							url: this.baseUrl + url.trim(),
							status: 'success',
							message: '上传成功'
						}))
					} else {
						this.fileList = []
					}
				} else {
					// 如果是新建，设置默认值：良品数为指派数量，不良品数为0
					this.formData = {
						goodQuantity: task.assignedQuantity || '',
						defectiveQuantity: '0',
						pic: '',
						byteNo: '',
						locationAddress: '',
						description: '',
						repairGoodNo: ''
					}
					this.fileList = []
				}
				this.showSubmitFormFlag = true
			},
			
			// 关闭提交表单
			closeSubmitForm() {
				this.showSubmitFormFlag = false
				this.currentTask = null
				this.formData = {
					goodQuantity: '',
					defectiveQuantity: '',
					pic: '',
					byteNo: '',
					locationAddress: '',
					description: '',
					repairGoodNo: ''
				}
				this.fileList = []
				// 重置上传状态
				this.isUploading = false
				this.uploadProgress = 0
			},
			
			// 上传图片后
			async afterRead(event) {
				console.log(event)
				
				// 设置上传状态
				this.isUploading = true
				this.uploadProgress = 0
				
				// 显示上传中提示
				uni.showLoading({
					title: '图片上传中...',
					mask: true
				})
				
				try {
					const file = event.file
					
					// 模拟上传进度（因为uni.uploadFile不支持进度回调）
					const progressTimer = setInterval(() => {
						if (this.uploadProgress < 90) {
							this.uploadProgress += 10
						}
					}, 100)
					
					const [uploadErr, uploadRes] = await uni.uploadFile({
						url: this.baseUrl + '/common/upload',
						filePath: file.url,
						name: 'file'
					})
					
					// 清除进度定时器
					clearInterval(progressTimer)
					this.uploadProgress = 100
					
					if (uploadErr) {
						throw new Error('上传图片失败')
					}
					
					const result = JSON.parse(uploadRes.data)
					if (result.code === 200) {
						// 更新图片路径，用逗号分隔多张图片
						const newPic = result.fileName
						if (this.formData.pic) {
							this.formData.pic = this.formData.pic + ',' + newPic
						} else {
							this.formData.pic = newPic
						}
						
						// 更新文件列表显示
						this.fileList = this.formData.pic.split(',').filter(url => url).map(url => ({
							url: this.baseUrl + url,
							status: 'success',
							message: '上传成功'
						}))
						
						// 延迟一下再隐藏加载，让用户看到100%的进度
						setTimeout(() => {
							uni.hideLoading()
							uni.showToast({
								title: '上传成功',
								icon: 'success',
								duration: 1500
							})
							this.isUploading = false
							this.uploadProgress = 0
						}, 300)
						
					} else {
						throw new Error(result.msg || '上传失败')
					}
					
				} catch (error) {
					console.error('上传图片失败：', error)
					
					// 重置状态
					this.isUploading = false
					this.uploadProgress = 0
					
					uni.hideLoading()
					uni.showToast({
						title: error.message || '上传图片失败',
						icon: 'error',
						duration: 2000
					})
				}
			},
			
			// 删除图片
			deletePic(event) {
				console.log(event)
				const pics = this.formData.pic.split(',').filter(url => url)
				pics.splice(event.index, 1)
				this.formData.pic = pics.join(',')
				
				// 更新文件列表显示
				this.fileList = pics.map(url => ({
					url: this.baseUrl + url,
					status: 'success',
					message: '上传成功'
				}))
			},
			
			// 提交表单
			async submitForm() {
				// 根据任务类型进行不同的表单验证
				if (this.currentTaskType === 'packaging') {
					// 包装任务验证
					if (!this.formData.byteNo) {
						uni.showToast({
							title: '请填写字节号',
							icon: 'none'
						})
						return
					}
					if (!this.formData.goodQuantity || !this.formData.defectiveQuantity) {
						uni.showToast({
							title: '请填写良品数和不良品数',
							icon: 'none'
						})
						return
					}
					
					const goodQuantity = Number(this.formData.goodQuantity)
					const defectiveQuantity = Number(this.formData.defectiveQuantity)
					const totalQuantity = goodQuantity + defectiveQuantity
					
					if (totalQuantity > this.currentTask.assignedQuantity) {
						uni.showToast({
							title: '良品数和不良品数之和不能大于指派数量',
							icon: 'none'
						})
						return
					}
					
					if (!this.formData.pic) {
						uni.showToast({
							title: '请上传图片',
							icon: 'none'
						})
						return
					}
				} else if (this.currentTaskType === 'shipping') {
					// 发货任务验证
					if (!this.formData.locationAddress) {
						uni.showToast({
							title: '请填写发货地址',
							icon: 'none'
						})
						return
					}
				} else if (this.currentTaskType === 'maintenance') {
					// 维修任务验证
					if (!this.formData.pic) {
						uni.showToast({
							title: '请上传图片',
							icon: 'none'
						})
						return
					}
					if (!this.formData.description) {
						uni.showToast({
							title: '请填写文字说明',
							icon: 'none'
						})
						return
					}
				} else {
					// 其他任务的原有验证逻辑
					if (!this.formData.goodQuantity || !this.formData.defectiveQuantity) {
						uni.showToast({
							title: '请填写良品数和不良品数',
							icon: 'none'
						})
						return
					}
					
					const goodQuantity = Number(this.formData.goodQuantity)
					const defectiveQuantity = Number(this.formData.defectiveQuantity)
					const totalQuantity = goodQuantity + defectiveQuantity
					
					if (totalQuantity > this.currentTask.assignedQuantity) {
						uni.showToast({
							title: '良品数和不良品数之和不能大于指派数量',
							icon: 'none'
						})
						return
					}
					
					if (!this.formData.pic) {
						uni.showToast({
							title: '请上传图片',
							icon: 'none'
						})
						return
					}
				}
				
				try {
					// 提交生产记录
					const recordData = {
						assignmentId: this.currentTask.assignmentId,
						pic: this.formData.pic,
						byteNo: this.formData.byteNo,
						locationAddress: this.formData.locationAddress,
						description: this.formData.description,
						repairGoodNo: this.formData.repairGoodNo
					}
					
					// 根据任务类型添加相应字段
					if (this.currentTaskType === 'packaging' || this.currentTaskType === 'other') {
						recordData.goodQuantity = Number(this.formData.goodQuantity)
						recordData.defectiveQuantity = Number(this.formData.defectiveQuantity)
					}
					
					if (this.currentTask.recordId) {
						// 修改记录
						recordData.recordId = this.currentTask.recordId
						await updateProductionRecords(recordData)
					} else {
						// 新增记录
						await addProductionRecords(recordData)
					}
					
					// 更新任务状态为已完成
					const updateData = {
						assignmentId: this.currentTask.assignmentId,
						assignmentStatus: '2'
					}
					
					// 根据任务类型添加相应的上报数量字段
					if (this.currentTaskType === 'packaging' || this.currentTaskType === 'other') {
						updateData.reportedGoodQuantity = Number(this.formData.goodQuantity)
						updateData.reportedDefectiveQuantity = Number(this.formData.defectiveQuantity)
					}
					
					await updateTaskAssignments(updateData)
					
					uni.showToast({
						title: '提交成功',
						icon: 'success'
					})
					
					// 关闭弹窗并刷新列表
					this.closeSubmitForm()
					this.onRefresh()
					
				} catch (error) {
					console.error('提交失败：', error)
					uni.showToast({
						title: '提交失败',
						icon: 'none'
					})
				}
			},
			onSearchInput(event) {
				this.searchKeyword = event.target.value;
				// 防抖处理，避免频繁请求
				if (this.searchTimer) {
					clearTimeout(this.searchTimer);
				}
				this.searchTimer = setTimeout(() => {
					this.performSearch();
				}, 500);
			},
			performSearch() {
				this.queryParams.pageNum = 1;
				this.taskList = [];
				this.getList(true);
			},
			clearSearch() {
				this.searchKeyword = '';
				// 清除防抖定时器
				if (this.searchTimer) {
					clearTimeout(this.searchTimer);
					this.searchTimer = null;
				}
				// 重新获取数据
				this.queryParams.pageNum = 1;
				this.taskList = [];
				this.getList(true);
			}
		}
	}
</script>

<style lang="scss">
.task-list-container {
	min-height: 100vh;
	background-color: #f5f7fa;
	
	.status-tabs {
		display: flex;
		background-color: #ffffff;
		padding: 20rpx 30rpx;
		position: sticky;
		top: 0;
		z-index: 1;
		
		.tab-item {
			flex: 1;
			text-align: center;
			font-size: 28rpx;
			color: #606266;
			padding: 16rpx 0;
			position: relative;
			
			&.active {
				color: #1e3a8a;
				font-weight: 500;
				
				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 40rpx;
					height: 4rpx;
					background-color: #1e3a8a;
					border-radius: 2rpx;
				}
			}
		}
	}
	
	.search-container {
		padding: 20rpx 30rpx;
		background-color: #ffffff;
		position: sticky;
		top: 100rpx;
		z-index: 1;
		border-bottom: 1px solid #ebeef5;
		
		.search-box {
			display: flex;
			align-items: center;
			background-color: #f8f9fa;
			border-radius: 12rpx;
			padding: 16rpx 24rpx;
			border: 1px solid #ebeef5;
			transition: all 0.3s;
			
			&:focus-within {
				border-color: #1e3a8a;
				box-shadow: 0 0 0 4rpx rgba(30, 58, 138, 0.1);
			}
			
			.search-icon {
				margin-right: 16rpx;
				flex-shrink: 0;
			}
			
			.search-input {
				flex: 1;
				height: 60rpx;
				background-color: transparent;
				border: none;
				font-size: 28rpx;
				color: #2c3e50;
				
				&::placeholder {
					color: #909399;
				}
			}
			
			.search-clear {
				margin-left: 16rpx;
				padding: 4rpx;
				border-radius: 50%;
				flex-shrink: 0;
				
				&:active {
					opacity: 0.8;
				}
			}
		}
	}
	
	.task-list {
		height: 80vh;
		padding: 20rpx;
		
		.task-item {
			background-color: #ffffff;
			border-radius: 12rpx;
			padding: 24rpx;
			margin-bottom: 20rpx;
			box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
			
			.task-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;
				
				.task-name {
					font-size: 32rpx;
					font-weight: 600;
					color: #1e3a8a;
				}
				
				.task-status {
					font-size: 24rpx;
					padding: 6rpx 20rpx;
					border-radius: 6rpx;
					
					&.status-1 {
						background-color: #ecf5ff;
						color: #409eff;
					}
					
					&.status-2 {
						background-color: #f0f9eb;
						color: #67c23a;
					}
				}
			}
			
			.task-info {
				.info-row {
					display: flex;
					margin-bottom: 12rpx;
					
					&:last-child {
						margin-bottom: 0;
					}
					
					.label {
						width: 140rpx;
						color: #909399;
						font-size: 26rpx;
					}
					
					.value {
						flex: 1;
						color: #2c3e50;
						font-size: 26rpx;
					}
				}
			}
			
			.task-footer {
				margin-top: 20rpx;
				padding-top: 20rpx;
				border-top: 1px solid #ebeef5;
				display: flex;
				justify-content: flex-end;
				gap: 20rpx;
				
				.submit-btn {
					width: 200rpx;
					height: 60rpx;
					line-height: 60rpx;
					font-size: 28rpx;
					color: #ffffff;
					background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
					border: none;
					border-radius: 6rpx;
					
					&::after {
						border: none;
					}
					
					&:active {
						opacity: 0.8;
					}
					
					&.secondary {
						background: linear-gradient(135deg, #606266 0%, #909399 100%);
					}
				}
			}
		}
		
		.load-more {
			text-align: center;
			padding: 20rpx 0;
			color: #909399;
			font-size: 24rpx;
		}
		
		.empty-state {
			text-align: center;
			padding: 100rpx 0;
			color: #909399;
			font-size: 28rpx;
		}
	}
	
	.submit-form-container {
		padding: 30rpx;
		background-color: #ffffff;
		display: flex;
		flex-direction: column;
		max-height: 80vh;
		
		.form-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 30rpx;
			padding-bottom: 20rpx;
			border-bottom: 1px solid #ebeef5;
			flex-shrink: 0;
			
			.form-title {
				font-size: 32rpx;
				font-weight: 500;
				color: #2c3e50;
			}
			
			.form-actions {
				display: flex;
				align-items: center;
			}
		}
		
		.form-content {
			flex: 1;
			overflow-y: auto;
			padding-bottom: 30rpx;
			
			.form-item {
				margin-bottom: 24rpx;
				
				.form-label {
					display: block;
					font-size: 28rpx;
					color: #2c3e50;
					margin-bottom: 12rpx;
				}
				
				.form-input {
					width: 100%;
					height: 80rpx;
					background-color: #f8f9fa;
					border: 1px solid #ebeef5;
					border-radius: 8rpx;
					padding: 0 24rpx;
					font-size: 28rpx;
					color: #2c3e50;
				}
				
				.form-textarea {
					width: 100%;
					min-height: 160rpx;
					background-color: #f8f9fa;
					border: 1px solid #ebeef5;
					border-radius: 8rpx;
					padding: 24rpx;
					font-size: 28rpx;
					color: #2c3e50;
					line-height: 1.6;
					box-sizing: border-box;
				}
				
				.char-count {
					text-align: right;
					margin-top: 8rpx;
					
					text {
						font-size: 24rpx;
						color: #909399;
					}
				}
				
				.upload-container {
					position: relative;
					
					.upload-progress {
						margin-top: 16rpx;
						padding: 16rpx;
						background-color: rgba(30, 58, 138, 0.05);
						border-radius: 8rpx;
						border: 1px solid rgba(30, 58, 138, 0.1);
						
						.progress-bar {
							width: 100%;
							height: 8rpx;
							background-color: #ebeef5;
							border-radius: 4rpx;
							overflow: hidden;
							margin-bottom: 12rpx;
							
							.progress-fill {
								height: 100%;
								background: linear-gradient(90deg, #1e3a8a 0%, #3b82f6 100%);
								border-radius: 4rpx;
								transition: width 0.3s ease;
								position: relative;
								
								&::after {
									content: '';
									position: absolute;
									top: 0;
									left: 0;
									right: 0;
									bottom: 0;
									background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
									animation: shimmer 1.5s infinite;
								}
							}
						}
						
						.progress-text {
							font-size: 24rpx;
							color: #1e3a8a;
							text-align: center;
							display: block;
							font-weight: 500;
						}
					}
				}
				
				@keyframes shimmer {
					0% {
						transform: translateX(-100%);
					}
					100% {
						transform: translateX(100%);
					}
				}
			}
		}
	}
}
</style>
