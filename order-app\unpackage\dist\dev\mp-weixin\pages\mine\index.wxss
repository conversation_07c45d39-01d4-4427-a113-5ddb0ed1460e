@charset "UTF-8";
/**
 * uni-app内置的常用样式变量
 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background-color: #f5f6f7;
}
.mine-container {
  width: 100%;
  height: 100%;
}
.mine-container .header-section {
  padding: 15px 15px 45px 15px;
  background-color: #3c96f3;
  color: white;
}
.mine-container .header-section .login-tip {
  font-size: 18px;
  margin-left: 10px;
}
.mine-container .header-section .cu-avatar {
  border: 2px solid #eaeaea;
}
.mine-container .header-section .cu-avatar .icon {
  font-size: 40px;
}
.mine-container .header-section .user-info {
  margin-left: 15px;
}
.mine-container .header-section .user-info .u_title {
  font-size: 18px;
  line-height: 30px;
}
.mine-container .content-section {
  position: relative;
  top: -50px;
}
.mine-container .content-section .mine-actions {
  margin: 15px 15px;
  padding: 20px 0px;
  border-radius: 8px;
  background-color: white;
}
.mine-container .content-section .mine-actions .action-item .icon {
  font-size: 28px;
}
.mine-container .content-section .mine-actions .action-item .text {
  display: block;
  font-size: 13px;
  margin: 8px 0px;
}
