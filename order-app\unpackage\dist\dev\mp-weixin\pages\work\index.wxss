@charset "UTF-8";
/**
 * uni-app内置的常用样式变量
 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.task-list-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}
.task-list-container .status-tabs {
  display: flex;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 1;
}
.task-list-container .status-tabs .tab-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #606266;
  padding: 16rpx 0;
  position: relative;
}
.task-list-container .status-tabs .tab-item.active {
  color: #1e3a8a;
  font-weight: 500;
}
.task-list-container .status-tabs .tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #1e3a8a;
  border-radius: 2rpx;
}
.task-list-container .search-container {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  position: -webkit-sticky;
  position: sticky;
  top: 100rpx;
  z-index: 1;
  border-bottom: 1px solid #ebeef5;
}
.task-list-container .search-container .search-box {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  border: 1px solid #ebeef5;
  transition: all 0.3s;
}
.task-list-container .search-container .search-box:focus-within {
  border-color: #1e3a8a;
  box-shadow: 0 0 0 4rpx rgba(30, 58, 138, 0.1);
}
.task-list-container .search-container .search-box .search-icon {
  margin-right: 16rpx;
  flex-shrink: 0;
}
.task-list-container .search-container .search-box .search-input {
  flex: 1;
  height: 60rpx;
  background-color: transparent;
  border: none;
  font-size: 28rpx;
  color: #2c3e50;
}
.task-list-container .search-container .search-box .search-input::-webkit-input-placeholder {
  color: #909399;
}
.task-list-container .search-container .search-box .search-input::placeholder {
  color: #909399;
}
.task-list-container .search-container .search-box .search-clear {
  margin-left: 16rpx;
  padding: 4rpx;
  border-radius: 50%;
  flex-shrink: 0;
}
.task-list-container .search-container .search-box .search-clear:active {
  opacity: 0.8;
}
.task-list-container .task-list {
  height: 80vh;
  padding: 20rpx;
}
.task-list-container .task-list .task-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}
.task-list-container .task-list .task-item .task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.task-list-container .task-list .task-item .task-header .task-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e3a8a;
}
.task-list-container .task-list .task-item .task-header .task-status {
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 6rpx;
}
.task-list-container .task-list .task-item .task-header .task-status.status-1 {
  background-color: #ecf5ff;
  color: #409eff;
}
.task-list-container .task-list .task-item .task-header .task-status.status-2 {
  background-color: #f0f9eb;
  color: #67c23a;
}
.task-list-container .task-list .task-item .task-info .info-row {
  display: flex;
  margin-bottom: 12rpx;
}
.task-list-container .task-list .task-item .task-info .info-row:last-child {
  margin-bottom: 0;
}
.task-list-container .task-list .task-item .task-info .info-row .label {
  width: 140rpx;
  color: #909399;
  font-size: 26rpx;
}
.task-list-container .task-list .task-item .task-info .info-row .value {
  flex: 1;
  color: #2c3e50;
  font-size: 26rpx;
}
.task-list-container .task-list .task-item .task-footer {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}
.task-list-container .task-list .task-item .task-footer .submit-btn {
  width: 200rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 28rpx;
  color: #ffffff;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  border: none;
  border-radius: 6rpx;
}
.task-list-container .task-list .task-item .task-footer .submit-btn::after {
  border: none;
}
.task-list-container .task-list .task-item .task-footer .submit-btn:active {
  opacity: 0.8;
}
.task-list-container .task-list .task-item .task-footer .submit-btn.secondary {
  background: linear-gradient(135deg, #606266 0%, #909399 100%);
}
.task-list-container .task-list .load-more {
  text-align: center;
  padding: 20rpx 0;
  color: #909399;
  font-size: 24rpx;
}
.task-list-container .task-list .empty-state {
  text-align: center;
  padding: 100rpx 0;
  color: #909399;
  font-size: 28rpx;
}
.task-list-container .submit-form-container {
  padding: 30rpx;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  max-height: 80vh;
}
.task-list-container .submit-form-container .form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #ebeef5;
  flex-shrink: 0;
}
.task-list-container .submit-form-container .form-header .form-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #2c3e50;
}
.task-list-container .submit-form-container .form-header .form-actions {
  display: flex;
  align-items: center;
}
.task-list-container .submit-form-container .form-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 30rpx;
}
.task-list-container .submit-form-container .form-content .form-item {
  margin-bottom: 24rpx;
}
.task-list-container .submit-form-container .form-content .form-item .form-label {
  display: block;
  font-size: 28rpx;
  color: #2c3e50;
  margin-bottom: 12rpx;
}
.task-list-container .submit-form-container .form-content .form-item .form-input {
  width: 100%;
  height: 80rpx;
  background-color: #f8f9fa;
  border: 1px solid #ebeef5;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #2c3e50;
}
.task-list-container .submit-form-container .form-content .form-item .form-textarea {
  width: 100%;
  min-height: 160rpx;
  background-color: #f8f9fa;
  border: 1px solid #ebeef5;
  border-radius: 8rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #2c3e50;
  line-height: 1.6;
  box-sizing: border-box;
}
.task-list-container .submit-form-container .form-content .form-item .char-count {
  text-align: right;
  margin-top: 8rpx;
}
.task-list-container .submit-form-container .form-content .form-item .char-count text {
  font-size: 24rpx;
  color: #909399;
}
.task-list-container .submit-form-container .form-content .form-item .upload-container {
  position: relative;
}
.task-list-container .submit-form-container .form-content .form-item .upload-container .upload-progress {
  margin-top: 16rpx;
  padding: 16rpx;
  background-color: rgba(30, 58, 138, 0.05);
  border-radius: 8rpx;
  border: 1px solid rgba(30, 58, 138, 0.1);
}
.task-list-container .submit-form-container .form-content .form-item .upload-container .upload-progress .progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #ebeef5;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
}
.task-list-container .submit-form-container .form-content .form-item .upload-container .upload-progress .progress-bar .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1e3a8a 0%, #3b82f6 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
  position: relative;
}
.task-list-container .submit-form-container .form-content .form-item .upload-container .upload-progress .progress-bar .progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  -webkit-animation: shimmer 1.5s infinite;
          animation: shimmer 1.5s infinite;
}
.task-list-container .submit-form-container .form-content .form-item .upload-container .upload-progress .progress-text {
  font-size: 24rpx;
  color: #1e3a8a;
  text-align: center;
  display: block;
  font-weight: 500;
}
@-webkit-keyframes shimmer {
0% {
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
}
100% {
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
}
}
@keyframes shimmer {
0% {
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
}
100% {
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
}
}
