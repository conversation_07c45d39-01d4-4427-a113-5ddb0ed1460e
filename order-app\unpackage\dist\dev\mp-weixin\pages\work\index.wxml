<view class="task-list-container"><view class="status-tabs"><view data-event-opts="{{[['tap',[['changeStatus',[null]]]]]}}" class="{{['tab-item',(queryParams.assignmentStatus===null)?'active':'']}}" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['changeStatus',['1']]]]]}}" class="{{['tab-item',(queryParams.assignmentStatus==='1')?'active':'']}}" bindtap="__e">进行中</view><view data-event-opts="{{[['tap',[['changeStatus',['2']]]]]}}" class="{{['tab-item',(queryParams.assignmentStatus==='2')?'active':'']}}" bindtap="__e">已完成</view></view><view class="search-container"><view class="search-box"><u-icon class="search-icon" vue-id="3614c1ce-1" name="search" size="32" color="#909399" bind:__l="__l"></u-icon><input class="search-input" type="text" placeholder="请输入产品编号版本搜索" data-event-opts="{{[['input',[['__set_model',['','searchKeyword','$event',[]]],['onSearchInput',['$event']]]],['confirm',[['performSearch',['$event']]]]]}}" value="{{searchKeyword}}" bindinput="__e" bindconfirm="__e"/><block wx:if="{{searchKeyword}}"><view data-event-opts="{{[['tap',[['clearSearch',['$event']]]]]}}" class="search-clear" bindtap="__e"><u-icon vue-id="3614c1ce-2" name="close-circle-fill" size="32" color="#c0c4cc" bind:__l="__l"></u-icon></view></block></view></view><scroll-view class="task-list" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{isRefreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><block wx:for="{{$root.l0}}" wx:for-item="task" wx:for-index="index" wx:key="index"><view class="task-item"><view class="task-header"><text class="task-name">{{task.$orig.taskName}}</text><text class="{{['task-status','status-'+task.$orig.assignmentStatus]}}">{{''+task.m0+''}}</text></view><view class="task-info"><view class="info-row"><text class="label">工单号：</text><text class="value">{{task.$orig.workOrderNo}}</text></view><view class="info-row"><text class="label">指派数量：</text><text class="value">{{task.$orig.assignedQuantity}}</text></view><view class="info-row"><text class="label">下单客户：</text><text class="value">{{task.$orig.orderCustomerName||'暂无'}}</text></view><view class="info-row"><text class="label">订单备注：</text><text class="value">{{task.$orig.ordersRemark||'暂无'}}</text></view><block wx:if="{{task.$orig.assignmentStatus==='2'}}"><view class="info-row"><text class="label">良品数：</text><text class="value">{{task.$orig.reportedGoodQuantity}}</text></view><view class="info-row"><text class="label">不良品数：</text><text class="value">{{task.$orig.reportedDefectiveQuantity}}</text></view></block></view><view class="task-footer"><block wx:if="{{task.$orig.recordId}}"><button data-event-opts="{{[['tap',[['showSubmitForm',['$0'],[[['taskList','',index]]]]]]]}}" class="submit-btn secondary" bindtap="__e">修改</button></block><block wx:if="{{task.$orig.assignmentStatus==='1'}}"><button data-event-opts="{{[['tap',[['showSubmitForm',['$0'],[[['taskList','',index]]]]]]]}}" class="submit-btn" bindtap="__e">完成任务</button></block></view></view></block><block wx:if="{{$root.g0>0}}"><view class="load-more"><block wx:if="{{hasMore}}"><text>加载中...</text></block><block wx:else><text>没有更多数据了</text></block></view></block><block wx:if="{{$root.g1===0}}"><view class="empty-state"><text>暂无任务</text></view></block></scroll-view><u-popup vue-id="3614c1ce-3" mode="bottom" show="{{showSubmitFormFlag}}" border-radius="24" safe-area-inset-bottom="{{true}}" data-event-opts="{{[['^close',[['closeSubmitForm']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="submit-form-container"><view class="form-header"><text class="form-title">提交任务</text><view class="form-actions"><u-button style="margin-right:20rpx;" vue-id="{{('3614c1ce-4')+','+('3614c1ce-3')}}" plain="{{true}}" size="mini" data-event-opts="{{[['^click',[['closeSubmitForm']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">取消</u-button><u-button vue-id="{{('3614c1ce-5')+','+('3614c1ce-3')}}" type="primary" size="mini" disabled="{{isUploading}}" data-event-opts="{{[['^click',[['submitForm']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">{{''+(isUploading?'上传中...':'确认')+''}}</u-button></view></view><scroll-view class="form-content" scroll-y="{{true}}"><block wx:if="{{currentTaskType==='packaging'}}"><view class="form-item"><text class="form-label">字节号 *</text><textarea class="form-textarea" placeholder="请输入字节号" auto-height="{{true}}" maxlength="{{500}}" data-event-opts="{{[['input',[['__set_model',['$0','byteNo','$event',[]],['formData']]]]]}}" value="{{formData.byteNo}}" bindinput="__e"></textarea><view class="char-count"><text>{{$root.g2+"/500"}}</text></view></view><view class="form-item"><text class="form-label">良品数 *</text><input class="form-input" type="number" placeholder="请输入良品数" data-event-opts="{{[['input',[['__set_model',['$0','goodQuantity','$event',[]],['formData']]]]]}}" value="{{formData.goodQuantity}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">不良品数 *</text><input class="form-input" type="number" placeholder="请输入不良品数" data-event-opts="{{[['input',[['__set_model',['$0','defectiveQuantity','$event',[]],['formData']]]]]}}" value="{{formData.defectiveQuantity}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">图片 *</text><view class="upload-container"><u-upload vue-id="{{('3614c1ce-6')+','+('3614c1ce-3')}}" fileList="{{fileList}}" maxCount="{{100}}" maxSize="{{5242880}}" width="200" height="200" previewFullImage="{{true}}" disabled="{{isUploading}}" data-event-opts="{{[['^afterRead',[['afterRead']]],['^delete',[['deletePic']]]]}}" bind:afterRead="__e" bind:delete="__e" bind:__l="__l"></u-upload><block wx:if="{{isUploading}}"><view class="upload-progress"><view class="progress-bar"><view class="progress-fill" style="{{'width:'+(uploadProgress+'%')+';'}}"></view></view><text class="progress-text">{{"上传中 "+uploadProgress+"%"}}</text></view></block></view></view></block><block wx:else><block wx:if="{{currentTaskType==='shipping'}}"><view class="form-item"><text class="form-label">发货地址 *</text><textarea class="form-textarea" placeholder="请输入发货地址" auto-height="{{true}}" maxlength="{{500}}" data-event-opts="{{[['input',[['__set_model',['$0','locationAddress','$event',[]],['formData']]]]]}}" value="{{formData.locationAddress}}" bindinput="__e"></textarea></view></block><block wx:else><block wx:if="{{currentTaskType==='maintenance'}}"><view class="form-item"><text class="form-label">图片 *</text><view class="upload-container"><u-upload vue-id="{{('3614c1ce-7')+','+('3614c1ce-3')}}" fileList="{{fileList}}" maxCount="{{100}}" maxSize="{{5242880}}" width="200" height="200" previewFullImage="{{true}}" disabled="{{isUploading}}" data-event-opts="{{[['^afterRead',[['afterRead']]],['^delete',[['deletePic']]]]}}" bind:afterRead="__e" bind:delete="__e" bind:__l="__l"></u-upload><block wx:if="{{isUploading}}"><view class="upload-progress"><view class="progress-bar"><view class="progress-fill" style="{{'width:'+(uploadProgress+'%')+';'}}"></view></view><text class="progress-text">{{"上传中 "+uploadProgress+"%"}}</text></view></block></view></view><view class="form-item"><text class="form-label">维修产品编号</text><textarea class="form-textarea" placeholder="请输入维修产品编号" auto-height="{{true}}" maxlength="{{200}}" data-event-opts="{{[['input',[['__set_model',['$0','repairGoodNo','$event',[]],['formData']]]]]}}" value="{{formData.repairGoodNo}}" bindinput="__e"></textarea><view class="char-count"><text>{{$root.g3+"/200"}}</text></view></view><view class="form-item"><text class="form-label">文字说明 *</text><textarea class="form-textarea" placeholder="请输入文字说明" auto-height="{{true}}" maxlength="{{500}}" data-event-opts="{{[['input',[['__set_model',['$0','description','$event',[]],['formData']]]]]}}" value="{{formData.description}}" bindinput="__e"></textarea></view></block><block wx:else><view class="form-item"><text class="form-label">良品数 *</text><input class="form-input" type="number" placeholder="请输入良品数" data-event-opts="{{[['input',[['__set_model',['$0','goodQuantity','$event',[]],['formData']]]]]}}" value="{{formData.goodQuantity}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">不良品数 *</text><input class="form-input" type="number" placeholder="请输入不良品数" data-event-opts="{{[['input',[['__set_model',['$0','defectiveQuantity','$event',[]],['formData']]]]]}}" value="{{formData.defectiveQuantity}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">图片 *</text><view class="upload-container"><u-upload vue-id="{{('3614c1ce-8')+','+('3614c1ce-3')}}" fileList="{{fileList}}" maxCount="{{100}}" maxSize="{{5242880}}" width="200" height="200" previewFullImage="{{true}}" disabled="{{isUploading}}" data-event-opts="{{[['^afterRead',[['afterRead']]],['^delete',[['deletePic']]]]}}" bind:afterRead="__e" bind:delete="__e" bind:__l="__l"></u-upload><block wx:if="{{isUploading}}"><view class="upload-progress"><view class="progress-bar"><view class="progress-fill" style="{{'width:'+(uploadProgress+'%')+';'}}"></view></view><text class="progress-text">{{"上传中 "+uploadProgress+"%"}}</text></view></block></view></view></block></block></block></scroll-view></view></u-popup></view>