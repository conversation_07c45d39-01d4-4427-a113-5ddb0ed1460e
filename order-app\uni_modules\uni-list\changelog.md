## 1.2.14（2023-04-14）
- 优化 uni-list-chat 具名插槽`header` 非app端套一层元素，方便使用时通过外层元素定位实现样式修改
## 1.2.13（2023-03-03）
- uni-list-chat 新增 支持具名插槽`header`
## 1.2.12（2023-02-01）
- 新增 列表图标新增 customPrefix 属性 ，用法 [详见](https://uniapp.dcloud.net.cn/component/uniui/uni-icons.html#icons-props)
## 1.2.11（2023-01-31）
- 修复 无反馈效果呈现的bug
## 1.2.9（2022-11-22）
- 修复 uni-list-chat 在vue3下跳转报错的bug
## 1.2.8（2022-11-21）
- 修复 uni-list-chat avatar属性 值为本地路径时错误的问题
## 1.2.7（2022-11-21）
- 修复 uni-list-chat avatar属性 在腾讯云版uniCloud下错误的问题
## 1.2.6（2022-11-18）
- 修复 uni-list-chat note属性 支持：“草稿”字样功能 文本少1位的问题
## 1.2.5（2022-11-15）
- 修复 uni-list-item 的 customStyle 属性 padding值在 H5端 无效的bug
## 1.2.4（2022-11-15）
- 修复 uni-list-item 的 customStyle 属性 padding值在nvue（vue2）下无效的bug
## 1.2.3（2022-11-14）
- uni-list-chat 新增 avatar 支持 fileId
## 1.2.2（2022-11-11）
- uni-list 新增属性 render-reverse 详情参考：[https://uniapp.dcloud.net.cn/component/list.html](https://uniapp.dcloud.net.cn/component/list.html)
- uni-list-chat note属性 支持：“草稿”字样 加红显示 详情参考uni-im：[https://ext.dcloud.net.cn/plugin?name=uni-im](https://ext.dcloud.net.cn/plugin?name=uni-im)
- uni-list-item 新增属性 customStyle 支持设置padding、backgroundColor
## 1.2.1（2022-03-30）
- 删除无用文件
## 1.2.0（2021-11-23）
- 优化 组件UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-list](https://uniapp.dcloud.io/component/uniui/uni-list)
## 1.1.3（2021-08-30）
- 修复 在vue3中to属性在发行应用的时候报错的bug
## 1.1.2（2021-07-30）
- 优化 vue3下事件警告的问题
## 1.1.1（2021-07-21）
- 修复 与其他组件嵌套使用时，点击失效的Bug
## 1.1.0（2021-07-13）
- 组件兼容 vue3，如何创建vue3项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
## 1.0.17（2021-05-12）
- 新增 组件示例地址
## 1.0.16（2021-02-05）
- 优化 组件引用关系，通过uni_modules引用组件
## 1.0.15（2021-02-05）
- 调整为uni_modules目录规范
- 修复 uni-list-chat 角标显示不正常的问题
