//1.用数组保存所有注册的通知（包含名称、要执行的代码、观察者）
let notices = [];
/**
 * 2.注册通知到通知中心
 * @param {String} name 消息名称
 * @param {Function} selector 对应的通知函数
 * @param {Object} observer 观察者
 */
function add(name,selector,observer){
	notices.push({
		name: name,
		selector: selector,
		observer: observer,
	})
}
/**
 * 3.通知成功移除消息
 * @param {String} name 消息名称
 * @param {Object} observer 观察者
 */
function remove(name,observer){
	for(let i=0;i<notices.length;i++){
		if(notices[i].name==name&&notices[i].observer==observer){
			notifications.splice(i, 1);
			break;
		}
	}
}
/**
 * 4.发送通知
 * @param {String} name 消息名称
 * @param {Object} param 通知参数
 */
 function post(name,param){
	 notices.forEach(notice=>{
		 if(notice.name==name){
			 notice.selector(param)
		 }
	 })
 }
 
 module.exports = {
	 add,
	 remove,
	 post
 }