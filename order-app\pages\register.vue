<template>
  <view class="business-register-container">
    <view class="header-banner">
      <view class="banner-content">
        <view class="system-name">工单管理系统</view>
        <view class="welcome-text">创建您的账号</view>
      </view>
      <view class="logo-container">
        <image class="logo-image" :src="globalConfig.appInfo.logo" mode="widthFix"></image>
      </view>
    </view>
    
    <view class="register-form-content">
      <view class="form-title">账号注册</view>
      
      <view class="input-item flex align-center">
        <view class="iconfont icon-user icon"></view>
        <input v-model="registerForm.username" class="input" type="tel" placeholder="请输入手机号作为账号" maxlength="11" />
      </view>
      
      <view class="input-item flex align-center">
        <view class="iconfont icon-password icon"></view>
        <input v-model="registerForm.password" type="password" class="input" placeholder="请输入密码" maxlength="20" />
      </view>
      
      <view class="input-item flex align-center">
        <view class="iconfont icon-password icon"></view>
        <input v-model="registerForm.confirmPassword" type="password" class="input" placeholder="请输入确认密码" maxlength="20" />
      </view>
      
      <view class="captcha-container flex" v-if="captchaEnabled">
        <view class="input-item captcha-input flex align-center">
          <view class="iconfont icon-code icon"></view>
          <input v-model="registerForm.code" type="number" class="input" placeholder="请输入验证码" maxlength="4" />
        </view>
        <view class="register-code" @click="getCode"> 
          <image :src="codeUrl" class="register-code-img"></image>
        </view>
      </view>
      
      <view class="action-btn">
        <button @click="handleRegister()" class="register-btn">注册账号</button>
      </view>
      
      <view class="login-link text-center">
        <text class="text-grey1">已有账号？</text>
        <text @click="handleUserLogin" class="text-blue">立即登录</text>
      </view>
    </view>
    
    <view class="footer">
      <text class="footer-text">© 2023 工单管理系统 版权所有</text>
    </view>
  </view>
</template>

<script>
  import { getCodeImg, register } from '@/api/login'

  export default {
    data() {
      return {
        codeUrl: "",
        captchaEnabled: true,
        globalConfig: getApp().globalData.config,
        registerForm: {
          username: "",
          password: "",
          confirmPassword: "",
          code: "",
          uuid: ''
        }
      }
    },
    created() {
      this.getCode()
    },
    methods: {
      // 用户登录
      handleUserLogin() {
        this.$tab.navigateTo(`/pages/login`)
      },
      // 获取图形验证码
      getCode() {
        getCodeImg().then(res => {
          this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
          if (this.captchaEnabled) {
            this.codeUrl = 'data:image/gif;base64,' + res.img
            this.registerForm.uuid = res.uuid
          }
        })
      },
      // 注册方法
      async handleRegister() {
        if (this.registerForm.username === "") {
          this.$modal.msgError("请输入您的账号")
        } else if (!this.isValidPhoneNumber(this.registerForm.username)) {
          this.$modal.msgError("账号必须是有效的手机号码")
        } else if (this.registerForm.password === "") {
          this.$modal.msgError("请输入您的密码")
        } else if (this.registerForm.confirmPassword === "") {
          this.$modal.msgError("请再次输入您的密码")
        } else if (this.registerForm.password !== this.registerForm.confirmPassword) {
          this.$modal.msgError("两次输入的密码不一致")
        } else if (this.registerForm.code === "" && this.captchaEnabled) {
          this.$modal.msgError("请输入验证码")
        } else {
          this.$modal.loading("注册中，请耐心等待...")
          this.register()
        }
      },
      // 验证手机号格式
      isValidPhoneNumber(phone) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return phoneRegex.test(phone);
      },
      // 用户注册
      async register() {
        register(this.registerForm).then(res => {
          this.$modal.closeLoading()
          uni.showModal({
          	title: "系统提示",
          	content: "恭喜你，您的账号 " + this.registerForm.username + " 注册成功！",
          	success: function (res) {
          		if (res.confirm) {
                uni.redirectTo({ url: `/pages/login` });
          		}
          	}
          })
        }).catch(() => {
          if (this.captchaEnabled) {
            this.getCode()
          }
        })
      },
      // 注册成功后，处理函数
      registerSuccess(result) {
        // 设置用户信息
        this.$store.dispatch('GetInfo').then(res => {
          this.$tab.reLaunch('/pages/index')
        })
      }
    }
  }
</script>

<style lang="scss">
  page {
    background-color: #f8f9fa;
  }

  .business-register-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    
    .header-banner {
      width: 100%;
      height: 280rpx;
      background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
      position: relative;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      
      &:before {
        content: '';
        position: absolute;
        top: -10%;
        right: -10%;
        width: 300rpx;
        height: 300rpx;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
      }
      
      &:after {
        content: '';
        position: absolute;
        bottom: -15%;
        left: -5%;
        width: 250rpx;
        height: 250rpx;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.08);
      }
      
      .banner-content {
        z-index: 2;
        text-align: center;
        padding: 0 40rpx;
        
        .system-name {
          font-size: 48rpx;
          font-weight: 600;
          color: #ffffff;
          letter-spacing: 2rpx;
          margin-bottom: 20rpx;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .welcome-text {
          font-size: 28rpx;
          color: rgba(255, 255, 255, 0.9);
          letter-spacing: 4rpx;
        }
      }
      
      .logo-container {
        position: absolute;
        top: 30rpx;
        left: 30rpx;
        z-index: 3;
        
        .logo-image {
          width: 80rpx;
          height: 80rpx;
          border-radius: 8rpx;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
      }
    }

    .register-form-content {
      width: 85%;
      margin: 40rpx auto;
      padding: 40rpx;
      background-color: #ffffff;
      border-radius: 8rpx;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      
      .form-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #2c3e50;
        text-align: left;
        margin-bottom: 40rpx;
        padding-bottom: 20rpx;
        border-bottom: 1px solid #ebeef5;
      }

      .input-item {
        margin: 30rpx auto;
        height: 90rpx;
        border: 1px solid #dcdfe6;
        border-radius: 4rpx;
        background-color: #ffffff;
        
        .icon {
          font-size: 40rpx;
          margin-left: 20rpx;
          color: #606266;
        }

        .input {
          width: 100%;
          font-size: 28rpx;
          line-height: 90rpx;
          text-align: left;
          padding-left: 20rpx;
          color: #2c3e50;
        }
      }
      
      .captcha-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 30rpx auto;
        
        .captcha-input {
          width: 60%;
          margin: 0;
        }
        
        .register-code {
          width: 35%;
          height: 90rpx;
          border: 1px solid #dcdfe6;
          border-radius: 4rpx;
          overflow: hidden;
          
          .register-code-img {
            width: 100%;
            height: 100%;
          }
        }
      }

      .action-btn {
        margin-top: 50rpx;
        
        .register-btn {
          height: 90rpx;
          line-height: 90rpx;
          background-color: #1e3a8a;
          color: #ffffff;
          font-size: 32rpx;
          border-radius: 4rpx;
          font-weight: 500;
          letter-spacing: 2rpx;
          width: 100%;
          border: none;
          
          &::after {
            border: none;
          }
        }
      }
      
      .login-link {
        margin-top: 30rpx;
        font-size: 26rpx;
        
        .text-grey1 {
          color: #606266;
        }
        
        .text-blue {
          color: #1e3a8a;
          margin-left: 10rpx;
        }
      }
    }
    
    .footer {
      margin-top: auto;
      padding: 40rpx 0;
      text-align: center;
      
      .footer-text {
        font-size: 24rpx;
        color: #909399;
      }
    }
  }
</style>
