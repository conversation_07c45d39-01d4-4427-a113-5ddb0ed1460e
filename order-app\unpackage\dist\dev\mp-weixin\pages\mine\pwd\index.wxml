<view class="pwd-retrieve-container"><uni-forms class="vue-ref" vue-id="494b9529-1" value="{{user}}" labelWidth="80px" data-ref="form" bind:__l="__l" vue-slots="{{['default']}}"><uni-forms-item vue-id="{{('494b9529-2')+','+('494b9529-1')}}" name="oldPassword" label="旧密码" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('494b9529-3')+','+('494b9529-2')}}" type="password" placeholder="请输入旧密码" value="{{user.oldPassword}}" data-event-opts="{{[['^input',[['__set_model',['$0','oldPassword','$event',[]],['user']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('494b9529-4')+','+('494b9529-1')}}" name="newPassword" label="新密码" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('494b9529-5')+','+('494b9529-4')}}" type="password" placeholder="请输入新密码" value="{{user.newPassword}}" data-event-opts="{{[['^input',[['__set_model',['$0','newPassword','$event',[]],['user']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('494b9529-6')+','+('494b9529-1')}}" name="confirmPassword" label="确认密码" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('494b9529-7')+','+('494b9529-6')}}" type="password" placeholder="请确认新密码" value="{{user.confirmPassword}}" data-event-opts="{{[['^input',[['__set_model',['$0','confirmPassword','$event',[]],['user']]]]]}}" bind:__l="__l"></uni-easyinput></uni-forms-item><button type="primary" data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">提交</button></uni-forms></view>