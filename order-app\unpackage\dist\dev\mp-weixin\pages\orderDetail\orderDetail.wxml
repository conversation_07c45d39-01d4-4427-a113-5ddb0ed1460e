<view class="order-detail-container"><block wx:if="{{isRepairman}}"><view class="repair-button-container"><button data-event-opts="{{[['tap',[['showMaintenanceForm',['$event']]]]]}}" class="repair-btn" bindtap="__e">添加维修记录</button></view></block><view class="detail-card"><view class="card-header"><text class="card-title">工单信息</text><text class="{{['order-status','status-'+(orderDetail&&orderDetail.status)]}}">{{''+$root.m0+''}}</text></view><view class="info-list"><view class="info-item"><text class="label">工单编号</text><text class="value">{{orderDetail&&orderDetail.workOrderNo}}</text></view><view class="info-item"><text class="label">产品名称</text><text class="value">{{orderDetail&&orderDetail.productName}}</text></view><view class="info-item"><text class="label">下单客户</text><text class="value">{{orderDetail&&orderDetail.customerName}}</text></view><view class="info-item"><text class="label">型号</text><text class="value">{{orderDetail&&orderDetail.modelType}}</text></view><view class="info-item"><text class="label">产品编号版本</text><text class="value">{{orderDetail&&orderDetail.orderProductNumberVersion}}</text></view><view class="info-item"><text class="label">计划数量</text><text class="value">{{orderDetail&&orderDetail.totalPlannedQuantity}}</text></view><view class="info-item"><text class="label">计划开始时间</text><text class="value">{{orderDetail&&orderDetail.overallPlannedStartTime}}</text></view><view class="info-item"><text class="label">计划结束时间</text><text class="value">{{orderDetail&&orderDetail.overallPlannedEndTime}}</text></view></view></view><block wx:if="{{orderDetail&&orderDetail.packingListImagePath}}"><view class="detail-card"><view class="card-header"><text class="card-title">装箱单图片</text></view><view class="image-list"><block wx:for="{{imageList}}" wx:for-item="image" wx:for-index="imageIndex" wx:key="imageIndex"><image class="image-item" src="{{image}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewImage',[imageIndex]]]]]}}" bindtap="__e"></image></block></view></view></block><view class="detail-card"><view class="card-header"><text class="card-title">任务列表</text></view><view class="task-list"><block wx:for="{{$root.l1}}" wx:for-item="task" wx:for-index="taskIndex" wx:key="taskIndex"><view class="task-item"><view data-event-opts="{{[['tap',[['toggleTask',['$0'],[[['orderItemLists','',taskIndex]]]]]]]}}" class="task-main" bindtap="__e"><view class="task-header"><view class="task-header-left"><u-icon class="expand-icon" vue-id="{{'0e26bf40-1-'+taskIndex}}" name="{{expandedTasks[task.$orig.workOrderItemId]?'arrow-up':'arrow-down'}}" size="28" color="#2c3e50" bind:__l="__l"></u-icon><text class="task-name">{{task.$orig.taskName}}</text></view><text class="{{['task-status','status-'+task.$orig.itemStatus]}}">{{''+task.m1+''}}</text></view><view class="task-info"><view class="info-row"><text class="info-label">计划数量：</text><text class="info-value">{{task.$orig.plannedQuantity}}</text></view><block wx:if="{{!task.m2}}"><view class="info-row"><text class="info-label">良品数：</text><text class="info-value">{{task.$orig.itemGoodQuantity}}</text></view><view class="info-row"><text class="info-label">不良品数：</text><text class="info-value">{{task.$orig.itemDefectiveQuantity}}</text></view></block></view></view><view hidden="{{!(expandedTasks[task.$orig.workOrderItemId])}}" class="task-details"><view class="assignment-info"><view class="assignment-title">指派情况</view><view class="assignment-list"><block wx:for="{{task.l0}}" wx:for-item="assignment" wx:for-index="assignmentIndex" wx:key="assignmentIndex"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({assignment:assignment.$orig,task:task.$orig})}}" class="assignment-item" bindtap="__e"><view class="assignment-header"><text class="assignee-name">{{assignment.$orig.nickName||'未知用户'}}</text><view class="assignment-actions"><text class="{{['assignment-status','status-'+(assignment.$orig.assignmentStatus||'1')]}}">{{''+assignment.m3+''}}</text><block wx:if="{{canDeleteAssignment}}"><view class="delete-btn" data-assignment-id="{{assignment.$orig.assignmentId}}" data-task-id="{{task.$orig.workOrderItemId}}" data-event-opts="{{[['tap',[['handleDeleteAssignment',['$event']]]]]}}" catchtap="__e"><u-icon vue-id="{{'0e26bf40-2-'+taskIndex+'-'+assignmentIndex}}" name="trash" size="32" color="#909399" bind:__l="__l"></u-icon></view></block></view></view><view class="assignment-details"><view class="detail-row"><text class="detail-label">指派数量：</text><text class="detail-value">{{assignment.$orig.assignedQuantity||0}}</text></view><block wx:if="{{task.m4}}"><view class="detail-row"><text class="detail-label">地址：</text><text class="detail-value">{{assignment.$orig.locationAddress||'未填写'}}</text></view></block><block wx:if="{{task.m5}}"><view class="detail-row"><text class="detail-label">良品数：</text><text class="detail-value">{{assignment.$orig.reportedGoodQuantity||0}}</text></view><view class="detail-row"><text class="detail-label">不良品数：</text><text class="detail-value">{{assignment.$orig.reportedDefectiveQuantity||0}}</text></view></block><block wx:if="{{task.m6}}"><block wx:if="{{assignment.$orig.description}}"><view class="detail-row"><text class="detail-label">说明：</text><text class="detail-value description-text">{{assignment.$orig.description}}</text></view></block></block></view></view></block><block wx:if="{{task.g0}}"><view class="no-assignment"><text>暂无指派信息</text></view></block></view></view><block wx:if="{{task.$orig.itemStatus==='0'&&canAssignTask}}"><view class="task-footer"><button data-event-opts="{{[['tap',[['showUserPicker',['$0'],[[['orderItemLists','',taskIndex]]]]]]]}}" class="assign-btn" catchtap="__e">指派</button></view></block></view></view></block></view></view><u-popup vue-id="0e26bf40-3" mode="bottom" show="{{showUserPickerFlag}}" border-radius="24" safe-area-inset-bottom="{{true}}" data-event-opts="{{[['^close',[['closeUserPicker']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="user-picker-container"><view class="user-picker-header"><text class="user-picker-title">选择执行人</text><view class="user-picker-actions"><u-button style="margin-right:20rpx;" vue-id="{{('0e26bf40-4')+','+('0e26bf40-3')}}" plain="{{true}}" size="mini" data-event-opts="{{[['^click',[['closeUserPicker']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">取消</u-button><u-button vue-id="{{('0e26bf40-5')+','+('0e26bf40-3')}}" type="primary" size="mini" data-event-opts="{{[['^click',[['confirmUser']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">确认</u-button></view></view><view class="user-list"><block wx:for="{{userList}}" wx:for-item="user" wx:for-index="userIndex" wx:key="userIndex"><view data-event-opts="{{[['tap',[['selectUser',['$0'],[[['userList','',userIndex]]]]]]]}}" class="{{['user-item',(selectedUser&&selectedUser.userId===user.userId)?'user-item-selected':'']}}" bindtap="__e"><text class="user-name">{{user.nickName}}</text><u-icon vue-id="{{('0e26bf40-6-'+userIndex)+','+('0e26bf40-3')}}" name="{{selectedUser&&selectedUser.userId===user.userId?'checkmark-circle':''}}" color="{{selectedUser&&selectedUser.userId===user.userId?'#1e3a8a':'#8c9fba'}}" size="40" bind:__l="__l"></u-icon></view></block></view><block wx:if="{{selectedUser}}"><view class="quantity-input"><view class="input-label">计划数量</view><view class="input-wrapper"><input class="quantity-input-field" type="number" placeholder="请输入计划数量" data-event-opts="{{[['input',[['__set_model',['','assignQuantity','$event',[]]]]]]}}" value="{{assignQuantity}}" bindinput="__e"/><text class="quantity-tip">{{"最大可分配数量："+(currentTask?currentTask.plannedQuantity:0)}}</text></view></view></block></view></u-popup><u-popup vue-id="0e26bf40-7" mode="bottom" show="{{showAssignmentDetailFlag}}" border-radius="24" safe-area-inset-bottom="{{true}}" data-event-opts="{{[['^close',[['closeAssignmentDetail']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="assignment-detail-container"><view class="detail-header"><text class="detail-title">指派详情</text><view class="detail-actions"><u-button vue-id="{{('0e26bf40-8')+','+('0e26bf40-7')}}" plain="{{true}}" size="mini" data-event-opts="{{[['^click',[['closeAssignmentDetail']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">关闭</u-button></view></view><scroll-view class="detail-content" scroll-y="{{true}}"><view class="detail-item"><text class="detail-label">执行人</text><text class="detail-value">{{currentAssignment.nickName||'未知用户'}}</text></view><block wx:if="{{isShippingAssignment}}"><view class="detail-item"><text class="detail-label">地址</text><text class="detail-value">{{currentAssignment.locationAddress||'未填写'}}</text></view></block><block wx:if="{{!isMaintenanceAssignment&&!isShippingAssignment}}"><view class="detail-item"><text class="detail-label">良品数</text><text class="detail-value">{{currentAssignment.goodQuantity||0}}</text></view><view class="detail-item"><text class="detail-label">不良品数</text><text class="detail-value">{{currentAssignment.defectiveQuantity||0}}</text></view></block><block wx:if="{{currentAssignment.pic}}"><view class="detail-item"><text class="detail-label">{{isMaintenanceAssignment?'维修图片':'图片'}}</text><view class="image-list"><block wx:for="{{assignmentImageList}}" wx:for-item="image" wx:for-index="detailImageIndex" wx:key="detailImageIndex"><image class="image-item" src="{{image}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewAssignmentImage',[detailImageIndex]]]]]}}" bindtap="__e"></image></block></view></view></block><block wx:if="{{isMaintenanceAssignment&&currentAssignment.repairGoodNo}}"><view class="detail-item"><text class="detail-label">维修产品编号</text><text class="detail-value description-text">{{currentAssignment.repairGoodNo}}</text></view></block><block wx:if="{{isMaintenanceAssignment&&currentAssignment.createTime}}"><view class="detail-item"><text class="detail-label">维修时间</text><text class="detail-value">{{currentAssignment.createTime}}</text></view></block><block wx:if="{{isMaintenanceAssignment&&currentAssignment.description}}"><view class="detail-item"><text class="detail-label">维修说明</text><text class="detail-value description-text">{{currentAssignment.description}}</text></view></block></scroll-view></view></u-popup><u-popup vue-id="0e26bf40-9" mode="bottom" show="{{showMaintenanceFormFlag}}" border-radius="24" safe-area-inset-bottom="{{true}}" data-event-opts="{{[['^close',[['closeMaintenanceForm']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="maintenance-form-container"><view class="form-header"><text class="form-title">添加维修记录</text><view class="form-actions"><u-button style="margin-right:20rpx;" vue-id="{{('0e26bf40-10')+','+('0e26bf40-9')}}" plain="{{true}}" size="mini" data-event-opts="{{[['^click',[['closeMaintenanceForm']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">取消</u-button><u-button vue-id="{{('0e26bf40-11')+','+('0e26bf40-9')}}" type="primary" size="mini" disabled="{{isUploading}}" data-event-opts="{{[['^click',[['submitMaintenanceRecord']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">{{''+(isUploading?'上传中...':'确认')+''}}</u-button></view></view><scroll-view class="form-content" scroll-y="{{true}}"><view class="form-item"><text class="form-label">维修图片 *</text><view class="upload-container"><u-upload vue-id="{{('0e26bf40-12')+','+('0e26bf40-9')}}" fileList="{{maintenanceFileList}}" maxCount="{{9}}" maxSize="{{5242880}}" width="200" height="200" previewFullImage="{{true}}" disabled="{{isUploading}}" data-event-opts="{{[['^afterRead',[['afterMaintenanceRead']]],['^delete',[['deleteMaintenancePic']]]]}}" bind:afterRead="__e" bind:delete="__e" bind:__l="__l"></u-upload><block wx:if="{{isUploading}}"><view class="upload-progress"><view class="progress-bar"><view class="progress-fill" style="{{'width:'+(uploadProgress+'%')+';'}}"></view></view><text class="progress-text">{{"上传中 "+uploadProgress+"%"}}</text></view></block></view></view><view class="form-item"><text class="form-label">维修产品编号</text><textarea class="form-textarea" placeholder="请输入维修产品编号" auto-height="{{true}}" maxlength="{{500}}" data-event-opts="{{[['input',[['__set_model',['$0','repairGoodNo','$event',[]],['maintenanceFormData']]]]]}}" value="{{maintenanceFormData.repairGoodNo}}" bindinput="__e"></textarea><view class="char-count"><text>{{$root.g1+"/500"}}</text></view></view><view class="form-item"><text class="form-label">维修说明 *</text><textarea class="form-textarea" placeholder="请输入维修说明" auto-height="{{true}}" maxlength="{{500}}" data-event-opts="{{[['input',[['__set_model',['$0','description','$event',[]],['maintenanceFormData']]]]]}}" value="{{maintenanceFormData.description}}" bindinput="__e"></textarea></view></scroll-view></view></u-popup></view>